---
description: 
globs: 
alwaysApply: false
---
# Playwright C# .NET Testing Guidelines

## General Principles
- **Test User-Visible Behavior:** Focus tests on how users interact with your application, not on internal implementation details.
- **Isolate Tests:** Ensure tests are independent of each other to prevent cascading failures and ensure predictable results.
- **Avoid Testing Third-Party Dependencies:** Mock or stub external services and APIs to isolate your application's behavior.

## Code Organization and Structure

### Directory Structure
```
Tests/
├── E2E/                    # End-to-end tests
├── Integration/            # Integration tests
├── Unit/                   # Unit tests (if applicable)  
├── PageObjects/           # Page Object Models
├── Fixtures/              # Test fixtures and utilities
├── Support/               # Helper classes and utilities
└── Configuration/         # Test configuration files
```

### File Naming Conventions
- Use `Tests.cs` suffix for test files (e.g., `LoginTests.cs`)
- Use `PageObject.cs` or `Page.cs` suffix for page objects (e.g., `LoginPage.cs`)
- Group related tests in the same class using `[TestClass]` attribute

### Class Structure
```csharp
[TestClass]
public class LoginTests : PageTest
{
    private LoginPage _loginPage;

    [TestInitialize]
    public async Task Setup()
    {
        _loginPage = new LoginPage(Page);
        await _loginPage.GotoAsync();
    }

    [TestMethod]
    public async Task ShouldLoginWithValidCredentials()
    {
        // Test implementation
    }

    [TestCleanup]
    public async Task Cleanup()
    {
        // Cleanup logic if needed
    }
}
```

## Common Patterns and Anti-patterns

### Design Patterns

#### Page Object Model (POM)
```csharp
public class LoginPage
{
    private readonly IPage _page;

    public LoginPage(IPage page)
    {
        _page = page;
    }

    private ILocator UsernameInput => _page.Locator("#username");
    private ILocator PasswordInput => _page.Locator("#password");
    private ILocator LoginButton => _page.Locator("#login-button");
    private ILocator ErrorMessage => _page.Locator("#error-message");

    public async Task GotoAsync()
    {
        await _page.GotoAsync("/login");
    }

    public async Task LoginAsync(string username, string password)
    {
        await UsernameInput.FillAsync(username);
        await PasswordInput.FillAsync(password);
        await LoginButton.ClickAsync();
    }

    public async Task<string> GetErrorMessageAsync()
    {
        return await ErrorMessage.TextContentAsync() ?? string.Empty;
    }
}
```

#### Base Test Class Pattern
```csharp
public abstract class BaseTest : PageTest
{
    protected IConfiguration Configuration { get; private set; }
    protected string BaseUrl { get; private set; }

    [TestInitialize]
    public virtual async Task BaseSetup()
    {
        Configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.test.json")
            .Build();
        
        BaseUrl = Configuration["BaseUrl"] ?? "https://localhost:5001";
    }

    protected async Task<IResponse> WaitForApiCallAsync(string endpoint)
    {
        return await Page.WaitForResponseAsync(response => 
            response.Url.Contains(endpoint) && response.Status == 200);
    }
}
```

### Recommended Approaches
- Use `appsettings.test.json` or environment variables for configuration instead of hardcoding URLs
- Utilize `Expect()` methods for assertions (e.g., `await Expect(Page.Locator("#success")).ToBeVisibleAsync()`)
- Use auto-waiting features for improved stability
- Implement retry logic for flaky tests using `[Retry]` attributes

### Anti-patterns
- Hardcoding URLs and configuration values
- Using brittle selectors (e.g., XPath based on DOM structure)
- Writing tests that depend on each other
- Not using async/await properly in C#

### State Management
```csharp
[TestClass]
public class ShoppingCartTests : PageTest
{
    [TestInitialize]
    public async Task Setup()
    {
        // Reset application state before each test
        await Page.GotoAsync("/api/test/reset-database");
        await SeedTestDataAsync();
    }

    private async Task SeedTestDataAsync()
    {
        // Seed necessary test data via API calls
        await Page.Request.PostAsync("/api/test/seed-products", new() 
        { 
            DataObject = new { Products = GetTestProducts() } 
        });
    }
}
```

### Error Handling
```csharp
[TestMethod]
public async Task ShouldHandleNetworkErrorGracefully()
{
    try
    {
        await Page.RouteAsync("**/api/login", route => 
            route.AbortAsync("internetdisconnected"));
        
        await _loginPage.LoginAsync("user", "pass");
        
        // Use soft assertions for non-critical checks
        await Expect.Soft(_loginPage.ErrorMessage).ToBeVisibleAsync();
        await Expect.Soft(_loginPage.ErrorMessage).ToContainTextAsync("Network error");
    }
    catch (PlaywrightException ex)
    {
        Assert.Fail($"Test failed with Playwright exception: {ex.Message}");
    }
}
```

## Performance Considerations

### Optimization Techniques
```csharp
// Run tests in parallel
[assembly: Parallelize(Workers = 4, Scope = ExecutionScope.ClassLevel)]

// Reuse browser context for faster execution
public class OptimizedTests : PageTest
{
    private static IBrowserContext? _sharedContext;

    [ClassInitialize]
    public static async Task ClassSetup(TestContext context)
    {
        var playwright = await Playwright.CreateAsync();
        var browser = await playwright.Chromium.LaunchAsync();
        _sharedContext = await browser.NewContextAsync();
    }

    [ClassCleanup]
    public static async Task ClassCleanup()
    {
        await _sharedContext?.CloseAsync();
    }
}
```

### Memory Management
```csharp
[TestCleanup]
public async Task Cleanup()
{
    // Properly dispose of resources
    await Page?.CloseAsync();
    await Context?.CloseAsync();
}
```

## Security Best Practices

### Secure Configuration
```csharp
public class SecureTestBase : PageTest
{
    protected string GetSecureConfig(string key)
    {
        // Use secure configuration management
        return Environment.GetEnvironmentVariable(key) 
            ?? Configuration[key] 
            ?? throw new InvalidOperationException($"Missing required config: {key}");
    }

    protected async Task AuthenticateAsync(string role = "user")
    {
        var token = await GetAuthTokenAsync(role);
        await Context.SetExtraHTTPHeadersAsync(new Dictionary<string, string>
        {
            ["Authorization"] = $"Bearer {token}"
        });
    }
}
```

### Input Validation Testing
```csharp
[TestMethod]
[DataRow("", "", "Username and password are required")]
[DataRow("user", "", "Password is required")]
[DataRow("", "pass", "Username is required")]
[DataRow("user'--", "pass", "Invalid characters in username")]
public async Task ShouldValidateLoginInput(string username, string password, string expectedError)
{
    await _loginPage.LoginAsync(username, password);
    await Expect(_loginPage.ErrorMessage).ToContainTextAsync(expectedError);
}
```

## Testing Approaches

### API Mocking
```csharp
[TestMethod]
public async Task ShouldHandleMockedApiResponse()
{
    // Mock API response
    await Page.RouteAsync("**/api/users", async route =>
    {
        var mockResponse = new
        {
            users = new[]
            {
                new { id = 1, name = "Test User", email = "<EMAIL>" }
            }
        };
        
        await route.FulfillAsync(new()
        {
            Status = 200,
            ContentType = "application/json",
            Body = JsonSerializer.Serialize(mockResponse)
        });
    });

    await _usersPage.LoadUsersAsync();
    await Expect(_usersPage.FirstUser).ToContainTextAsync("Test User");
}
```

### Database Testing with Entity Framework
```csharp
[TestClass]
public class DatabaseIntegrationTests : PageTest
{
    private TestDbContext _dbContext;

    [TestInitialize]
    public async Task Setup()
    {
        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        
        _dbContext = new TestDbContext(options);
        await _dbContext.Database.EnsureCreatedAsync();
        await SeedDatabaseAsync();
    }

    private async Task SeedDatabaseAsync()
    {
        _dbContext.Users.Add(new User { Name = "Test User", Email = "<EMAIL>" });
        await _dbContext.SaveChangesAsync();
    }

    [TestCleanup]
    public async Task Cleanup()
    {
        await _dbContext.DisposeAsync();
    }
}
```

## Common Pitfalls and Gotchas

### Frequent Mistakes
- Not using `await` with async Playwright methods
- Using string concatenation instead of interpolation for selectors
- Not handling `null` returns from Playwright methods
- Writing flaky tests due to timing issues

### Debugging Strategies
```csharp
[TestMethod]
public async Task DebuggingExample()
{
    // Enable tracing for debugging
    await Context.Tracing.StartAsync(new()
    {
        Screenshots = true,
        Snapshots = true,
        Sources = true
    });

    try
    {
        // Test steps
        await _loginPage.LoginAsync("user", "pass");
        
        // Pause execution for manual inspection
        await Page.PauseAsync();
    }
    finally
    {
        await Context.Tracing.StopAsync(new()
        {
            Path = "trace.zip"
        });
    }
}
```

### Version-Specific Configurations
```json
// playwright.config.json
{
  "projects": [
    {
      "name": "chromium",
      "use": {
        "browserName": "chromium",
        "viewport": { "width": 1280, "height": 720 },
        "ignoreHTTPSErrors": true
      }
    },
    {
      "name": "firefox",
      "use": {
        "browserName": "firefox"
      }
    },
    {
      "name": "webkit",
      "use": {
        "browserName": "webkit"
      }
    }
  ],
  "use": {
    "baseURL": "https://localhost:5001",
    "trace": "on-first-retry",
    "video": "retain-on-failure",
    "screenshot": "only-on-failure"
  }
}
```

## Tooling and Environment

### Recommended NuGet Packages
```xml
<PackageReference Include="Microsoft.Playwright" Version="1.40.0" />
<PackageReference Include="Microsoft.Playwright.MSTest" Version="1.40.0" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.0" />
```

### CI/CD Integration (GitHub Actions)
```yaml
name: Playwright Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '7.0'
    - name: Restore dependencies
      run: dotnet restore
    - name: Install Playwright browsers
      run: pwsh bin/Debug/net7.0/playwright.ps1 install
    - name: Run Playwright tests
      run: dotnet test --configuration Release --logger trx --results-directory TestResults
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: TestResults/
```

## Specific Best Practices & Details

### Stable Selectors
```csharp
// Prefer data attributes over CSS classes
private ILocator LoginButton => _page.Locator("[data-testid='login-button']");

// Use role-based selectors when appropriate
private ILocator SubmitButton => _page.GetByRole(AriaRole.Button, new() { Name = "Submit" });

// Use text-based selectors for better maintainability
private ILocator WelcomeMessage => _page.GetByText("Welcome back!");
```

### Web-First Assertions
```csharp
// Good: Uses built-in waiting and retrying
await Expect(Page.Locator("#success-message")).ToBeVisibleAsync();
await Expect(Page.Locator("#user-count")).ToHaveTextAsync("5 users");

// Better: Use more specific assertions
await Expect(Page.Locator("#price")).ToHaveTextAsync("$19.99");
await Expect(Page.GetByRole(AriaRole.Button)).ToBeEnabledAsync();
```

### Configuration Management
```csharp
public class TestConfiguration
{
    public string BaseUrl { get; set; } = "https://localhost:5001";
    public string ApiUrl { get; set; } = "https://localhost:5001/api";
    public int TimeoutMs { get; set; } = 30000;
    public bool HeadlessMode { get; set; } = true;
    public string BrowserName { get; set; } = "chromium";
}

// Usage in test class
protected TestConfiguration Config { get; private set; }

[TestInitialize]
public async Task Setup()
{
    Config = Configuration.GetSection("TestSettings").Get<TestConfiguration>()
        ?? new TestConfiguration();
    
    await Page.GotoAsync(Config.BaseUrl);
}
```

## Additional Notes
- Regularly review and update your test suite to reflect changes in your application
- Document your page objects and complex test logic using XML documentation comments
- Use consistent naming conventions following C# conventions (PascalCase for public members)
- Implement proper logging using ILogger interface for better debugging
- Consider using Factory pattern for creating page objects with dependency injection
- Use `ConfigureAwait(false)` in library code but not in test code when using async/await

