# EInvoice Settings Implementation Detail

## 1. Project Structure (EInvoiceSetting Files Only)

```
src/
├── Modules/
│   └── Payment/
│       ├── KvFnB.Modules.Payment.Domain/
│       │   ├── Models/
│       │   │   └── EInvoiceSetting.cs
│       │   ├── Enums/
│       │   │   ├── EInvoiceStatus.cs
│       │   │   └── EInvoicePartnerType.cs
│       │   ├── Repositories/
│       │   │   └── IEInvoiceSettingRepository.cs
│       │   ├── Specifications/
│       │   │   └── EInvoiceSetting/
│       │   │       ├── EInvoiceSettingByIdSpec.cs
│       │   │       ├── EInvoiceSettingByRetailerSpec.cs
│       │   │       └── EInvoiceSettingByRetailerAndBranchSpec.cs
│       │   └── Exceptions/
│       │       ├── EInvoiceSettingAlreadyExistsException.cs
│       │       ├── EInvoiceSettingNotFoundException.cs
│       │       └── InvalidEInvoiceConfigurationException.cs
│       ├── KvFnB.Modules.Payment.Application/
│       │   ├── Contracts/
│       │   │   ├── EInvoiceConfigDto.cs
│       │   │   ├── EInvoiceSettingDto.cs
│       │   │   └── EInvoiceSettingExtensions.cs
│       │   └── UseCases/EInvoiceSettingUseCase/
│       │       ├── CreateEInvoiceSetting/
│       │       │   ├── CreateEInvoiceSettingRequest.cs
│       │       │   ├── CreateEInvoiceSettingResponse.cs
│       │       │   ├── CreateEInvoiceSettingValidator.cs
│       │       │   └── CreateEInvoiceSettingUseCase.cs
│       │       ├── UpdateEInvoiceSettingStatus/
│       │       │   ├── UpdateEInvoiceSettingStatusRequest.cs
│       │       │   ├── UpdateEInvoiceSettingStatusResponse.cs
│       │       │   ├── UpdateEInvoiceSettingStatusValidator.cs
│       │       │   └── UpdateEInvoiceSettingStatusUseCase.cs
│       │       ├── UpdateEInvoiceSetting/
│       │       │   ├── UpdateEInvoiceSettingRequest.cs
│       │       │   ├── UpdateEInvoiceSettingResponse.cs
│       │       │   ├── UpdateEInvoiceSettingValidator.cs
│       │       │   └── UpdateEInvoiceSettingUseCase.cs
│       │       ├── GetEInvoiceSettingById/
│       │       │   ├── GetEInvoiceSettingByIdRequest.cs
│       │       │   ├── GetEInvoiceSettingByIdResponse.cs
│       │       │   ├── GetEInvoiceSettingByIdValidator.cs
│       │       │   └── GetEInvoiceSettingByIdUseCase.cs
│       │       ├── GetEInvoiceSettingsByRetailer/
│       │       │   ├── GetEInvoiceSettingsByRetailerRequest.cs
│       │       │   ├── GetEInvoiceSettingsByRetailerResponse.cs
│       │       │   ├── GetEInvoiceSettingsByRetailerValidator.cs
│       │       │   └── GetEInvoiceSettingsByRetailerUseCase.cs
│       │       └── GetEInvoiceSettingByRetailerAndBranch/
│       │           ├── GetEInvoiceSettingByRetailerAndBranchRequest.cs
│       │           ├── GetEInvoiceSettingByRetailerAndBranchResponse.cs
│       │           ├── GetEInvoiceSettingByRetailerAndBranchValidator.cs
│       │           └── GetEInvoiceSettingByRetailerAndBranchUseCase.cs
│       ├── KvFnB.Modules.Payment.Infrastructure/
│       │   └── Persistence/
│       │       └── EInvoiceSettingRepository.cs
│       └── Presentation/
│           └── Restful/
│               └── KvFnB.Modules.Payment.Restful/
│                   └── EInvoiceController.cs (update existing)
├── Shared/
│   └── Persistence/
│       └── ShardingDb/
│           └── EntityTypeConfigurations/
│               └── EInvoiceSettingEntityTypeConfiguration.cs
```

## 2. Key Implementation Classes

### 2.1 Domain Models

**EInvoiceSetting.cs**
```csharp
using KvFnB.Core.Domain;
using KvFnB.Modules.Payment.Domain.Enums;

namespace KvFnB.Modules.Payment.Domain.Models;

public class EInvoiceSetting : AggregateRoot<long>, IAuditableEntity
{
    public int BranchId { get; private set; }
    public EInvoiceStatus Status { get; private set; }
    public string? EInvoiceConfig { get; private set; }
    public EInvoicePartnerType PartnerType { get; private set; }
    public long CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public long? ModifiedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }

    protected EInvoiceSetting() { }

    public static EInvoiceSetting Create(int branchId, EInvoicePartnerType partnerType, string? eInvoiceConfig = null)
    {
        if (branchId <= 0) throw new ArgumentException("BranchId must be greater than 0", nameof(branchId));

        return new EInvoiceSetting
        {
            BranchId = branchId,
            PartnerType = partnerType ?? throw new ArgumentNullException(nameof(partnerType)),
            EInvoiceConfig = eInvoiceConfig,
            Status = EInvoiceStatus.Active
        };
    }

    public void UpdateStatus(EInvoiceStatus status)
    {
        Status = status ?? throw new ArgumentNullException(nameof(status));
    }

    public void Update(EInvoicePartnerType partnerType, string? eInvoiceConfig)
    {
        PartnerType = partnerType ?? throw new ArgumentNullException(nameof(partnerType));
        EInvoiceConfig = eInvoiceConfig;
    }
}
```

### 2.2 Application DTOs and Contracts

**EInvoiceConfigDto.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.Contracts;

public record EInvoiceConfigDto(
    [property: JsonPropertyName("is_push_from_pos")] bool IsPushFromPos,
    [property: Required, JsonPropertyName("default_template_id")] string DefaultTemplateId,
    [property: Required, JsonPropertyName("url_search")] string UrlSearch,
    [property: JsonPropertyName("is_push_invoice_item_note")] bool IsPushInvoiceItemNote,
    [property: Required, JsonPropertyName("tax_code")] string TaxCode
);
```

**EInvoiceSettingDto.cs**
```csharp
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.Contracts;

public record EInvoiceSettingDto(
    [property: JsonPropertyName("id")] long Id,
    [property: JsonPropertyName("tenant_id")] int TenantId,
    [property: JsonPropertyName("branch_id")] int BranchId,
    [property: JsonPropertyName("status")] int Status,
    [property: JsonPropertyName("status_name")] string StatusName,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig,
    [property: JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("partner_type_name")] string PartnerTypeName,
    [property: JsonPropertyName("created_by")] long CreatedBy,
    [property: JsonPropertyName("created_at")] DateTime CreatedAt,
    [property: JsonPropertyName("modified_by")] long? ModifiedBy,
    [property: JsonPropertyName("modified_at")] DateTime? ModifiedAt
);
```

### 2.3 Use Case Requests

**CreateEInvoiceSettingRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.CreateEInvoiceSetting;

public record CreateEInvoiceSettingRequest(
    [property: Required, Range(1, int.MaxValue), JsonPropertyName("branch_id")] int BranchId,
    [property: Required, Range(1, 4), JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig
) : IRequest<CreateEInvoiceSettingResponse>;
```

**UpdateEInvoiceSettingStatusRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.UpdateEInvoiceSettingStatus;

public record UpdateEInvoiceSettingStatusRequest(
    [property: Required, JsonPropertyName("id")] long Id,
    [property: Required, Range(0, 1), JsonPropertyName("status")] int Status
) : IRequest<UpdateEInvoiceSettingStatusResponse>;
```

### 2.4 Application Use Case Request and Response Models

**CreateEInvoiceSetting Use Case Models:**

**CreateEInvoiceSettingRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.CreateEInvoiceSetting;

public record CreateEInvoiceSettingRequest(
    [property: Required, Range(1, int.MaxValue), JsonPropertyName("branch_id")] int BranchId,
    [property: Required, Range(1, 4), JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig
) : IRequest<CreateEInvoiceSettingResponse>;
```

**CreateEInvoiceSettingResponse.cs**
```csharp
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.CreateEInvoiceSetting;

public record CreateEInvoiceSettingResponse(
    [property: JsonPropertyName("id")] long Id,
    [property: JsonPropertyName("tenant_id")] int TenantId,
    [property: JsonPropertyName("branch_id")] int BranchId,
    [property: JsonPropertyName("status")] int Status,
    [property: JsonPropertyName("status_name")] string StatusName,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig,
    [property: JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("partner_type_name")] string PartnerTypeName,
    [property: JsonPropertyName("created_by")] long CreatedBy,
    [property: JsonPropertyName("created_at")] DateTime CreatedAt,
    [property: JsonPropertyName("modified_by")] long? ModifiedBy,
    [property: JsonPropertyName("modified_at")] DateTime? ModifiedAt
);
```

**UpdateEInvoiceSetting Use Case Models:**

**UpdateEInvoiceSettingRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.UpdateEInvoiceSetting;

public record UpdateEInvoiceSettingRequest(
    [property: Required, JsonPropertyName("id")] long Id,
    [property: Required, Range(1, 4), JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig
) : IRequest<UpdateEInvoiceSettingResponse>;
```

**UpdateEInvoiceSettingResponse.cs**
```csharp
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.UpdateEInvoiceSetting;

public record UpdateEInvoiceSettingResponse(
    [property: JsonPropertyName("id")] long Id,
    [property: JsonPropertyName("tenant_id")] int TenantId,
    [property: JsonPropertyName("branch_id")] int BranchId,
    [property: JsonPropertyName("status")] int Status,
    [property: JsonPropertyName("status_name")] string StatusName,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig,
    [property: JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("partner_type_name")] string PartnerTypeName,
    [property: JsonPropertyName("created_by")] long CreatedBy,
    [property: JsonPropertyName("created_at")] DateTime CreatedAt,
    [property: JsonPropertyName("modified_by")] long? ModifiedBy,
    [property: JsonPropertyName("modified_at")] DateTime? ModifiedAt
);
```

**UpdateEInvoiceSettingStatus Use Case Models:**

**UpdateEInvoiceSettingStatusRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.UpdateEInvoiceSettingStatus;

public record UpdateEInvoiceSettingStatusRequest(
    [property: Required, JsonPropertyName("id")] long Id,
    [property: Required, Range(0, 1), JsonPropertyName("status")] int Status
) : IRequest<UpdateEInvoiceSettingStatusResponse>;
```

**UpdateEInvoiceSettingStatusResponse.cs**
```csharp
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.UpdateEInvoiceSettingStatus;

public record UpdateEInvoiceSettingStatusResponse(
    [property: JsonPropertyName("id")] long Id,
    [property: JsonPropertyName("tenant_id")] int TenantId,
    [property: JsonPropertyName("branch_id")] int BranchId,
    [property: JsonPropertyName("status")] int Status,
    [property: JsonPropertyName("status_name")] string StatusName,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig,
    [property: JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("partner_type_name")] string PartnerTypeName,
    [property: JsonPropertyName("created_by")] long CreatedBy,
    [property: JsonPropertyName("created_at")] DateTime CreatedAt,
    [property: JsonPropertyName("modified_by")] long? ModifiedBy,
    [property: JsonPropertyName("modified_at")] DateTime? ModifiedAt
);
```

**GetEInvoiceSettingById Use Case Models:**

**GetEInvoiceSettingByIdRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingById;

public record GetEInvoiceSettingByIdRequest(
    [property: Required, JsonPropertyName("id")] long Id
) : IRequest<GetEInvoiceSettingByIdResponse>;
```

**GetEInvoiceSettingByIdResponse.cs**
```csharp
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingById;

public record GetEInvoiceSettingByIdResponse(
    [property: JsonPropertyName("id")] long Id,
    [property: JsonPropertyName("tenant_id")] int TenantId,
    [property: JsonPropertyName("branch_id")] int BranchId,
    [property: JsonPropertyName("status")] int Status,
    [property: JsonPropertyName("status_name")] string StatusName,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig,
    [property: JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("partner_type_name")] string PartnerTypeName,
    [property: JsonPropertyName("created_by")] long CreatedBy,
    [property: JsonPropertyName("created_at")] DateTime CreatedAt,
    [property: JsonPropertyName("modified_by")] long? ModifiedBy,
    [property: JsonPropertyName("modified_at")] DateTime? ModifiedAt
);
```

**GetEInvoiceSettingsByRetailer Use Case Models:**

**GetEInvoiceSettingsByRetailerRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingsByRetailer;

public record GetEInvoiceSettingsByRetailerRequest(
    [property: Required, JsonPropertyName("retailer_id")] int RetailerId
) : IRequest<GetEInvoiceSettingsByRetailerResponse>;
```

**GetEInvoiceSettingsByRetailerResponse.cs**
```csharp
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingsByRetailer;

public record GetEInvoiceSettingsByRetailerResponse(
    [property: JsonPropertyName("items")] List<EInvoiceSettingDto> Items,
    [property: JsonPropertyName("total_count")] int TotalCount
);
```

**GetEInvoiceSettingByRetailerAndBranch Use Case Models:**

**GetEInvoiceSettingByRetailerAndBranchRequest.cs**
```csharp
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingByRetailerAndBranch;

public record GetEInvoiceSettingByRetailerAndBranchRequest(
    [property: Required, JsonPropertyName("retailer_id")] int RetailerId,
    [property: Required, JsonPropertyName("branch_id")] int BranchId
) : IRequest<GetEInvoiceSettingByRetailerAndBranchResponse>;
```

**GetEInvoiceSettingByRetailerAndBranchResponse.cs**
```csharp
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingByRetailerAndBranch;

public record GetEInvoiceSettingByRetailerAndBranchResponse(
    [property: JsonPropertyName("id")] long Id,
    [property: JsonPropertyName("tenant_id")] int TenantId,
    [property: JsonPropertyName("branch_id")] int BranchId,
    [property: JsonPropertyName("status")] int Status,
    [property: JsonPropertyName("status_name")] string StatusName,
    [property: JsonPropertyName("einvoice_config")] EInvoiceConfigDto? EInvoiceConfig,
    [property: JsonPropertyName("partner_type")] int PartnerType,
    [property: JsonPropertyName("partner_type_name")] string PartnerTypeName,
    [property: JsonPropertyName("created_by")] long CreatedBy,
    [property: JsonPropertyName("created_at")] DateTime CreatedAt,
    [property: JsonPropertyName("modified_by")] long? ModifiedBy,
    [property: JsonPropertyName("modified_at")] DateTime? ModifiedAt
);
```

**Shared Response Models:**

**EInvoiceConfigResponse.cs**
```csharp
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.Shared;

public record EInvoiceConfigResponse(
    [property: JsonPropertyName("is_push_from_pos")] bool IsPushFromPos,
    [property: JsonPropertyName("default_template_id")] string DefaultTemplateId,
    [property: JsonPropertyName("url_search")] string UrlSearch,
    [property: JsonPropertyName("is_push_invoice_item_note")] bool IsPushInvoiceItemNote,
    [property: JsonPropertyName("tax_code")] string TaxCode
);
```

**Response Extension Methods:**

**EInvoiceSettingResponseExtensions.cs**
```csharp
using KvFnB.Modules.Payment.Application.UseCases.Contracts;
using KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.CreateEInvoiceSetting;
using KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.UpdateEInvoiceSetting;
using KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.UpdateEInvoiceSettingStatus;
using KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.GetEInvoiceSettingById;
using KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.GetEInvoiceSettingsByRetailer;
using KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.GetEInvoiceSettingByRetailerAndBranch;

namespace KvFnB.Modules.Payment.Restful.EInvoiceSettingUseCase.Shared;

public static class EInvoiceSettingResponseExtensions
{
    public static CreateEInvoiceSettingResponse ToCreateResponse(this EInvoiceSettingDto dto)
    {
        return new CreateEInvoiceSettingResponse(
            Id: dto.Id,
            TenantId: dto.TenantId,
            BranchId: dto.BranchId,
            Status: dto.Status,
            StatusName: dto.StatusName,
            EInvoiceConfig: dto.EInvoiceConfig,
            PartnerType: dto.PartnerType,
            PartnerTypeName: dto.PartnerTypeName,
            CreatedBy: dto.CreatedBy,
            CreatedAt: dto.CreatedAt,
            ModifiedBy: dto.ModifiedBy,
            ModifiedAt: dto.ModifiedAt
        );
    }

    public static UpdateEInvoiceSettingResponse ToUpdateResponse(this EInvoiceSettingDto dto)
    {
        return new UpdateEInvoiceSettingResponse(
            Id: dto.Id,
            TenantId: dto.TenantId,
            BranchId: dto.BranchId,
            Status: dto.Status,
            StatusName: dto.StatusName,
            EInvoiceConfig: dto.EInvoiceConfig,
            PartnerType: dto.PartnerType,
            PartnerTypeName: dto.PartnerTypeName,
            CreatedBy: dto.CreatedBy,
            CreatedAt: dto.CreatedAt,
            ModifiedBy: dto.ModifiedBy,
            ModifiedAt: dto.ModifiedAt
        );
    }

    public static UpdateEInvoiceSettingStatusResponse ToUpdateStatusResponse(this EInvoiceSettingDto dto)
    {
        return new UpdateEInvoiceSettingStatusResponse(
            Id: dto.Id,
            TenantId: dto.TenantId,
            BranchId: dto.BranchId,
            Status: dto.Status,
            StatusName: dto.StatusName,
            EInvoiceConfig: dto.EInvoiceConfig,
            PartnerType: dto.PartnerType,
            PartnerTypeName: dto.PartnerTypeName,
            CreatedBy: dto.CreatedBy,
            CreatedAt: dto.CreatedAt,
            ModifiedBy: dto.ModifiedBy,
            ModifiedAt: dto.ModifiedAt
        );
    }

    public static GetEInvoiceSettingByIdResponse ToGetByIdResponse(this EInvoiceSettingDto dto)
    {
        return new GetEInvoiceSettingByIdResponse(
            Id: dto.Id,
            TenantId: dto.TenantId,
            BranchId: dto.BranchId,
            Status: dto.Status,
            StatusName: dto.StatusName,
            EInvoiceConfig: dto.EInvoiceConfig,
            PartnerType: dto.PartnerType,
            PartnerTypeName: dto.PartnerTypeName,
            CreatedBy: dto.CreatedBy,
            CreatedAt: dto.CreatedAt,
            ModifiedBy: dto.ModifiedBy,
            ModifiedAt: dto.ModifiedAt
        );
    }

    public static GetEInvoiceSettingsByRetailerResponse ToGetByRetailerResponse(this IEnumerable<EInvoiceSettingDto> dtos)
    {
        var items = dtos.ToList();
        return new GetEInvoiceSettingsByRetailerResponse(items, items.Count);
    }

    public static GetEInvoiceSettingByRetailerAndBranchResponse ToGetByRetailerAndBranchResponse(this EInvoiceSettingDto dto)
    {
        return new GetEInvoiceSettingByRetailerAndBranchResponse(
            Id: dto.Id,
            TenantId: dto.TenantId,
            BranchId: dto.BranchId,
            Status: dto.Status,
            StatusName: dto.StatusName,
            EInvoiceConfig: dto.EInvoiceConfig,
            PartnerType: dto.PartnerType,
            PartnerTypeName: dto.PartnerTypeName,
            CreatedBy: dto.CreatedBy,
            CreatedAt: dto.CreatedAt,
            ModifiedBy: dto.ModifiedBy,
            ModifiedAt: dto.ModifiedAt
        );
    }

    public static EInvoiceConfigResponse ToResponse(this EInvoiceConfigDto dto)
    {
        return new EInvoiceConfigResponse(
            IsPushFromPos: dto.IsPushFromPos,
            DefaultTemplateId: dto.DefaultTemplateId,
            UrlSearch: dto.UrlSearch,
            IsPushInvoiceItemNote: dto.IsPushInvoiceItemNote,
            TaxCode: dto.TaxCode
        );
    }
}
```

**EInvoiceSettingExtensions.cs (Application Layer)**
```csharp
using System.Text.Json;
using KvFnB.Modules.Payment.Domain.Models;

namespace KvFnB.Modules.Payment.Application.UseCases.Contracts;

public static class EInvoiceSettingExtensions
{
    public static EInvoiceSettingDto ToDto(this EInvoiceSetting setting)
    {
        EInvoiceConfigDto? configDto = null;
        if (!string.IsNullOrEmpty(setting.EInvoiceConfig))
        {
            try
            {
                configDto = JsonSerializer.Deserialize<EInvoiceConfigDto>(setting.EInvoiceConfig, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });
            }
            catch (JsonException)
            {
                // Log error and return null config if deserialization fails
                configDto = null;
            }
        }

        return new EInvoiceSettingDto(
            Id: setting.Id,
            TenantId: setting.TenantId,
            BranchId: setting.BranchId,
            Status: setting.Status.Id,
            StatusName: setting.Status.Name,
            EInvoiceConfig: configDto,
            PartnerType: setting.PartnerType.Id,
            PartnerTypeName: setting.PartnerType.Name,
            CreatedBy: setting.CreatedBy,
            CreatedAt: setting.CreatedAt,
            ModifiedBy: setting.ModifiedBy,
            ModifiedAt: setting.ModifiedAt
        );
    }

    public static List<EInvoiceSettingDto> ToDto(this IEnumerable<EInvoiceSetting> settings)
    {
        return settings.Select(ToDto).ToList();
    }
}
```

## 3. Integration Points

### 3.1 Update Existing EInvoiceController

**EInvoiceController.cs - Add Admin endpoints using MediatR**
```csharp
using MediatR;
using KvFnB.Core.Authentication;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.CreateEInvoiceSetting;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.UpdateEInvoiceSetting;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.UpdateEInvoiceSettingStatus;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingById;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingsByRetailer;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCase.GetEInvoiceSettingByRetailerAndBranch;

// In constructor, inject IAuthUser
private readonly IAuthUser _authUser;

public EInvoiceController(IMediator mediator, IAuthUser authUser, /* other dependencies */)
{
    _mediator = mediator;
    _authUser = authUser;
    // Initialize other dependencies
}

[HttpPost("settings")]
[SwaggerOperation(Summary = "Create EInvoice setting", Description = "Creates a new EInvoice setting for a branch")]
[SwaggerResponse(StatusCodes.Status201Created, "EInvoice setting created successfully", typeof(CreateEInvoiceSettingResponse))]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized - Admin access required")]
public async Task<IActionResult> CreateEInvoiceSetting([FromBody] CreateEInvoiceSettingRequest request)
{
    if (!_authUser.IsAdmin)
    {
        return Unauthorized("Admin access required");
    }

    var result = await _mediator.Send(request);
    return result.IsSuccess ? 
        Created($"/api/EInvoice/settings/{result.Data.Id}", result.Data) : 
        Failure(result);
}

[HttpGet("settings/{id}")]
[SwaggerOperation(Summary = "Get EInvoice setting by ID")]
[SwaggerResponse(StatusCodes.Status200OK, "EInvoice setting retrieved successfully", typeof(GetEInvoiceSettingByIdResponse))]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized - Admin access required")]
public async Task<IActionResult> GetEInvoiceSetting(long id)
{
    if (!_authUser.IsAdmin)
    {
        return Unauthorized("Admin access required");
    }

    var request = new GetEInvoiceSettingByIdRequest(id);
    var result = await _mediator.Send(request);
    return result.IsSuccess ? 
        Success(result.Data) : 
        Failure(result);
}

[HttpPut("settings/{id}")]
[SwaggerOperation(Summary = "Update EInvoice setting")]
[SwaggerResponse(StatusCodes.Status200OK, "EInvoice setting updated successfully", typeof(UpdateEInvoiceSettingResponse))]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized - Admin access required")]
public async Task<IActionResult> UpdateEInvoiceSetting(long id, [FromBody] UpdateEInvoiceSettingRequest request)
{
    if (!_authUser.IsAdmin)
    {
        return Unauthorized("Admin access required");
    }

    // Override the ID from route parameter
    var updateRequest = request with { Id = id };
    var result = await _mediator.Send(updateRequest);
    return result.IsSuccess ? 
        Success(result.Data) : 
        Failure(result);
}

[HttpPatch("settings/{id}/status")]
[SwaggerOperation(Summary = "Update EInvoice setting status")]
[SwaggerResponse(StatusCodes.Status200OK, "EInvoice setting status updated successfully", typeof(UpdateEInvoiceSettingStatusResponse))]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized - Admin access required")]
public async Task<IActionResult> UpdateEInvoiceSettingStatus(long id, [FromBody] UpdateEInvoiceSettingStatusRequest request)
{
    if (!_authUser.IsAdmin)
    {
        return Unauthorized("Admin access required");
    }

    // Override the ID from route parameter
    var updateRequest = request with { Id = id };
    var result = await _mediator.Send(updateRequest);
    return result.IsSuccess ? 
        Success(result.Data) : 
        Failure(result);
}

[HttpGet("settings/retailer/{retailerId}")]
[SwaggerOperation(Summary = "Get EInvoice settings by retailer")]
[SwaggerResponse(StatusCodes.Status200OK, "EInvoice settings retrieved successfully", typeof(GetEInvoiceSettingsByRetailerResponse))]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized - Admin access required")]
public async Task<IActionResult> GetEInvoiceSettingsByRetailer(int retailerId)
{
    if (!_authUser.IsAdmin)
    {
        return Unauthorized("Admin access required");
    }

    var request = new GetEInvoiceSettingsByRetailerRequest(retailerId);
    var result = await _mediator.Send(request);
    return result.IsSuccess ? 
        Success(result.Data) : 
        Failure(result);
}

[HttpGet("settings/retailer/{retailerId}/branch/{branchId}")]
[SwaggerOperation(Summary = "Get EInvoice setting by retailer and branch")]
[SwaggerResponse(StatusCodes.Status200OK, "EInvoice setting retrieved successfully", typeof(GetEInvoiceSettingByRetailerAndBranchResponse))]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "Unauthorized - Admin access required")]
public async Task<IActionResult> GetEInvoiceSettingByRetailerAndBranch(int retailerId, int branchId)
{
    if (!_authUser.IsAdmin)
    {
        return Unauthorized("Admin access required");
    }

    var request = new GetEInvoiceSettingByRetailerAndBranchRequest(retailerId, branchId);
    var result = await _mediator.Send(request);
    return result.IsSuccess ? 
        Success(result.Data) : 
        Failure(result);
}
```

## 4. Summary

**Total**: 31 new files + 2 updated files (EInvoiceController + Infrastructure) with **Admin access control** for all EInvoiceSetting endpoints.

### Key Features:
- All DTOs, requests, and responses are **records** with **MediatR integration**
- **Admin access control** using `IAuthUser.IsAdmin` checks (not role-based authorization)
- JSON property naming with snake_case
- Complete validation attributes for all request models
- Swagger documentation with proper response type declarations including 401 Unauthorized
- **Request/Response models in Application layer** following UseCase pattern
- **Validator classes** for all use case requests
- **Infrastructure updates** for DependencyInjection and Mapping
- Clean architecture patterns following the official guidelines

### File Breakdown:
- **Domain Layer**: 8 files (1 entity, 2 enums, 1 repository interface, 3 specifications, 3 exceptions)
- **Application Layer**: 18 files (3 contracts + 15 use case files with requests/responses/validators)
- **Infrastructure Layer**: 3 files (1 repository, 1 mapping profile update, 1 DI registrar update)
- **Presentation Layer**: 1 updated controller
- **Shared Layer**: 1 entity configuration

The implementation follows the hexagonal architecture pattern with proper separation of concerns and all required components as specified in the UseCase implementation guidelines. 