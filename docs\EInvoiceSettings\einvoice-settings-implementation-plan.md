# EInvoice Settings Implementation Plan

## Overview
This document outlines the implementation plan for the EInvoice Settings feature, broken down into 6 manageable tasks following clean architecture principles.

**Total Scope:** 31 new files + 2 updated files across all layers

---

## **Task 1: Domain Layer Foundation** 
**Priority:** `HIGH` - Must complete first  
**Estimated Time:** 2-3 days  
**Dependencies:** None

### Files to Create (10 files)

#### Core Domain Models
- `KvFnB.Modules.Payment.Domain/Models/EInvoiceSetting.cs`
- `KvFnB.Modules.Payment.Domain/Enums/EInvoiceStatus.cs`
- `KvFnB.Modules.Payment.Domain/Enums/EInvoicePartnerType.cs`

#### Repository Interface
- `KvFnB.Modules.Payment.Domain/Repositories/IEInvoiceSettingRepository.cs`

#### Specifications
- `KvFnB.Modules.Payment.Domain/Specifications/EInvoiceSetting/EInvoiceSettingByIdSpec.cs`
- `KvFnB.Modules.Payment.Domain/Specifications/EInvoiceSetting/EInvoiceSettingByRetailerSpec.cs`
- `KvFnB.Modules.Payment.Domain/Specifications/EInvoiceSetting/EInvoiceSettingByRetailerAndBranchSpec.cs`

#### Domain Exceptions
- `KvFnB.Modules.Payment.Domain/Exceptions/EInvoiceSettingAlreadyExistsException.cs`
- `KvFnB.Modules.Payment.Domain/Exceptions/EInvoiceSettingNotFoundException.cs`
- `KvFnB.Modules.Payment.Domain/Exceptions/InvalidEInvoiceConfigurationException.cs`

### Key Features
- EInvoiceSetting aggregate root with IAuditableEntity
- Strongly-typed enums for Status and PartnerType
- Business validation in domain methods
- Custom domain exceptions for error handling

### Acceptance Criteria
- [ ] EInvoiceSetting entity with proper encapsulation
- [ ] Enums with Id and Name properties
- [ ] Repository interface following specification pattern
- [ ] Custom exceptions with meaningful messages
- [ ] All domain business rules implemented

---

## **Task 2: Application Contracts**
**Priority:** `HIGH` - Depends on Task 1  
**Estimated Time:** 1-2 days  
**Dependencies:** Task 1

### Files to Create (3 files)
- `KvFnB.Modules.Payment.Application/Contracts/EInvoiceConfigDto.cs`
- `KvFnB.Modules.Payment.Application/Contracts/EInvoiceSettingDto.cs`
- `KvFnB.Modules.Payment.Application/Contracts/EInvoiceSettingExtensions.cs`

### Key Features
- Strongly-typed EInvoiceConfigDto with validation attributes
- EInvoiceSettingDto with snake_case JSON properties
- Extension methods for Domain to DTO mapping
- JSON serialization/deserialization handling

### Acceptance Criteria
- [ ] DTOs with proper validation attributes
- [ ] Snake_case JSON property naming
- [ ] Extension methods for mapping
- [ ] Error handling for JSON operations

---

## **Task 3A: Query Use Cases (Read Operations)**
**Priority:** `MEDIUM` - Depends on Task 2  
**Estimated Time:** 3-4 days  
**Dependencies:** Task 1, Task 2

### Files to Create (12 files)

#### GetEInvoiceSettingById (4 files)
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingById/GetEInvoiceSettingByIdRequest.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingById/GetEInvoiceSettingByIdResponse.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingById/GetEInvoiceSettingByIdValidator.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingById/GetEInvoiceSettingByIdUseCase.cs`

#### GetEInvoiceSettingsByRetailer (4 files)
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingsByRetailer/GetEInvoiceSettingsByRetailerRequest.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingsByRetailer/GetEInvoiceSettingsByRetailerResponse.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingsByRetailer/GetEInvoiceSettingsByRetailerValidator.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingsByRetailer/GetEInvoiceSettingsByRetailerUseCase.cs`

#### GetEInvoiceSettingByRetailerAndBranch (4 files)
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingByRetailerAndBranch/GetEInvoiceSettingByRetailerAndBranchRequest.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingByRetailerAndBranch/GetEInvoiceSettingByRetailerAndBranchResponse.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingByRetailerAndBranch/GetEInvoiceSettingByRetailerAndBranchValidator.cs`
- `Application/UseCases/EInvoiceSettingUseCase/GetEInvoiceSettingByRetailerAndBranch/GetEInvoiceSettingByRetailerAndBranchUseCase.cs`

### Key Features
- MediatR integration with IRequest<TResponse>
- IQueryService usage for data retrieval
- Input validation with Validator classes
- Proper error handling and result patterns

### Acceptance Criteria
- [ ] All requests implement IRequest<TResponse>
- [ ] Validators with proper validation rules
- [ ] Use cases using IQueryService
- [ ] Proper error handling with Result pattern

---

## **Task 3B: Command Use Cases (Write Operations)**
**Priority:** `MEDIUM` - Depends on Task 2  
**Estimated Time:** 3-4 days  
**Dependencies:** Task 1, Task 2

### Files to Create (12 files)

#### CreateEInvoiceSetting (4 files)
- `Application/UseCases/EInvoiceSettingUseCase/CreateEInvoiceSetting/CreateEInvoiceSettingRequest.cs`
- `Application/UseCases/EInvoiceSettingUseCase/CreateEInvoiceSetting/CreateEInvoiceSettingResponse.cs`
- `Application/UseCases/EInvoiceSettingUseCase/CreateEInvoiceSetting/CreateEInvoiceSettingValidator.cs`
- `Application/UseCases/EInvoiceSettingUseCase/CreateEInvoiceSetting/CreateEInvoiceSettingUseCase.cs`

#### UpdateEInvoiceSetting (4 files)
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSetting/UpdateEInvoiceSettingRequest.cs`
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSetting/UpdateEInvoiceSettingResponse.cs`
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSetting/UpdateEInvoiceSettingValidator.cs`
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSetting/UpdateEInvoiceSettingUseCase.cs`

#### UpdateEInvoiceSettingStatus (4 files)
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSettingStatus/UpdateEInvoiceSettingStatusRequest.cs`
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSettingStatus/UpdateEInvoiceSettingStatusResponse.cs`
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSettingStatus/UpdateEInvoiceSettingStatusValidator.cs`
- `Application/UseCases/EInvoiceSettingUseCase/UpdateEInvoiceSettingStatus/UpdateEInvoiceSettingStatusUseCase.cs`

### Key Features
- IRepository and IUnitOfWork usage
- Business logic validation
- Automatic audit field handling
- Transaction management

### Acceptance Criteria
- [ ] Use cases using IRepository for data persistence
- [ ] IUnitOfWork for transaction management
- [ ] Business validation before persistence
- [ ] Proper error handling for business rules

---

## **Task 4: Infrastructure Layer**
**Priority:** `MEDIUM` - Depends on Task 1, 3A, 3B  
**Estimated Time:** 2-3 days  
**Dependencies:** Task 1, Task 3A, Task 3B

### Files to Create/Update (3 files)

#### New Files
- `KvFnB.Modules.Payment.Infrastructure/Persistence/EInvoiceSettingRepository.cs`

#### Files to Update
- `KvFnB.Modules.Payment.Infrastructure/DependencyInjection/PaymentModuleRegistrar.cs`
- `KvFnB.Modules.Payment.Infrastructure/Mapping/PaymentMappingProfile.cs`

### Key Features
- Repository implementation with specification pattern
- Dependency injection registration for all use cases and validators
- AutoMapper profile configuration
- Proper service registration

### Acceptance Criteria
- [ ] Repository implementing IEInvoiceSettingRepository
- [ ] All use cases registered in DI container
- [ ] All validators registered in DI container
- [ ] AutoMapper profiles configured
- [ ] Proper service lifetimes (Scoped for most services)

---

## **Task 5: Shared Persistence Configuration**
**Priority:** `LOW` - Depends on Task 1  
**Estimated Time:** 1 day  
**Dependencies:** Task 1

### Files to Create (1 file)
- `Shared/Persistence/ShardingDb/EntityTypeConfigurations/EInvoiceSettingEntityTypeConfiguration.cs`

### Key Features
- Entity Framework configuration
- Table mapping and constraints
- Index configuration
- Unique constraint on (TenantId, BranchId)

### Acceptance Criteria
- [ ] Proper table mapping
- [ ] Unique constraint implementation
- [ ] Index configuration for performance
- [ ] Foreign key relationships

---

## **Task 6: API Layer (Controller)**
**Priority:** `LOW` - Depends on all previous tasks  
**Estimated Time:** 2-3 days  
**Dependencies:** All previous tasks

### Files to Update (1 file)
- `KvFnB.Modules.Payment.Restful/EInvoiceController.cs`

### API Endpoints to Add
1. `POST /api/EInvoice/settings` - Create setting
2. `GET /api/EInvoice/settings/{id}` - Get by ID
3. `PUT /api/EInvoice/settings/{id}` - Update setting
4. `PATCH /api/EInvoice/settings/{id}/status` - Update status
5. `GET /api/EInvoice/settings/retailer/{retailerId}` - Get by retailer
6. `GET /api/EInvoice/settings/retailer/{retailerId}/branch/{branchId}` - Get by retailer and branch

### Key Features
- IAuthUser.IsAdmin access control (not role-based)
- MediatR integration
- Swagger documentation
- Proper HTTP status codes
- Input validation

### Acceptance Criteria
- [ ] All 6 endpoints implemented
- [ ] IAuthUser.IsAdmin checks in all endpoints
- [ ] Proper Swagger documentation
- [ ] MediatR usage for all operations
- [ ] Proper HTTP status codes and error handling

---

## **Implementation Timeline**

### **Week 1: Foundation**
- **Day 1-2:** Task 1 (Domain Layer)
- **Day 3:** Task 2 (Application Contracts)
- **Day 4-5:** Start Task 3A

### **Week 2: Query Operations**
- **Day 1-3:** Complete Task 3A (Query Use Cases)
- **Day 4-5:** Start Task 3B

### **Week 3: Command Operations**
- **Day 1-3:** Complete Task 3B (Command Use Cases)
- **Day 4-5:** Task 4 (Infrastructure)

### **Week 4: Infrastructure & Configuration**
- **Day 1:** Complete Task 4
- **Day 2:** Task 5 (Persistence Configuration)
- **Day 3-5:** Task 6 (API Layer)

### **Week 5: Testing & Finalization**
- **Day 1-3:** Integration testing
- **Day 4-5:** Bug fixes and documentation

---

## **Testing Strategy**

### Unit Tests (Optional but Recommended)
- Domain entity behavior tests
- Use case logic tests
- Validator tests
- Repository tests

### Integration Tests
- API endpoint tests
- Database integration tests
- End-to-end workflow tests

---

## **Rollback Plan**

Each task is designed to be independent where possible:
- Tasks 3A and 3B can be implemented in parallel
- Infrastructure (Task 4) can be partially implemented
- API layer (Task 6) can be disabled via feature flags

---

## **Success Metrics**

- [ ] All 31 new files created successfully
- [ ] 2 existing files updated without breaking changes
- [ ] All API endpoints working with proper admin access control
- [ ] Full CRUD operations for EInvoice Settings
- [ ] Proper error handling and validation
- [ ] Clean architecture principles maintained
- [ ] No breaking changes to existing functionality

---

## **Risk Mitigation**

**High Risk:**
- Database migration issues → Test on dev environment first
- Breaking existing Payment module → Comprehensive testing

**Medium Risk:**
- UseCase complexity → Follow existing patterns
- Validation rules → Use reference implementations

**Low Risk:**
- API documentation → Use existing Swagger patterns
- Performance issues → Implement with proper indexing

---

## **Getting Started**

1. **Choose starting task:** Recommend Task 1 (Domain Layer)
2. **Set up branch:** Create feature branch from main
3. **Review documentation:** Study existing EWallet implementation
4. **Start implementation:** Follow UseCase implementation guidelines
5. **Regular commits:** Commit after each major component

**Ready to begin implementation? Start with Task 1!**