# EInvoice Settings Technical Specification

## 1. Overview

This document outlines the technical specification for developing an EInvoice Settings management system. The system will provide branch-specific electronic invoice configuration management with support for multiple EInvoice providers (MISA, VIETTEL, VNPT, KIOTVIET). The feature ensures only one configuration per branch and supports create, update status, and update operations without deletion capabilities.

## 2. System Requirements

### 2.1 Functional Requirements

- **Branch-specific Configuration**: Each branch can have only one EInvoice setting configuration
- **Provider Support**: Support for multiple EInvoice providers (MISA=1, VIETTEL=2, VNPT=3, KIOTVIET=4)
- **Configuration Management**: Store flexible JSON configuration for each provider
- **Status Management**: Enable/disable configurations without deletion
- **Audit Trail**: Track creation and modification with user information and timestamps
- **Data Integrity**: Prevent duplicate configurations per branch
- **No Deletion**: Only allow create and update operations, no physical deletion

### 2.2 Non-Functional Requirements

- **Performance**: Efficient querying by branch and retailer
- **Security**: Proper authentication and authorization for configuration management
- **Reliability**: Ensure data consistency and prevent configuration conflicts
- **Scalability**: Support multiple retailers and branches
- **Maintainability**: Follow domain-driven design principles

## 3. Database Design

### 3.1 Table Structure

```sql
CREATE TABLE EInvoiceSetting (
    Id BIGINT PRIMARY KEY IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
    RetailerId INT NOT NULL,
    BranchId INT NOT NULL,
    CreatedBy BIGINT NOT NULL, 
    ModifiedBy BIGINT NULL, 
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME NULL,
    [Status] TINYINT NOT NULL, -- 0. InActive, 1 - Active
    EInvoiceConfig NVARCHAR(MAX) NULL,  -- JSON configuration
    PartnerType INT NOT NULL, -- MISA = 1, VIETTEL = 2, VNPT = 3, KIOTVIET = 4
    
    -- Constraints
    CONSTRAINT UK_EInvoiceSetting_RetailerId_BranchId UNIQUE (RetailerId, BranchId)
);
```

### 3.2 JSON Configuration Schema

```json
{
    "IsPushFromPos": true,
    "DefaultTemplateId": "ABC",
    "UrlSearch": "abc.com",
    "IsPushInvoiceItemNote": true,
    "TaxCode": "abcd",
    "ApiUrl": "https://api.provider.com",
    "Username": "username",
    "Password": "encrypted_password",
    "CompanyCode": "COMP001",
    "AdditionalSettings": {
        "TimeoutSeconds": 30,
        "RetryAttempts": 3,
        "EnableLogging": true
    }
}
```

## 4. Domain Design

### 4.1 Domain Entity

```csharp
namespace KvFnB.Modules.Invoice.Domain.Models
{
    public class EInvoiceSetting : AggregateRoot<long>, ICreatedBy, ICreatedAt, IModifiedBy, IModifiedAt
    {
        public int RetailerId { get; private set; }
        public int BranchId { get; private set; }
        public EInvoiceStatus Status { get; private set; }
        public string? EInvoiceConfig { get; private set; }
        public EInvoicePartnerType PartnerType { get; private set; }
        public long CreatedBy { get; private set; }
        public DateTime CreatedAt { get; private set; }
        public long? ModifiedBy { get; private set; }
        public DateTime? ModifiedAt { get; private set; }

        protected EInvoiceSetting() { }

        public static EInvoiceSetting Create(
            int retailerId,
            int branchId,
            EInvoicePartnerType partnerType,
            string? eInvoiceConfig = null,
            long createdBy = 0)
        {
            var setting = new EInvoiceSetting
            {
                RetailerId = retailerId,
                BranchId = branchId,
                PartnerType = partnerType,
                EInvoiceConfig = eInvoiceConfig,
                Status = EInvoiceStatus.Active,
                CreatedBy = createdBy,
                CreatedAt = DateTime.UtcNow
            };

            return setting;
        }

        public void UpdateConfiguration(string? eInvoiceConfig, long modifiedBy)
        {
            EInvoiceConfig = eInvoiceConfig;
            ModifiedBy = modifiedBy;
            ModifiedAt = DateTime.UtcNow;
        }

        public void UpdateStatus(EInvoiceStatus status, long modifiedBy)
        {
            Status = status;
            ModifiedBy = modifiedBy;
            ModifiedAt = DateTime.UtcNow;
        }

        public void Update(
            EInvoicePartnerType partnerType,
            string? eInvoiceConfig,
            long modifiedBy)
        {
            PartnerType = partnerType;
            EInvoiceConfig = eInvoiceConfig;
            ModifiedBy = modifiedBy;
            ModifiedAt = DateTime.UtcNow;
        }
    }
}
```

### 4.2 Value Objects

```csharp
namespace KvFnB.Modules.Invoice.Domain.Enums
{
    public class EInvoiceStatus : Enumeration
    {
        public static readonly EInvoiceStatus InActive = new(0, nameof(InActive));
        public static readonly EInvoiceStatus Active = new(1, nameof(Active));

        public EInvoiceStatus(int id, string name) : base(id, name) { }
    }

    public class EInvoicePartnerType : Enumeration
    {
        public static readonly EInvoicePartnerType MISA = new(1, nameof(MISA));
        public static readonly EInvoicePartnerType VIETTEL = new(2, nameof(VIETTEL));
        public static readonly EInvoicePartnerType VNPT = new(3, nameof(VNPT));
        public static readonly EInvoicePartnerType KIOTVIET = new(4, nameof(KIOTVIET));

        public EInvoicePartnerType(int id, string name) : base(id, name) { }
    }
}
```

### 4.3 Repository Interface

```csharp
namespace KvFnB.Modules.Invoice.Domain.Repositories
{
    public interface IEInvoiceSettingRepository : IRepository<EInvoiceSetting, long>
    {
        /// <summary>
        /// Checks if an EInvoice setting already exists for the specified retailer and branch
        /// </summary>
        /// <param name="retailerId">The retailer ID</param>
        /// <param name="branchId">The branch ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if setting exists, false otherwise</returns>
        Task<bool> ExistsAsync(int retailerId, int branchId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets EInvoice setting by retailer and branch
        /// </summary>
        /// <param name="retailerId">The retailer ID</param>
        /// <param name="branchId">The branch ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>EInvoice setting if found, null otherwise</returns>
        Task<EInvoiceSetting?> GetByRetailerAndBranchAsync(int retailerId, int branchId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all EInvoice settings for a retailer
        /// </summary>
        /// <param name="retailerId">The retailer ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of EInvoice settings</returns>
        Task<List<EInvoiceSetting>> GetByRetailerAsync(int retailerId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets EInvoice setting by ID
        /// </summary>
        /// <param name="id">The setting ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>EInvoice setting if found, null otherwise</returns>
        Task<EInvoiceSetting?> GetByIdAsync(long id, CancellationToken cancellationToken = default);
    }
}
```

## 5. Application Layer

### 5.1 Use Cases

#### 5.1.1 Create EInvoice Setting

```csharp
namespace KvFnB.Modules.Invoice.Application.UseCases.EInvoiceSettingUseCases.CreateEInvoiceSettingUseCase
{
    public record CreateEInvoiceSettingRequest
    {
        [Required]
        [Range(1, int.MaxValue)]
        [JsonPropertyName("retailer_id")]
        [Description("The retailer ID (must be greater than 0).")]
        public int RetailerId { get; init; }

        [Required]
        [Range(1, int.MaxValue)]
        [JsonPropertyName("branch_id")]
        [Description("The branch ID (must be greater than 0).")]
        public int BranchId { get; init; }

        [Required]
        [Range(1, 4)]
        [JsonPropertyName("partner_type")]
        [Description("The EInvoice partner type (1=MISA, 2=VIETTEL, 3=VNPT, 4=KIOTVIET).")]
        public int PartnerType { get; init; }

        [JsonPropertyName("einvoice_config")]
        [Description("JSON configuration for the EInvoice provider.")]
        public string? EInvoiceConfig { get; init; }
    }

    public class CreateEInvoiceSettingUseCase : IRequestHandler<CreateEInvoiceSettingRequest, EInvoiceSettingDto>
    {
        private readonly IEInvoiceSettingRepository _repository;
        private readonly IAuthUser _authUser;
        private readonly ILogger<CreateEInvoiceSettingUseCase> _logger;

        public CreateEInvoiceSettingUseCase(
            IEInvoiceSettingRepository repository,
            IAuthUser authUser,
            ILogger<CreateEInvoiceSettingUseCase> logger)
        {
            _repository = repository;
            _authUser = authUser;
            _logger = logger;
        }

        public async Task<EInvoiceSettingDto> Handle(CreateEInvoiceSettingRequest request, CancellationToken cancellationToken)
        {
            // Check if setting already exists
            var exists = await _repository.ExistsAsync(request.RetailerId, request.BranchId, cancellationToken);
            if (exists)
            {
                throw new BusinessException("EInvoice setting already exists for this branch");
            }

            // Validate JSON configuration if provided
            if (!string.IsNullOrEmpty(request.EInvoiceConfig))
            {
                ValidateJsonConfiguration(request.EInvoiceConfig);
            }

            // Create new setting
            var partnerType = EInvoicePartnerType.From(request.PartnerType);
            var setting = EInvoiceSetting.Create(
                request.RetailerId,
                request.BranchId,
                partnerType,
                request.EInvoiceConfig,
                _authUser.UserId);

            await _repository.AddAsync(setting, cancellationToken);

            _logger.LogInformation("Created EInvoice setting for retailer {RetailerId}, branch {BranchId}", 
                request.RetailerId, request.BranchId);

            return setting.ToDto();
        }

        private static void ValidateJsonConfiguration(string jsonConfig)
        {
            try
            {
                JsonDocument.Parse(jsonConfig);
            }
            catch (JsonException ex)
            {
                throw new ValidationException("Invalid JSON configuration format", ex);
            }
        }
    }
}
```

#### 5.1.2 Update EInvoice Setting Status

```csharp
namespace KvFnB.Modules.Invoice.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingStatusUseCase
{
    public record UpdateEInvoiceSettingStatusRequest
    {
        [Required]
        [JsonPropertyName("id")]
        [Description("The EInvoice setting ID.")]
        public long Id { get; init; }

        [Required]
        [Range(0, 1)]
        [JsonPropertyName("status")]
        [Description("The status (0=InActive, 1=Active).")]
        public int Status { get; init; }
    }

    public class UpdateEInvoiceSettingStatusUseCase : IRequestHandler<UpdateEInvoiceSettingStatusRequest, EInvoiceSettingDto>
    {
        private readonly IEInvoiceSettingRepository _repository;
        private readonly IAuthUser _authUser;
        private readonly ILogger<UpdateEInvoiceSettingStatusUseCase> _logger;

        public UpdateEInvoiceSettingStatusUseCase(
            IEInvoiceSettingRepository repository,
            IAuthUser authUser,
            ILogger<UpdateEInvoiceSettingStatusUseCase> logger)
        {
            _repository = repository;
            _authUser = authUser;
            _logger = logger;
        }

        public async Task<EInvoiceSettingDto> Handle(UpdateEInvoiceSettingStatusRequest request, CancellationToken cancellationToken)
        {
            var setting = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (setting == null)
            {
                throw new NotFoundException("EInvoice setting not found");
            }

            var status = EInvoiceStatus.From(request.Status);
            setting.UpdateStatus(status, _authUser.UserId);

            await _repository.UpdateAsync(setting, cancellationToken);

            _logger.LogInformation("Updated EInvoice setting status for ID {Id} to {Status}", 
                request.Id, status.Name);

            return setting.ToDto();
        }
    }
}
```

#### 5.1.3 Update EInvoice Setting

```csharp
namespace KvFnB.Modules.Invoice.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingUseCase
{
    public record UpdateEInvoiceSettingRequest
    {
        [Required]
        [JsonPropertyName("id")]
        [Description("The EInvoice setting ID.")]
        public long Id { get; init; }

        [Required]
        [Range(1, 4)]
        [JsonPropertyName("partner_type")]
        [Description("The EInvoice partner type (1=MISA, 2=VIETTEL, 3=VNPT, 4=KIOTVIET).")]
        public int PartnerType { get; init; }

        [JsonPropertyName("einvoice_config")]
        [Description("JSON configuration for the EInvoice provider.")]
        public string? EInvoiceConfig { get; init; }
    }

    public class UpdateEInvoiceSettingUseCase : IRequestHandler<UpdateEInvoiceSettingRequest, EInvoiceSettingDto>
    {
        private readonly IEInvoiceSettingRepository _repository;
        private readonly IAuthUser _authUser;
        private readonly ILogger<UpdateEInvoiceSettingUseCase> _logger;

        public UpdateEInvoiceSettingUseCase(
            IEInvoiceSettingRepository repository,
            IAuthUser authUser,
            ILogger<UpdateEInvoiceSettingUseCase> logger)
        {
            _repository = repository;
            _authUser = authUser;
            _logger = logger;
        }

        public async Task<EInvoiceSettingDto> Handle(UpdateEInvoiceSettingRequest request, CancellationToken cancellationToken)
        {
            var setting = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (setting == null)
            {
                throw new NotFoundException("EInvoice setting not found");
            }

            // Validate JSON configuration if provided
            if (!string.IsNullOrEmpty(request.EInvoiceConfig))
            {
                ValidateJsonConfiguration(request.EInvoiceConfig);
            }

            var partnerType = EInvoicePartnerType.From(request.PartnerType);
            setting.Update(partnerType, request.EInvoiceConfig, _authUser.UserId);

            await _repository.UpdateAsync(setting, cancellationToken);

            _logger.LogInformation("Updated EInvoice setting for ID {Id}", request.Id);

            return setting.ToDto();
        }

        private static void ValidateJsonConfiguration(string jsonConfig)
        {
            try
            {
                JsonDocument.Parse(jsonConfig);
            }
            catch (JsonException ex)
            {
                throw new ValidationException("Invalid JSON configuration format", ex);
            }
        }
    }
}
```

### 5.2 DTOs

```csharp
namespace KvFnB.Modules.Invoice.Application.UseCases.Contracts
{
    public class EInvoiceSettingDto
    {
        [JsonPropertyName("id"), Description("The unique identifier of the EInvoice setting")]
        public long Id { get; set; }

        [JsonPropertyName("retailer_id"), Description("The retailer ID")]
        public int RetailerId { get; set; }

        [JsonPropertyName("branch_id"), Description("The branch ID")]
        public int BranchId { get; set; }

        [JsonPropertyName("status"), Description("The status of the EInvoice setting")]
        public int Status { get; set; }

        [JsonPropertyName("einvoice_config"), Description("JSON configuration for the EInvoice provider")]
        public string? EInvoiceConfig { get; set; }

        [JsonPropertyName("partner_type"), Description("The EInvoice partner type")]
        public int PartnerType { get; set; }

        [JsonPropertyName("partner_type_name"), Description("The EInvoice partner type name")]
        public string PartnerTypeName { get; set; } = string.Empty;

        [JsonPropertyName("created_by"), Description("User who created this setting")]
        public long CreatedBy { get; set; }

        [JsonPropertyName("created_at"), Description("Creation timestamp")]
        public DateTime CreatedAt { get; set; }

        [JsonPropertyName("modified_by"), Description("User who last modified this setting")]
        public long? ModifiedBy { get; set; }

        [JsonPropertyName("modified_at"), Description("Last modification timestamp")]
        public DateTime? ModifiedAt { get; set; }
    }
}
```

## 6. Infrastructure Layer

### 6.1 Repository Implementation

```csharp
namespace KvFnB.Modules.Invoice.Infrastructure.Persistence
{
    public class EInvoiceSettingRepository : BaseRepository<EInvoiceSetting, long>, IEInvoiceSettingRepository
    {
        public EInvoiceSettingRepository(ShardingDbContext context) : base(context) { }

        public async Task<bool> ExistsAsync(int retailerId, int branchId, CancellationToken cancellationToken = default)
        {
            var spec = new EInvoiceSettingByRetailerAndBranchSpec(retailerId, branchId);
            return await AnyAsync(spec, cancellationToken);
        }

        public async Task<EInvoiceSetting?> GetByRetailerAndBranchAsync(int retailerId, int branchId, CancellationToken cancellationToken = default)
        {
            var spec = new EInvoiceSettingByRetailerAndBranchSpec(retailerId, branchId);
            return await GetAsync(spec, cancellationToken);
        }

        public async Task<List<EInvoiceSetting>> GetByRetailerAsync(int retailerId, CancellationToken cancellationToken = default)
        {
            var spec = new EInvoiceSettingByRetailerSpec(retailerId);
            return [.. (await FindAsync(spec, cancellationToken))];
        }

        public async Task<EInvoiceSetting?> GetByIdAsync(long id, CancellationToken cancellationToken = default)
        {
            var spec = new EInvoiceSettingByIdSpec(id);
            return await GetAsync(spec, cancellationToken);
        }
    }
}
```

### 6.2 Entity Configuration

```csharp
namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations
{
    public class EInvoiceSettingEntityTypeConfiguration : BaseEntityTypeConfiguration<EInvoiceSetting>
    {
        public override void Configure(EntityTypeBuilder<EInvoiceSetting> builder)
        {
            base.Configure(builder);
            
            builder.ToTable("EInvoiceSetting");
            
            builder.Property(b => b.Id)
                .UseIdentityColumn()
                .ValueGeneratedOnAdd()
                .HasColumnName("Id")
                .HasColumnType(SqlServerColumnTypes.BIGINT);

            builder.Property(x => x.RetailerId)
                .HasColumnType(SqlServerColumnTypes.INT)
                .IsRequired();

            builder.Property(x => x.BranchId)
                .HasColumnType(SqlServerColumnTypes.INT)
                .IsRequired();

            builder.Property(x => x.Status)
                .HasColumnType(SqlServerColumnTypes.TINYINT)
                .IsRequired();

            builder.Property(x => x.EInvoiceConfig)
                .HasColumnType(SqlServerColumnTypes.NVARCHARMAX)
                .IsRequired(false);

            builder.Property(x => x.PartnerType)
                .HasColumnType(SqlServerColumnTypes.INT)
                .IsRequired();

            builder.Property(x => x.CreatedBy)
                .HasColumnType(SqlServerColumnTypes.BIGINT)
                .IsRequired();

            builder.Property(x => x.CreatedAt)
                .HasColumnType(SqlServerColumnTypes.DATETIME)
                .HasDefaultValueSql("GETDATE()")
                .IsRequired();

            builder.Property(x => x.ModifiedBy)
                .HasColumnType(SqlServerColumnTypes.BIGINT)
                .IsRequired(false);

            builder.Property(x => x.ModifiedAt)
                .HasColumnType(SqlServerColumnTypes.DATETIME)
                .IsRequired(false);

            // Unique constraint
            builder.HasIndex(x => new { x.RetailerId, x.BranchId })
                .IsUnique()
                .HasDatabaseName("UK_EInvoiceSetting_RetailerId_BranchId");
        }
    }
}
```

### 6.3 Specifications

```csharp
namespace KvFnB.Modules.Invoice.Domain.Specifications.EInvoiceSetting
{
    public class EInvoiceSettingByRetailerAndBranchSpec : Specification<EInvoiceSetting>
    {
        public EInvoiceSettingByRetailerAndBranchSpec(int retailerId, int branchId)
        {
            Query.Where(x => x.RetailerId == retailerId && x.BranchId == branchId);
        }
    }

    public class EInvoiceSettingByRetailerSpec : Specification<EInvoiceSetting>
    {
        public EInvoiceSettingByRetailerSpec(int retailerId)
        {
            Query.Where(x => x.RetailerId == retailerId);
        }
    }

    public class EInvoiceSettingByIdSpec : Specification<EInvoiceSetting>
    {
        public EInvoiceSettingByIdSpec(long id)
        {
            Query.Where(x => x.Id == id);
        }
    }
}
```

## 7. API Layer

### 7.1 Controller

```csharp
namespace KvFnB.PartnerAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EInvoiceSettingsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<EInvoiceSettingsController> _logger;

        public EInvoiceSettingsController(IMediator mediator, ILogger<EInvoiceSettingsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Create a new EInvoice setting
        /// </summary>
        /// <param name="request">The EInvoice setting creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created EInvoice setting</returns>
        [HttpPost]
        [ProducesResponseType(typeof(EInvoiceSettingDto), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status409Conflict)]
        public async Task<ActionResult<EInvoiceSettingDto>> CreateEInvoiceSetting(
            [FromBody] CreateEInvoiceSettingRequest request,
            CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(request, cancellationToken);
            return CreatedAtAction(nameof(GetEInvoiceSetting), new { id = result.Id }, result);
        }

        /// <summary>
        /// Update EInvoice setting status
        /// </summary>
        /// <param name="id">The EInvoice setting ID</param>
        /// <param name="request">The status update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated EInvoice setting</returns>
        [HttpPatch("{id}/status")]
        [ProducesResponseType(typeof(EInvoiceSettingDto), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        public async Task<ActionResult<EInvoiceSettingDto>> UpdateEInvoiceSettingStatus(
            long id,
            [FromBody] UpdateEInvoiceSettingStatusRequest request,
            CancellationToken cancellationToken)
        {
            if (id != request.Id)
            {
                return BadRequest("ID mismatch");
            }

            var result = await _mediator.Send(request, cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Update EInvoice setting
        /// </summary>
        /// <param name="id">The EInvoice setting ID</param>
        /// <param name="request">The update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated EInvoice setting</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(EInvoiceSettingDto), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        public async Task<ActionResult<EInvoiceSettingDto>> UpdateEInvoiceSetting(
            long id,
            [FromBody] UpdateEInvoiceSettingRequest request,
            CancellationToken cancellationToken)
        {
            if (id != request.Id)
            {
                return BadRequest("ID mismatch");
            }

            var result = await _mediator.Send(request, cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Get EInvoice setting by ID
        /// </summary>
        /// <param name="id">The EInvoice setting ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The EInvoice setting</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(EInvoiceSettingDto), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        public async Task<ActionResult<EInvoiceSettingDto>> GetEInvoiceSetting(
            long id,
            CancellationToken cancellationToken)
        {
            var request = new GetEInvoiceSettingByIdRequest { Id = id };
            var result = await _mediator.Send(request, cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Get EInvoice settings by retailer
        /// </summary>
        /// <param name="retailerId">The retailer ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of EInvoice settings</returns>
        [HttpGet("retailer/{retailerId}")]
        [ProducesResponseType(typeof(List<EInvoiceSettingDto>), StatusCodes.Status200OK)]
        public async Task<ActionResult<List<EInvoiceSettingDto>>> GetEInvoiceSettingsByRetailer(
            int retailerId,
            CancellationToken cancellationToken)
        {
            var request = new GetEInvoiceSettingsByRetailerRequest { RetailerId = retailerId };
            var result = await _mediator.Send(request, cancellationToken);
            return Ok(result);
        }
    }
}
```

## 8. Business Rules

### 8.1 Core Business Rules

1. **Unique Configuration per Branch**: Each branch can have only one EInvoice setting configuration
2. **No Physical Deletion**: EInvoice settings cannot be deleted, only deactivated
3. **Provider Validation**: Only supported partner types (MISA, VIETTEL, VNPT, KIOTVIET) are allowed
4. **JSON Configuration Validation**: EInvoice configuration must be valid JSON format
5. **Audit Trail**: All changes must be tracked with user information and timestamps

### 8.2 Validation Rules

1. **RetailerId**: Must be greater than 0
2. **BranchId**: Must be greater than 0
3. **PartnerType**: Must be between 1-4 (inclusive)
4. **Status**: Must be 0 (InActive) or 1 (Active)
5. **EInvoiceConfig**: Must be valid JSON if provided

## 9. Error Handling

### 9.1 Business Exceptions

```csharp
public class EInvoiceSettingAlreadyExistsException : BusinessException
{
    public EInvoiceSettingAlreadyExistsException(int retailerId, int branchId)
        : base($"EInvoice setting already exists for retailer {retailerId}, branch {branchId}")
    {
    }
}

public class EInvoiceSettingNotFoundException : NotFoundException
{
    public EInvoiceSettingNotFoundException(long id)
        : base($"EInvoice setting with ID {id} not found")
    {
    }
}

public class InvalidEInvoiceConfigurationException : ValidationException
{
    public InvalidEInvoiceConfigurationException(string message, Exception? innerException = null)
        : base($"Invalid EInvoice configuration: {message}", innerException)
    {
    }
}
```

## 10. Testing Strategy

### 10.1 Unit Tests

- Domain entity behavior tests
- Use case logic tests
- Repository specification tests
- Validation rule tests

### 10.2 Integration Tests

- Database operations tests
- API endpoint tests
- End-to-end workflow tests

### 10.3 Test Scenarios

1. **Create EInvoice Setting**
   - Valid creation with all required fields
   - Creation with optional JSON configuration
   - Duplicate creation attempt (should fail)
   - Invalid partner type (should fail)
   - Invalid JSON configuration (should fail)

2. **Update Status**
   - Valid status update
   - Update non-existent setting (should fail)
   - Invalid status value (should fail)

3. **Update Configuration**
   - Valid configuration update
   - Update with invalid JSON (should fail)
   - Update non-existent setting (should fail)

## 11. Security Considerations

### 11.1 Authentication & Authorization

- All endpoints require authentication
- Users can only access settings for their authorized retailers/branches
- Role-based access control for different operations

### 11.2 Data Protection

- Sensitive configuration data should be encrypted
- Audit logs for all configuration changes
- Input validation and sanitization

## 12. Performance Considerations

### 12.1 Database Optimization

- Unique index on (RetailerId, BranchId) for fast lookups
- Consider partitioning by RetailerId for large datasets
- Implement caching for frequently accessed configurations

### 12.2 API Performance

- Implement response caching where appropriate
- Use pagination for list endpoints
- Consider async processing for bulk operations

## 13. Deployment & Migration

### 13.1 Database Migration

```sql
-- Create table migration script
CREATE TABLE EInvoiceSetting (
    Id BIGINT PRIMARY KEY IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
    RetailerId INT NOT NULL,
    BranchId INT NOT NULL,
    CreatedBy BIGINT NOT NULL, 
    ModifiedBy BIGINT NULL, 
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME NULL,
    [Status] TINYINT NOT NULL,
    EInvoiceConfig NVARCHAR(MAX) NULL,
    PartnerType INT NOT NULL,
    
    CONSTRAINT UK_EInvoiceSetting_RetailerId_BranchId UNIQUE (RetailerId, BranchId)
);

-- Create indexes
CREATE INDEX IX_EInvoiceSetting_RetailerId ON EInvoiceSetting (RetailerId);
CREATE INDEX IX_EInvoiceSetting_BranchId ON EInvoiceSetting (BranchId);
CREATE INDEX IX_EInvoiceSetting_Status ON EInvoiceSetting ([Status]);
```

### 13.2 Configuration

- Add EInvoice module to dependency injection
- Configure database connection strings
- Set up logging and monitoring

## 14. Monitoring & Logging

### 14.1 Logging Requirements

- Log all CRUD operations with user context
- Log validation failures and business rule violations
- Log performance metrics for database operations

### 14.2 Monitoring Metrics

- API response times
- Database query performance
- Error rates and types
- Configuration usage patterns

## 15. Future Enhancements

### 15.1 Potential Features

- Configuration templates for different providers
- Bulk configuration management
- Configuration validation against provider APIs
- Integration with external EInvoice services
- Configuration backup and restore functionality

### 15.2 Scalability Improvements

- Implement distributed caching
- Consider microservice architecture
- Add support for configuration versioning
- Implement event-driven architecture for configuration changes 