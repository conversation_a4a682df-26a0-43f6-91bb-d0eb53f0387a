# KvFnB RabbitMQ Integration Technical Specification

## 1. Overview

This document outlines the technical specifications for integrating RabbitMQ messaging capabilities within the KvFnB Core architecture using the KiotVietFnb.Rabbit package. The implementation follows the project's hexagonal architecture pattern and focuses primarily on publishing notification messages.

## 2. Architecture

### 2.1 Design Principles

- **Hexagonal Architecture Compliance**: Follow the port-adapter pattern with clear separation of concerns
- **Dependency Inversion**: Core domain logic depends on abstractions, not concrete implementations
- **Infrastructure Independence**: Domain logic remains independent of RabbitMQ specifics
- **Leveraging Existing Package**: Utilize KiotVietFnb.Rabbit package for RabbitMQ operations
- **Simplified Interface**: Focus on message publication for notifications

### 2.2 Component Diagram

```mermaid
graph TB
    %% Core Layer
    subgraph Core["Core Layer"]
        CoreInterfaces[Core Interfaces]
    end
    
    %% Shared Infrastructure
    subgraph Shared["Shared Infrastructure"]
        RabbitService[RabbitMessageQueueService]
        RabbitConfig[RabbitMessageQueueConfiguration]
        NotificationModels[Notification Models]
        KiotVietFnBRabbit[KiotVietFnB.RabbitMq]
    end
    
    %% Module Layer
    subgraph Modules["Module Layer"]
        UseCases[Use Cases]
        ApplicationServices[Application Services]
    end
    
    %% External Systems
    subgraph External["External Systems"]
        MessageBroker[RabbitMQ Message Broker]
    end
    
    %% Dependencies
    UseCases -->|Uses| RabbitService
    ApplicationServices -->|Uses| RabbitService
    RabbitService -->|Uses| KiotVietFnBRabbit
    RabbitService -->|Configured by| RabbitConfig
    KiotVietFnBRabbit -->|Publishes to| MessageBroker
    RabbitService -->|Creates| NotificationModels
```

### 2.3 Integration Points

1. **Shared Infrastructure Layer**: Contains the RabbitMQ adapter implementation
2. **Application Layer**: Uses the RabbitMQ service to publish notification messages
3. **External Package**: Leverages KiotVietFnB.RabbitMq for actual message publishing

## 3. Implementation Details

### 3.1 RabbitMQ Service Interface

The service interface defines the contract for sending notification messages:

```csharp
// Location: src/Shared/RabbitMq/IRabbitMessageQueueService.cs
using KvFnB.Shared.RabbitMq.Models;

namespace KvFnB.Shared.RabbitMq
{
    public interface IRabbitMessageQueueService
    {
        Task SendMessage(List<NotificationMessage> messages, CancellationToken cancellationToken = default);
    }
}
```

### 3.2 RabbitMQ Configuration Interface

The configuration interface leverages KiotVietFnB.RabbitMq's built-in configuration class:

```csharp
// Location: src/Shared/RabbitMq/IRabbitMessageQueueConfiguration.cs
using KiotVietFnB.RabbitMq;

namespace KvFnB.Shared.RabbitMq
{
    public interface IRabbitMessageQueueConfiguration
    {
        public DefaultRabbitMqConfig RabbitMq { get; set; }

        public int MaximumRetainedCapacity { get; set; }
    }
}
```

### 3.3 RabbitMQ Configuration Implementation

```csharp
// Location: src/Shared/RabbitMq/RabbitMessageQueueConfiguration.cs
using KiotVietFnB.RabbitMq;

namespace KvFnB.Shared.RabbitMq
{
    public class RabbitMessageQueueConfiguration : IRabbitMessageQueueConfiguration
    {
        public DefaultRabbitMqConfig RabbitMq { get; set; } = new DefaultRabbitMqConfig();

        public int MaximumRetainedCapacity { get; set; } = 200;
    }
}
```

### 3.4 Message Models

#### NotificationMessage

The primary message model for sending notifications:

```csharp
// Location: src/Shared/RabbitMq/Models/NotificationMessage.cs
namespace KvFnB.Shared.RabbitMq.Models
{
    public class NotificationMessage
    {
        public long UserId { get; set; }
        public int BranchId { get; set; }
        public int RetailerId { get; set; }
        public int FunctionId { get; set; }
        public int Action { get; set; }
        public string Content { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string IpSource { get; set; } = string.Empty;
        public string ClientInfo { get; set; } = string.Empty;
        public long? DocumentId { get; set; }
        public string NewDocumentId { get; set; } = string.Empty;
        public string DocumentCode { get; set; } = string.Empty;
        public long ActionByUserId { get; set; }
        public string ActionByUserName { get; set; } = string.Empty;
        public string BranchName { get; set; } = string.Empty;
        public int ToBranchId { get; set; }
        public string ToBranchName { get; set; } = string.Empty;
        public string GroupId { get; set; } = string.Empty;
        public string NotifyMessage { get; set; } = string.Empty;
        public string EventType { get; set; } = string.Empty;
        public decimal? Value { get; set; }
        public string ResponseError { get; set; } = string.Empty;
        public int Status { get; set; }
        public bool? IsSent { get; set; }
        public bool? IsSystem { get; set; }
        public bool? IsPrintedOrder { get; set; }
        public long NotifyUser { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int IndustryId { get; set; }
        public string DocumentItem { get; set; } = string.Empty;
        public string DocumentImage { get; set; } = string.Empty;
        public string TableName { get; set; } = string.Empty;
        public int? TableId { get; set; }
        public string TableGroupName { get; set; } = string.Empty;
        public string Payload { get; set; } = string.Empty;
        public string ToDevice { get; set; } = string.Empty;
        public int Shard { get; set; }
        public Guid MessageId { get; set; }
    }
}
```

#### NotificationMessageQueue

A wrapper for the notification message that includes execution context:

```csharp
// Location: src/Shared/RabbitMq/Models/NotificationMessageQueue.cs
namespace KvFnB.Shared.RabbitMq.Models
{
    public class NotificationMessageQueue
    {
        public NotificationMessage Data { get; set; } = new NotificationMessage();
        public string Key { get; set; } = string.Empty;
        public ExecutionContext Context { get; set; } = new ExecutionContext();
    }
}
```

### 3.5 RabbitMQ Service Implementation

The service implementation uses KiotVietFnB.RabbitMq's `IRabbitMqProducer` to publish messages:

```csharp
// Location: src/Shared/RabbitMq/RabbitMessageQueueService.cs
using KiotVietFnB.RabbitMq;
using KvFnB.Shared.RabbitMq.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace KvFnB.Shared.RabbitMq
{
    public class RabbitMessageQueueService : IRabbitMessageQueueService
    {
        private readonly ILogger<RabbitMessageQueueService> _logger;
        private readonly IRabbitMessageQueueConfiguration _notificationConfiguration;
        private readonly IRabbitMqProducer _rabbitMqProducer;

        public RabbitMessageQueueService(
            ILogger<RabbitMessageQueueService> logger,
            IRabbitMessageQueueConfiguration notificationConfiguration,
            IRabbitMqProducer rabbitMqProducer
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _notificationConfiguration = notificationConfiguration ?? throw new ArgumentNullException(nameof(notificationConfiguration));
            _rabbitMqProducer = rabbitMqProducer ?? throw new ArgumentNullException(nameof(rabbitMqProducer));
        }

        public async Task SendMessage(List<NotificationMessage> messages, CancellationToken cancellationToken = default)
        {
            foreach (var notiMsg in messages)
            {
                try
                {
                    var msg = new NotificationMessageQueue
                    {
                        Data = notiMsg,
                        Context = new Models.ExecutionContext()
                        {
                            User = new SessionUser()
                            {
                                Id = notiMsg.UserId,
                                UserName = notiMsg.UserName,
                                GivenName = notiMsg.UserName
                            },
                            GroupId = notiMsg.Shard,
                            BranchId = notiMsg.BranchId,
                            RetailerId = notiMsg.RetailerId
                        }
                    };

                    var msgString = JsonConvert.SerializeObject(msg,
                                        new JsonSerializerSettings()
                                        {
                                            Formatting = Formatting.None
                                        });
                    await _rabbitMqProducer.ProduceAsync(msgString, routingKey: _notificationConfiguration.RabbitMq.QueueName, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                }
            }
        }
    }
}
```

### 3.6 Dependency Injection Extensions

Sample extension method for registering RabbitMQ services:

```csharp
// Location: src/Shared/RabbitMq/Extensions/RabbitMqServiceCollectionExtensions.cs
using KiotVietFnB.RabbitMq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Shared.RabbitMq.Extensions
{
    public static class RabbitMqServiceCollectionExtensions
    {
        public static IServiceCollection AddRabbitMqServices(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Register KiotVietFnB.RabbitMq services
            services.AddRabbitMq(configuration);
            
            // Register configuration
            services.Configure<RabbitMessageQueueConfiguration>(
                configuration.GetSection("RabbitMessageQueue"));
                
            // Register configuration interface
            services.AddSingleton<IRabbitMessageQueueConfiguration>(sp =>
                sp.GetRequiredService<Microsoft.Extensions.Options.IOptions<RabbitMessageQueueConfiguration>>().Value);
                
            // Register RabbitMQ notification service
            services.AddSingleton<IRabbitMessageQueueService, RabbitMessageQueueService>();
            
            return services;
        }
    }
}
```

## 4. Configuration

### 4.1 appsettings.json Configuration

```json
{
  "RabbitMessageQueue": {
    "RabbitMq": {
      "HostName": "localhost",
      "Port": 5672,
      "VirtualHost": "/",
      "UserName": "guest",
      "Password": "guest",
      "QueueName": "kvfnb.notifications",
      "ExchangeName": "kvfnb.notifications.exchange",
      "ExchangeType": "topic",
      "RoutingKey": "notifications.#"
    },
    "MaximumRetainedCapacity": 200
  }
}
```

## 5. Usage Examples

### 5.1 Publishing Notification Messages

```csharp
// Example from an OrderCompletedUseCase
public class OrderCompletedUseCase : UseCaseBase<OrderCompletedRequest, OrderCompletedResponse>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IRabbitMessageQueueService _notificationService;
    private readonly ITenantProvider _tenantProvider;
    
    public OrderCompletedUseCase(
        IValidator<OrderCompletedRequest> validator,
        IOrderRepository orderRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IRabbitMessageQueueService notificationService,
        ITenantProvider tenantProvider)
        : base(validator)
    {
        _orderRepository = orderRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _notificationService = notificationService;
        _tenantProvider = tenantProvider;
    }
    
    public override async Task<Result<OrderCompletedResponse>> ExecuteAsync(
        OrderCompletedRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Process order completion
            var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
            if (order == null)
            {
                return Result<OrderCompletedResponse>.Failure($"Order with ID {request.OrderId} not found");
            }
            
            order.Complete();
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.CommitAsync(cancellationToken);
            
            // Send notification
            var tenantId = _tenantProvider.GetTenantId();
            var userId = _tenantProvider.GetUserId() ?? 0;
            
            var notification = new NotificationMessage
            {
                UserId = userId,
                RetailerId = (int)tenantId,
                BranchId = order.BranchId,
                FunctionId = 123, // Order function ID
                Action = 3, // Complete action
                Content = $"Order {order.Code} has been completed",
                UserName = request.CompletedByUserName,
                DocumentId = order.Id,
                DocumentCode = order.Code,
                ActionByUserId = userId,
                ActionByUserName = request.CompletedByUserName,
                BranchName = order.BranchName,
                NotifyMessage = $"Order {order.Code} has been completed",
                EventType = "OrderCompleted",
                Status = 1,
                CreatedDate = DateTime.UtcNow,
                Shard = order.Shard,
                MessageId = Guid.NewGuid()
            };
            
            await _notificationService.SendMessage(new List<NotificationMessage> { notification }, cancellationToken);
            
            // Return success response
            var response = _mapper.Map<OrderCompletedResponse>(order);
            return Result<OrderCompletedResponse>.Success(response);
        }
        catch (Exception ex)
        {
            // Handle exception
            return Result<OrderCompletedResponse>.Failure(ex.Message);
        }
    }
}
```

## 6. Error Handling

1. **Message Publishing**:
   - Each message is sent individually to isolate failures
   - Exceptions are caught and logged per message
   - Failed messages do not prevent other messages from being sent

## 7. Testing Strategy

### 7.1 Unit Testing

- Test notification message creation
- Mock IRabbitMqProducer for testing the RabbitMessageQueueService
- Verify correct serialization of messages

### 7.2 Integration Testing

- Use Docker-based RabbitMQ instance for integration tests
- Verify message publication using a test consumer
- Test with different types of notification messages

## 8. Security Considerations

1. **Authentication**: Use credential-based authentication for RabbitMQ
2. **Transport Security**: Enable TLS for connections in production
3. **Sensitive Data**: Avoid sending sensitive personal data in notification messages
4. **Message Validation**: Validate notification content before sending

## 9. Performance Considerations

1. **Connection Management**: Leverage KiotVietFnB.RabbitMq's connection handling
2. **Message Batching**: Consider implementing batch processing for large volumes
3. **Error Handling**: Ensure errors don't block processing of other messages

## 10. Conclusion

This RabbitMQ integration leverages the KiotVietFnB.RabbitMq package to provide a simple notification publishing mechanism that integrates seamlessly with the KvFnB Core hexagonal architecture. By delegating the RabbitMQ infrastructure details to the package, the implementation remains focused on the business requirements of sending notifications while maintaining separation of concerns.
