# User Notification System Project Management Rule

## Purpose
This rule defines the standardized approach for managing User Notification-related features and enhancements using the established technical specification and planning documents as authoritative references.

## Rule Activation
When you see the tag **`notify-rule`** in any request related to User Notification features, you MUST:

1. **Always read the technical specification** (`notify-technical-spec.md`)
2. **Always read the planning document** (`notify-planning.md`)
3. **Follow the established patterns and standards** documented in these files
4. **Update the documentation** after any implementation or changes

## Document Hierarchy

### 1. Technical Specification (`notify-technical-spec.md`)
**Authority**: Primary technical reference  
**Purpose**: Defines the "what" and "how" of implementation

**Contains:**
- ✅ Implementation status tracking
- ✅ Business context and notification requirements
- ✅ Data models and notification message schemas
- ✅ Technical architecture decisions
- ✅ Request/response specifications
- ✅ Validation rules and business logic
- ✅ RabbitMQ integration strategies
- ✅ Security considerations and multi-tenancy
- ✅ Performance requirements and optimization
- ✅ Testing strategies and coverage
- ✅ Dependencies and deployment considerations

### 2. Planning Document (`notify-planning.md`)
**Authority**: Project management and execution reference  
**Purpose**: Defines the "when" and "who" of implementation

**Contains:**
- ✅ Implementation phases and timeline
- ✅ Task breakdown by developer role
- ✅ Success criteria and quality gates
- ✅ Risk assessment and mitigation strategies
- ✅ Implementation decisions and lessons learned
- ✅ Future enhancement roadmap
- ✅ Post-implementation activities and maintenance

## Implementation Workflow

### Phase 1: Analysis and Planning
When starting any User Notification-related work:

1. **Read Both Documents First**
   - Review current implementation status
   - Understand established notification patterns
   - Identify relevant sections for your task
   - Note any deferred items that may be relevant

2. **Assess Impact**
   - Determine if changes affect existing notification flow
   - Identify required updates to specifications
   - Plan testing strategy based on established patterns
   - Consider RabbitMQ and performance implications

3. **Plan Updates**
   - Identify which document sections need updates
   - Plan for documentation updates alongside code changes
   - Consider impact on future roadmap items

### Phase 2: Implementation
During implementation:

1. **Follow Established Patterns**
   - Use the same architectural approaches (UseCaseBase, IQueryService, RabbitMQ integration)
   - Follow the same naming conventions and folder structures
   - Implement similar error handling and logging patterns
   - Use the same testing approaches and coverage standards

2. **Maintain Consistency**
   - Follow the same coding standards and clean code principles
   - Use similar dependency injection patterns
   - Implement similar validation strategies
   - Follow the same security and multi-tenancy patterns

3. **Document Decisions**
   - Note any deviations from established patterns
   - Document new patterns or approaches
   - Record performance considerations
   - Track any new dependencies or requirements

### Phase 3: Documentation Updates
After any implementation:

1. **Update Technical Specification**
   - ✅ Mark completed items with checkmarks
   - ✅ Update implementation status sections
   - ✅ Add new components or features
   - ✅ Update performance characteristics
   - ✅ Document any architectural changes
   - ✅ Add new dependencies or requirements
   - ✅ Update security considerations

2. **Update Planning Document**
   - ✅ Mark completed phases and tasks
   - ✅ Update implementation status
   - ✅ Document lessons learned
   - ✅ Update risk assessment based on experience
   - ✅ Adjust future roadmap based on changes
   - ✅ Update success criteria and quality gates

3. **Maintain Accuracy**
   - Ensure both documents reflect current state
   - Update cross-references between documents
   - Validate that examples and code snippets are current
   - Update file paths and component names if changed

## Mandatory Actions for notify-rule Tag

### When Implementing New Notification Features
1. **Before Starting:**
   - Read both notify-technical-spec.md and notify-planning.md
   - Identify relevant notification patterns and standards
   - Plan documentation updates alongside implementation

2. **During Implementation:**
   - Follow established RabbitMQ integration patterns
   - Use consistent naming and folder structures
   - Implement similar testing strategies
   - Maintain code quality standards

3. **After Implementation:**
   - Update technical specification with new notification components
   - Update planning document with completion status
   - Document any new patterns or lessons learned
   - Update future roadmap if applicable

### When Fixing Notification Bugs or Issues
1. **Analysis:**
   - Check if the issue is documented in known limitations
   - Review testing strategies that might have missed the issue
   - Assess if the fix affects documented notification patterns

2. **Resolution:**
   - Follow established error handling patterns
   - Maintain consistency with existing code style
   - Add appropriate test coverage following established patterns

3. **Documentation:**
   - Update technical specification if the fix changes behavior
   - Update testing strategies if new test patterns are needed
   - Document lessons learned in planning document

### When Enhancing Existing Notification Features
1. **Compatibility:**
   - Ensure changes are compatible with documented architecture
   - Maintain backward compatibility where documented
   - Follow established security and performance patterns

2. **Extension:**
   - Use consistent patterns for new notification functionality
   - Follow the same validation and error handling approaches
   - Implement similar testing coverage

3. **Documentation:**
   - Update technical specification with enhanced capabilities
   - Update planning document with completed enhancements
   - Adjust future roadmap based on changes

## Quality Assurance Requirements

### Code Quality
- ✅ Follow clean code principles documented in specifications
- ✅ Maintain established dependency injection patterns
- ✅ Use consistent error handling approaches
- ✅ Implement comprehensive input validation
- ✅ Follow security best practices

### Testing Requirements
- ✅ Achieve similar test coverage levels (95%+ for business logic)
- ✅ Follow established testing patterns and structure
- ✅ Test edge cases and boundary conditions
- ✅ Implement proper mocking strategies
- ✅ Test multi-tenancy isolation

### Documentation Standards
- ✅ Update both documents after any changes
- ✅ Maintain accuracy of status indicators
- ✅ Keep examples and code snippets current
- ✅ Update cross-references and file paths
- ✅ Document new patterns and decisions

## Pattern Inheritance

### Architectural Patterns
- **Hexagonal Architecture**: Continue using clean separation of concerns
- **Use Case Pattern**: Follow UseCaseBase inheritance for new notification use cases
- **Query Pattern**: Use IQueryService with Dapper for read operations
- **Message Queue Pattern**: Use IRabbitMessageQueueService for notification delivery
- **Validation Pattern**: Implement comprehensive validators with proper error messages
- **Result Pattern**: Use Result<T> for consistent success/failure handling

### Technical Patterns
- **RabbitMQ Integration**: Continue using established message queue patterns
- **User Query Patterns**: Use efficient SQL queries with tenant filtering
- **Notification Creation**: Follow established NotificationMessage construction patterns
- **Error Handling**: Implement per-message error handling without blocking flows
- **Logging**: Use comprehensive logging throughout notification flows

### Notification-Specific Patterns

#### Message Creation Pattern
```csharp
var notificationMessages = users.Select(user => new NotificationMessage
{
    MessageId = Guid.NewGuid(),
    BranchId = branchId,
    UserId = user.Id,
    EventType = request.EventType,
    NotifyUser = user.Id,
    NotifyMessage = request.Message,
    Content = request.Message,
    Payload = request.Payload,
    Status = 1, // NotificationState.New
    DocumentId = request.DocumentId,
    DocumentCode = request.DocumentCode,
    Shard = groupId,
    GroupId = groupId.ToString(),
    RetailerId = tenantId,
    CreatedDate = currentDateTime,
    ModifiedDate = currentDateTime,
    IndustryId = 15, // Hardcoded as per requirements
    UserName = user.UserName,
    ActionByUserName = user.UserName
}).ToList();
```

#### User Query Pattern
```csharp
var query = new QueryBuilder()
    .Select("u.Id, u.UserName, u.GivenName")
    .From("[User] as u WITH(NOLOCK)")
    .Where("u.RetailerId", "=", tenantId)
    .WhereGroup(subQuery => subQuery
        .WhereIsNull("u.IsDeleted")
        .Where("u.IsDeleted", "<>", 1, "OR")
    );

if (request.IsSendUserAdmin)
{
    query.Where("u.IsAdmin", "=", true);
} 
else
{
    query.Where("u.Id", "IN", request.UserIds);
}
```

#### Validation Pattern
```csharp
RuleFor(x => x.UserIds.Any() || x.IsSendUserAdmin)
    .NotNull("UserIds cannot be null")
    .Must(u => u, "Request must contain at least one user ID or is_send_user_admin must be true");
```

### Testing Patterns

#### Use Case Testing Pattern
```csharp
[Fact]
public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
{
    // Arrange
    var request = new SendMessageUserRequest { /* test data */ };
    var validationResult = new ValidationResult(false, new List<string> { "Error" });
    
    _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

    // Act
    var result = await _useCase.ExecuteAsync(request);

    // Assert
    Assert.False(result.IsSuccess);
    Assert.Equal(validationResult.Errors, result.Errors);
    
    // Verify service interactions
    _rabbitServiceMock.Verify(
        r => r.SendMessage(It.IsAny<List<NotificationMessage>>(), It.IsAny<CancellationToken>()),
        Times.Never);
}
```

## Notification-Specific Implementation Guidelines

### User Targeting
1. **Specific Users**: When UserIds is provided, target specific users only
2. **Admin Broadcast**: When IsSendUserAdmin is true, target all admin users in the tenant
3. **Tenant Isolation**: Always filter users by RetailerId for multi-tenancy
4. **Active Users**: Exclude deleted or inactive users from notifications

### Message Construction
1. **Unique MessageId**: Generate GUID for each notification message
2. **Tenant Context**: Include RetailerId and branch information
3. **User Context**: Include both UserId and NotifyUser (same value)
4. **Event Context**: Include EventType for categorization
5. **Document Context**: Include DocumentId and DocumentCode when available
6. **Timestamps**: Set both CreatedDate and ModifiedDate to current UTC time

### RabbitMQ Integration
1. **Per-Message Processing**: Handle each message individually with error isolation
2. **Execution Context**: Include proper session and tenant context
3. **JSON Serialization**: Use Newtonsoft.Json with consistent formatting
4. **Error Handling**: Log errors but don't block other messages
5. **Queue Routing**: Use configured routing keys for message distribution

### Performance Considerations
1. **Efficient Queries**: Use NOLOCK hints for read operations
2. **Batch Processing**: Process multiple users efficiently
3. **Connection Management**: Use proper connection pooling
4. **Memory Management**: Dispose resources properly
5. **Async Processing**: Use asynchronous operations throughout

## Security and Multi-Tenancy Requirements

### Tenant Isolation
- All user queries MUST include RetailerId filtering
- Notification messages MUST include proper tenant context
- No cross-tenant information leakage allowed
- Validate user permissions within tenant boundaries

### Input Validation
- Comprehensive request validation before processing
- Required field validation for all critical parameters
- User existence validation before sending notifications
- Sanitize all input data appropriately

### Error Handling
- Never expose sensitive information in error messages
- Log detailed errors for debugging but return generic messages
- Handle RabbitMQ connection failures gracefully
- Implement proper exception logging and monitoring

## Monitoring and Observability

### Required Metrics
- Notification success/failure rates by event type
- RabbitMQ queue depth and processing rates
- User query performance metrics
- Error rate tracking by component
- Tenant-specific notification volumes

### Logging Requirements
- Log all notification attempts with context
- Log validation failures with sanitized details
- Log RabbitMQ connection issues and retries
- Log performance metrics for optimization
- Include correlation IDs for request tracing

### Alerting Thresholds
- High error rates in notification sending
- RabbitMQ queue depth exceeding thresholds
- Database query performance degradation
- Authentication or authorization failures
- Tenant isolation violations

## Future Enhancement Guidelines

### Template System
- Design notification templates with localization support
- Implement template management and configuration
- Support dynamic template variable substitution
- Maintain backward compatibility with existing notifications

### Delivery Tracking
- Implement delivery status tracking and confirmations
- Add retry mechanisms for failed deliveries
- Create delivery analytics and reporting
- Support delivery preferences and opt-out mechanisms

### Performance Optimization
- Implement message batching for high-volume scenarios
- Add circuit breaker pattern for RabbitMQ integration
- Implement caching for frequently accessed user data
- Optimize query performance with intelligent indexing

### Analytics and Reporting
- Implement comprehensive notification analytics
- Create dashboards for notification performance monitoring
- Support business intelligence reporting
- Track user engagement with notifications

## Maintenance Procedures

### Regular Maintenance Tasks
1. **Performance Review**: Monitor and optimize query performance monthly
2. **Dependency Updates**: Update RabbitMQ client and other dependencies quarterly
3. **Documentation Review**: Review and update documentation bi-annually
4. **Security Review**: Conduct security assessments annually

### Troubleshooting Procedures
1. **RabbitMQ Issues**: Check connection strings, permissions, and queue configuration
2. **Database Issues**: Verify connection strings, user permissions, and query performance
3. **Validation Issues**: Review request models and validation rules
4. **Performance Issues**: Check query execution plans and indexing

### Support Escalation
1. **Level 1**: Configuration and basic troubleshooting
2. **Level 2**: Code-level debugging and performance analysis
3. **Level 3**: Architecture and design consultation
4. **Emergency**: Critical system failures affecting notification delivery

## Conclusion

This rule ensures consistent implementation and maintenance of the User Notification System within the KvFnB Core project. By following established patterns and maintaining comprehensive documentation, teams can efficiently develop, test, and deploy notification features while maintaining high quality and security standards.

The notification system serves as a critical component for real-time communication within the application, and adherence to these rules ensures reliable, scalable, and maintainable notification delivery across all user scenarios and tenant environments.
