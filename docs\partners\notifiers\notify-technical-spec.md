# User Notification System Technical Specification

## Overview
This document describes the technical specification for the User Notification module, specifically focusing on the **SendMessageUser** use case implementation within the KvFnB Core project.

## Implementation Status
✅ **COMPLETED** - All core components implemented and tested

## Business Context
The User Notification System enables sending real-time notifications to users through RabbitMQ message queues. The SendMessageUser use case allows sending notifications to specific users or all admin users in a branch, supporting various event types and payloads for different business scenarios.

## Architecture Overview
Following the hexagonal architecture pattern implemented in the KvFnB Core project:

- **Domain Layer**: Contains notification business rules and validation
- **Application Layer**: Contains use cases, DTOs, and notification logic
- **Infrastructure Layer**: Contains RabbitMQ integration and message queue implementations
- **Shared Layer**: Contains RabbitMQ services and notification models

## Data Model

### NotificationMessage Model Structure
```csharp
public class NotificationMessage
{
    public Guid MessageId { get; set; }
    public long UserId { get; set; }
    public int BranchId { get; set; }
    public int RetailerId { get; set; }
    public string EventType { get; set; }
    public long NotifyUser { get; set; }
    public string NotifyMessage { get; set; }
    public string Content { get; set; }
    public string Payload { get; set; }
    public int Status { get; set; }
    public long? DocumentId { get; set; }
    public string DocumentCode { get; set; }
    public int Shard { get; set; }
    public string GroupId { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public int IndustryId { get; set; }
    public string UserName { get; set; }
    public string ActionByUserName { get; set; }
    // Additional properties for comprehensive notification support
}
```

### Domain Entity Properties
- **MessageId**: Unique identifier for the notification message (GUID)
- **UserId**: Target user identifier
- **BranchId**: Associated branch identifier for context
- **RetailerId**: Tenant identifier for multi-tenancy support
- **EventType**: Type of event that triggered the notification
- **NotifyUser**: User to be notified (same as UserId in most cases)
- **NotifyMessage**: Human-readable notification message
- **Content**: Message content for display
- **Payload**: Additional JSON data payload
- **Status**: Notification status (1 = New, processed states)
- **DocumentId**: Optional reference to related document
- **DocumentCode**: Optional code of related document
- **Shard**: Shard identifier for distributed processing
- **GroupId**: Group identifier for message routing
- **CreatedDate**: Timestamp when notification was created
- **ModifiedDate**: Timestamp when notification was last modified
- **IndustryId**: Industry identifier (hardcoded to 15)
- **UserName**: Username of the target user
- **ActionByUserName**: Username of the user who triggered the notification

## Use Case: SendMessageUser

### Purpose
Send notifications to specific users or all admin users in a branch through RabbitMQ message queues, enabling real-time communication and event-driven notifications.

### Input Requirements
- **BranchId**: Branch context for the notification
- **IsSendUserAdmin**: Flag to send to all admin users instead of specific users
- **UserIds**: List of specific user IDs to notify (required if not sending to all admins)
- **EventType**: Type of event triggering the notification
- **Message**: Human-readable notification message
- **Payload**: Additional JSON data payload
- **DocumentId**: Optional reference to related document
- **DocumentCode**: Optional code of related document
- **Tenant Context**: Automatically handled via ITenantProvider
- **User Context**: Automatically handled via IUserProvider

### Output Specification
- **Success**: Boolean indicating if notifications were sent successfully
- **RecipientsCount**: Number of users that received the notification
- **Result Pattern**: Wrapped in Result<T> for consistent success/failure handling

### Business Rules
1. Must provide either specific UserIds or set IsSendUserAdmin to true
2. Message content and payload are required
3. EventType is required for notification categorization
4. Only send to users belonging to the current tenant (RetailerId)
5. Filter out deleted or inactive users
6. Support both targeted and broadcast notification modes
7. Generate unique MessageId for each notification
8. Include tenant context and user information in notifications
9. Handle notification failures gracefully without blocking the process

### Performance Considerations
- Use efficient user queries with proper indexing
- Implement asynchronous message sending through RabbitMQ
- Handle large user lists efficiently for broadcast notifications
- Use connection pooling for RabbitMQ connections
- Consider batch processing for large notification volumes
- Implement proper error handling and retry mechanisms

## Technical Implementation

### Implemented Components

#### 1. Request Model (✅ Implemented)
**File**: `src/Modules/Users/<USER>/UseCases/UserNotificationUseCase/SendMessageUser/SendMessageUserRequest.cs`

```csharp
public record SendMessageUserRequest
{
    [JsonPropertyName("branch_id"), Description("The ID of the branch")]
    public int BranchId { get; init; }

    [JsonPropertyName("is_send_user_admin"), Description("Whether to send the message to all users")]
    public bool IsSendUserAdmin { get; init; }

    [Required]
    [JsonPropertyName("user_ids"), Description("List of user IDs to send the message to")]
    public List<long> UserIds { get; init; } = new List<long>();

    [Required]
    [JsonPropertyName("event_type"), Description("Type of event that triggered this message")]
    public string EventType { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("message"), Description("The message content to be displayed to users")]
    public string Message { get; init; } = string.Empty;

    [Required]
    [JsonPropertyName("payload"), Description("Additional data payload in JSON format")]
    public string Payload { get; init; } = string.Empty;

    [JsonPropertyName("document_id"), Description("Optional ID of the document related to this message")]
    public long? DocumentId { get; init; }

    [JsonPropertyName("document_code"), Description("Optional code of the document related to this message")]
    public string DocumentCode { get; init; } = string.Empty;
}
```

#### 2. Response Model (✅ Implemented)
**File**: `src/Modules/Users/<USER>/UseCases/UserNotificationUseCase/SendMessageUser/SendMessageUserResponse.cs`

```csharp
public record SendMessageUserResponse
{
    [JsonPropertyName("success"), Description("Indicates whether the message was sent successfully")]
    public bool Success { get; init; }
    
    [JsonPropertyName("recipients_count"), Description("The number of users that the message was sent to")]
    public int RecipientsCount { get; init; }
}
```

#### 3. Validator (✅ Implemented)
**File**: `src/Modules/Users/<USER>/UseCases/UserNotificationUseCase/SendMessageUser/SendMessageUserValidator.cs`

**Validation Rules:**
1. **UserIds or IsSendUserAdmin**: Must have at least one user ID or send to all admins
2. **Message**: Required and cannot be empty
3. **Payload**: Required and cannot be empty
4. **EventType**: Required and cannot be empty

#### 4. Use Case Implementation (✅ Implemented)
**File**: `src/Modules/Users/<USER>/UseCases/UserNotificationUseCase/SendMessageUser/SendMessageUserUseCase.cs`

**Key Features:**
- Inherits from `UseCaseBase<TRequest, TResponse>`
- Uses `IQueryService` for user data retrieval with Dapper
- Integrates with `IRabbitMessageQueueService` for message sending
- Supports both targeted and broadcast notification modes
- Implements comprehensive error handling and logging
- Multi-tenant aware with proper tenant filtering

**Dependencies:**
- `IValidator<SendMessageUserRequest>`: Request validation
- `ILogger<SendMessageUserUseCase>`: Logging and diagnostics
- `IRabbitMessageQueueService`: RabbitMQ message queue integration
- `ITenantProvider`: Tenant context for multi-tenancy
- `IQueryService`: Database queries using Dapper

**Business Logic Flow:**
1. Validate the incoming request
2. Retrieve tenant context information
3. Query users based on request parameters (specific users or all admins)
4. Validate that users exist
5. Create NotificationMessage objects for each user
6. Send notifications through RabbitMQ
7. Return success response with recipient count

#### 5. RabbitMQ Integration (✅ Implemented)
**File**: `src/Shared/RabbitMq/RabbitMessageQueueService.cs`

**Key Features:**
- Implements `IRabbitMessageQueueService` interface
- Uses KiotVietFnB.RabbitMq library for message production
- Serializes notification messages to JSON
- Includes execution context with user and tenant information
- Implements per-message error handling
- Configurable queue routing through IRabbitMessageQueueConfiguration

**Configuration Dependencies:**
- `IRabbitMessageQueueConfiguration`: Queue configuration
- `IRabbitMqProducer`: Message producer interface
- `ILogger<RabbitMessageQueueService>`: Service logging

#### 6. Data Models (✅ Implemented)
**NotificationMessage Model**: `src/Shared/RabbitMq/Models/NotificationMessage.cs`
**NotificationMessageQueue Model**: `src/Shared/RabbitMq/Models/NotificationMessageQueue.cs`
**ExecutionContext Model**: `src/Shared/RabbitMq/Models/ExecutionContext.cs`
**SessionUser Model**: `src/Shared/RabbitMq/Models/SessionUser.cs`

## Integration Points

### Database Integration
- **User Table**: Query active users by IDs or admin status
- **Multi-tenancy**: Filter by RetailerId for tenant isolation
- **Soft Delete**: Exclude deleted users from notifications

### RabbitMQ Integration
- **Message Queue**: Send notifications through configured queue
- **Routing**: Use configurable routing keys for message distribution
- **Serialization**: JSON serialization with Newtonsoft.Json
- **Error Handling**: Per-message error handling with logging

### External Dependencies
- **KiotVietFnB.RabbitMq**: Core RabbitMQ producer functionality
- **Dapper**: Database queries for user retrieval
- **Newtonsoft.Json**: Message serialization

## Security Considerations

### Multi-tenancy
- All user queries are filtered by tenant ID (RetailerId)
- Notifications are scoped to the requesting tenant
- No cross-tenant information leakage

### Input Validation
- Comprehensive request validation before processing
- Required field validation for critical parameters
- User existence validation before sending notifications

### Error Handling
- Sensitive information is not exposed in error messages
- Failed notifications are logged but don't block the process
- Graceful handling of RabbitMQ connection issues

## Performance Characteristics

### Query Performance
- Efficient user queries with proper indexing on User table
- Parameterized queries to prevent SQL injection
- NOLOCK hints for read operations to reduce blocking

### Message Processing
- Asynchronous message sending through RabbitMQ
- Individual message processing with error isolation
- Connection pooling and resource management

### Scalability
- Supports batch processing for multiple users
- Distributed message processing through RabbitMQ
- Tenant-aware sharding support

## Testing Strategy

### Unit Tests (✅ Implemented)
**File**: `test/Modules/Users.Tests/Application/UseCases/UserNotificationUseCase/SendMessage/SendMessageUseCaseTests.cs`

**Test Coverage:**
- Validation failure scenarios
- No users found scenarios
- Successful notification sending
- Exception handling
- Service interaction verification
- Multi-tenancy isolation
- Parameter validation

**Mock Dependencies:**
- `IValidator<SendMessageUserRequest>`
- `ILogger<SendMessageUserUseCase>`
- `IRabbitMessageQueueService`
- `ITenantProvider`
- `IQueryService`

### Integration Considerations
- RabbitMQ connectivity testing
- Database query performance testing
- End-to-end notification flow testing
- Multi-tenant scenario testing

## Deployment Considerations

### Configuration Requirements
- RabbitMQ connection strings and queue configuration
- Database connection strings for user queries
- Tenant provider configuration
- Logging configuration

### Dependencies
- RabbitMQ server availability
- Database connectivity
- KiotVietFnB.RabbitMq library
- Proper queue permissions and routing

### Monitoring
- Notification success/failure rates
- RabbitMQ queue depth monitoring
- User query performance metrics
- Error rate tracking

## Future Enhancements

### Planned Features
- Notification templates and localization
- Delivery status tracking and confirmations
- Message priority and scheduling
- Notification preferences and opt-out mechanisms
- Analytics and reporting capabilities

### Technical Improvements
- Message batching for high-volume scenarios
- Circuit breaker pattern for RabbitMQ integration
- Caching for frequently accessed user data
- Dead letter queue handling for failed messages
- Metric collection and performance monitoring

## Error Handling Strategy

### Validation Errors
- Return detailed validation error messages
- Prevent processing with invalid input
- Log validation failures for monitoring

### Business Logic Errors
- Handle user not found scenarios gracefully
- Provide meaningful error messages
- Continue processing for individual failures

### Infrastructure Errors
- Graceful RabbitMQ connection failure handling
- Database connectivity issue management
- Retry mechanisms for transient failures
- Comprehensive error logging

## API Integration

### Controller Integration
**Endpoint**: `/api/notifications/send-message-user`
**Method**: POST
**Authorization**: Required
**Content-Type**: application/json

### Request/Response Examples

**Request:**
```json
{
  "branch_id": 1,
  "is_send_user_admin": false,
  "user_ids": [1, 2, 3],
  "event_type": "ORDER_CREATED",
  "message": "New order has been created",
  "payload": "{\"order_id\": 12345, \"amount\": 150.00}",
  "document_id": 12345,
  "document_code": "ORD-12345"
}
```

**Response:**
```json
{
  "success": true,
  "recipients_count": 3
}
```

## Conclusion

The User Notification System provides a robust, scalable solution for real-time user notifications within the KvFnB Core project. The implementation follows hexagonal architecture principles, provides comprehensive validation and error handling, and integrates seamlessly with RabbitMQ for reliable message delivery.

The system supports both targeted and broadcast notification scenarios, maintains strict multi-tenancy isolation, and provides comprehensive logging and monitoring capabilities for production operations.
