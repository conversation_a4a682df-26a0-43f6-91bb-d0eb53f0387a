# Partner API Technical Specification

## Overview
This document defines the global technical standards, patterns, and conventions for all Partner API implementations within the KvFnB Core project. All partner-facing endpoints must follow these established patterns to ensure consistency, security, and maintainability.

## Implementation Status
✅ **ACTIVE** - Global standards for all Partner API development

## Architecture Overview
The Partner API follows hexagonal architecture principles with clean separation of concerns:

- **Presentation Layer**: Controllers in `src/Presentation/PartnerAPI/Controllers/`
- **Application Layer**: Use cases and business logic in respective modules
- **Infrastructure Layer**: Data access, external services, and technical concerns
- **Core Layer**: Shared abstractions, contracts, and domain logic

## Global API Conventions

### Routing Standards (✅ MANDATORY)

#### Controller Route Convention
```csharp
[ApiController]
[Route("resource-name")]  // NO "api/" prefix
[Authorize]
public class ResourceController : ControllerBase
```

**Key Rules:**
- ✅ **No API Prefix**: Never use `[Route("api/resource")]` - always use `[Route("resource")]`
- ✅ **Kebab Case**: Use kebab-case for route names (e.g., `payslip-payments`, `bank-accounts`)
- ✅ **Plural Resources**: Use plural nouns for resource names
- ✅ **Clean URLs**: Final URLs are clean without prefixes (e.g., `/payslip-payments/by-ids`)

#### Action Route Patterns
```csharp
// Collection operations
[HttpGet]                    // GET /resources
[HttpPost]                   // POST /resources

// Item operations  
[HttpGet("{id}")]           // GET /resources/{id}
[HttpPut("{id}")]           // PUT /resources/{id}
[HttpDelete("{id}")]        // DELETE /resources/{id}

// Custom operations
[HttpPost("by-ids")]        // POST /resources/by-ids
[HttpPost("bulk-update")]   // POST /resources/bulk-update
```

### Authorization Standards (✅ MANDATORY)

#### Required Using Statement
```csharp
using KvFnB.Shared.Filters;  // NEVER use Microsoft.AspNetCore.Authorization
```

#### Authorization Attributes
```csharp
[Authorize]  // Uses KvFnB.Shared.Filters for enhanced functionality
```

**Benefits of KvFnB.Shared.Filters:**
- ✅ Built-in tenant isolation and security
- ✅ Consistent authorization behavior across controllers
- ✅ Centralized authorization logic and customizations
- ✅ Enhanced JWT token validation and user context

### API Documentation Standards (✅ MANDATORY)

#### Required Using Statements
```csharp
using Swashbuckle.AspNetCore.Annotations;
```

#### SwaggerOperation Pattern
```csharp
[SwaggerOperation(
    Summary = "Brief description of the operation",
    Description = "Detailed explanation including behavior and edge cases",
    OperationId = "UniqueOperationId",
    Tags = new[] { "ResourceCategory" })]
```

#### SwaggerResponse Pattern
```csharp
[SwaggerResponse(StatusCodes.Status200OK, "Success description", typeof(ResponseType))]
[SwaggerResponse(StatusCodes.Status400BadRequest, "Validation or request error")]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "Authentication required")]
[SwaggerResponse(StatusCodes.Status403Forbidden, "Insufficient permissions")]
[SwaggerResponse(StatusCodes.Status500InternalServerError, "Internal server error")]
```

### Error Handling Standards (✅ MANDATORY)

#### Standard Error Response Pattern
```csharp
public async Task<IActionResult> Action([FromBody] RequestType request)
{
    var result = await _useCase.ExecuteAsync(request);

    if (!result.IsSuccess)
    {
        return BadRequest(result.ErrorMessage);  // Consistent error format
    }

    return Ok(result.Value);
}
```

#### HTTP Status Code Usage
- **200 OK**: Successful operations with data
- **400 Bad Request**: Validation errors, malformed requests
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Unexpected server errors

### Request/Response Model Standards

#### JSON Serialization Pattern
```csharp
public record RequestModel
{
    [Required]
    [JsonPropertyName("snake_case_property")]
    [Description("Clear description of the property")]
    public string Property { get; init; } = string.Empty;
}
```

#### Response Model Pattern
```csharp
public record ResponseModel
{
    [JsonPropertyName("data")]
    [Description("The main response data")]
    public List<DataDto> Data { get; init; } = [];

    [JsonPropertyName("total")]
    [Description("Total number of records")]
    public int Total { get; init; }
}
```

**Key Standards:**
- ✅ Use `record` types for immutability
- ✅ Use `[JsonPropertyName("snake_case")]` for consistent JSON format
- ✅ Add `[Description()]` attributes for API documentation
- ✅ Use `[Required]` for mandatory fields
- ✅ Initialize collections and strings with default values

### Dependency Injection Standards

#### Controller Dependency Pattern
```csharp
public class ResourceController : ControllerBase
{
    private readonly ResourceUseCase _useCase;

    public ResourceController(ResourceUseCase useCase)
    {
        _useCase = useCase ?? throw new ArgumentNullException(nameof(useCase));
    }
}
```

#### Registration Pattern
```csharp
// In ModuleRegistrar.cs
public static void AddResourceModule(this IServiceCollection services)
{
    // Register use cases
    services.AddScoped<ResourceUseCase>();
    
    // Register validators
    services.AddScoped<IValidator<ResourceRequest>, ResourceValidator>();
}
```

### OpenAPI Configuration

#### Controller Inclusion Pattern
**File**: `src/Presentation/PartnerAPI/Filters/OpenApiControllerProvider.cs`

```csharp
protected override bool IsController(TypeInfo typeInfo)
{
    var includedControllers = new[] {
        "BankAccountController",
        "EWalletController",
        "PayslipPaymentController",
        // Add new controllers here
    };
    return includedControllers.Contains(typeInfo.Name);
}
```

**Process:**
1. ✅ Create new controller following naming convention
2. ✅ Add controller name to `includedControllers` array
3. ✅ Verify controller appears in Swagger UI
4. ✅ Test API documentation generation

## Security Standards

### Multi-Tenancy (✅ MANDATORY)
- ✅ All operations must be tenant-isolated
- ✅ Use `ITenantProvider` for tenant context
- ✅ Never expose cross-tenant data
- ✅ Filter all queries by tenant automatically

### Authentication & Authorization
- ✅ All endpoints require `[Authorize]` attribute
- ✅ Use JWT Bearer token authentication
- ✅ Implement proper user context via `IUserProvider`
- ✅ Log security events for audit trails

### Data Protection
- ✅ Use parameterized queries to prevent SQL injection
- ✅ Validate all input data thoroughly
- ✅ Sanitize output data appropriately
- ✅ Implement proper CORS policies

## Performance Standards

### Response Time Requirements
- ✅ **Target**: < 200ms for simple operations
- ✅ **Maximum**: < 500ms for complex operations
- ✅ **Bulk Operations**: < 1000ms for batch processing

### Query Optimization
- ✅ Use `IQueryService` with Dapper for read operations
- ✅ Implement efficient parameterized queries
- ✅ Use `WITH(NOLOCK)` for read operations when appropriate
- ✅ Limit result set sizes (max 100 items per request)

### Resource Management
- ✅ Implement proper disposal patterns
- ✅ Use cancellation tokens for async operations
- ✅ Avoid memory leaks in long-running operations
- ✅ Monitor resource usage and optimize

## Testing Standards

### Controller Testing Requirements
- ✅ Test all HTTP status codes
- ✅ Test authentication and authorization
- ✅ Test request validation scenarios
- ✅ Test error handling paths
- ✅ Verify response formats and content

### API Integration Testing
- ✅ Test complete request/response cycles
- ✅ Verify tenant isolation
- ✅ Test concurrent request handling
- ✅ Validate performance requirements

## Documentation Requirements

### Controller Documentation
```csharp
/// <summary>
/// Clear, concise description of the endpoint purpose
/// </summary>
/// <param name="request">Description of request parameters</param>
/// <returns>Description of response data</returns>
[HttpPost("action")]
[SwaggerOperation(/* detailed operation info */)]
public async Task<IActionResult> Action([FromBody] RequestType request)
```

### API Documentation Standards
- ✅ Comprehensive Swagger/OpenAPI specifications
- ✅ Clear operation summaries and descriptions
- ✅ Complete request/response examples
- ✅ Error scenario documentation

## Deployment Considerations

### Configuration Management
- ✅ Environment-specific settings via appsettings
- ✅ Secure connection string management
- ✅ Proper logging configuration
- ✅ Feature flag support when needed

### Monitoring & Observability
- ✅ Structured logging with context
- ✅ Performance metrics collection
- ✅ Error tracking and alerting
- ✅ Health check endpoints

## Compliance & Validation

### Code Quality Requirements
- ✅ Follow clean code principles
- ✅ Implement comprehensive error handling
- ✅ Use consistent naming conventions
- ✅ Maintain proper separation of concerns

### Pattern Validation Checklist
- ✅ Routes follow kebab-case without "api/" prefix
- ✅ Authorization uses `KvFnB.Shared.Filters`
- ✅ SwaggerOperation and SwaggerResponse attributes present
- ✅ Request/response models use proper JSON serialization
- ✅ Error handling follows Result<T> pattern
- ✅ Controller included in OpenApiControllerProvider
- ✅ Dependencies properly registered in ModuleRegistrar

## Future Evolution

### Planned Enhancements
- 🔄 API versioning strategy
- 🔄 Rate limiting implementation
- 🔄 Advanced caching strategies
- 🔄 Enhanced monitoring and alerting

### Breaking Change Policy
- 🔄 Backward compatibility requirements
- 🔄 Migration strategies for existing endpoints
- 🔄 Communication protocols for API changes

---

**Note**: This specification is a living document that evolves with the Partner API implementation. All new partner-facing features must comply with these standards. Deviations require architectural review and documentation updates.

**Last Updated**: Partner API patterns standardization  
**Next Review**: When new global patterns are established  
**Version**: 1.0 - Initial global specification
