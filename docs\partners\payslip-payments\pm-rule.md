# PayslipPayment Project Management Rule

## Purpose
This rule defines the standardized approach for managing PayslipPayment-related features and enhancements using the established technical specification and planning documents as authoritative references.

## Rule Activation
When you see the tag **`pm-rule`** in any request related to PayslipPayment features, you MUST:

1. **Always read the technical specification** (`pm-technical-spec.md`)
2. **Always read the planning document** (`pm-planning.md`)
3. **Always read the partner API specification** (`partner-technical-spec.md`)
4. **Follow the established patterns and standards** documented in these files
5. **Update the documentation** after any implementation or changes

## Document Hierarchy

### 1. Partner API Specification (`partner-technical-spec.md`)
**Authority**: Global Partner API standards and patterns  
**Purpose**: Defines universal "how" for all Partner API implementations

**Contains:**
- ✅ Global API routing and authorization standards
- ✅ Mandatory authentication and security patterns
- ✅ API documentation requirements (Swagger/OpenAPI)
- ✅ Request/response model standards
- ✅ Error handling conventions
- ✅ Performance and testing requirements
- ✅ Deployment and monitoring guidelines

### 2. Technical Specification (`pm-technical-spec.md`)
**Authority**: PayslipPayment-specific technical reference  
**Purpose**: Defines the "what" and "how" of PayslipPayment implementation

**Contains:**
- ✅ Implementation status tracking
- ✅ Business context and requirements
- ✅ Data models and database schemas
- ✅ Technical architecture decisions
- ✅ Request/response specifications
- ✅ Validation rules and business logic
- ✅ Error handling strategies
- ✅ Security considerations
- ✅ Performance requirements
- ✅ Testing strategies
- ✅ Dependencies and deployment considerations

### 3. Planning Document (`pm-planning.md`)
**Authority**: Project management and execution reference  
**Purpose**: Defines the "when" and "who" of implementation

**Contains:**
- ✅ Implementation phases and timeline
- ✅ Task breakdown by developer role
- ✅ Success criteria and quality gates
- ✅ Risk assessment and mitigation strategies
- ✅ Implementation decisions and lessons learned
- ✅ Future enhancement roadmap
- ✅ Post-implementation activities

## Implementation Workflow

### Phase 1: Analysis and Planning
When starting any PayslipPayment-related work:

1. **Read All Documents First**
   - Review partner API specification for global standards compliance
   - Review current implementation status in technical specification
   - Review planning document for project context
   - Understand established patterns across all documents
   - Identify relevant sections for your task
   - Note any deferred items that may be relevant

2. **Assess Impact**
   - Ensure compliance with Partner API global standards
   - Determine if changes affect existing implementation
   - Identify required updates to specifications
   - Plan testing strategy based on established patterns
   - Consider security and performance implications

3. **Plan Updates**
   - Verify adherence to Partner API routing and authorization standards
   - Identify which document sections need updates
   - Plan for documentation updates alongside code changes
   - Consider impact on future roadmap items

### Phase 2: Implementation
During implementation:

1. **Follow Global Partner API Standards**
   - Use correct routing patterns (no "api/" prefix)
   - Use KvFnB.Shared.Filters for authorization
   - Implement proper SwaggerOperation and SwaggerResponse attributes
   - Follow established JSON serialization patterns
   - Ensure controller is included in OpenApiControllerProvider

2. **Follow PayslipPayment-Specific Patterns**
   - Use the same architectural approaches (IQueryService, manual mapping, etc.)
   - Follow the same naming conventions and folder structures
   - Implement similar error handling and logging patterns
   - Use the same testing approaches and coverage standards

3. **Maintain Consistency**
   - Follow Partner API documentation standards
   - Follow the same coding standards and clean code principles
   - Use similar dependency injection patterns
   - Implement similar validation strategies
   - Follow the same security and multi-tenancy patterns

### Phase 3: Documentation Updates
After any implementation:

1. **Update Technical Specification**
   - ✅ Mark completed items with checkmarks
   - ✅ Update implementation status sections
   - ✅ Add new components or features
   - ✅ Update performance characteristics
   - ✅ Document any architectural changes
   - ✅ Add new dependencies or requirements
   - ✅ Update security considerations

2. **Update Planning Document**
   - ✅ Mark completed phases and tasks
   - ✅ Update implementation status
   - ✅ Document lessons learned
   - ✅ Update risk assessment based on experience
   - ✅ Adjust future roadmap based on changes
   - ✅ Update success criteria and quality gates

3. **Maintain Accuracy**
   - Ensure both documents reflect current state
   - Update cross-references between documents
   - Validate that examples and code snippets are current
   - Update file paths and component names if changed

## Mandatory Actions for pm-rule Tag

### When Implementing New Features
1. **Before Starting:**
   - Read partner-technical-spec.md for global Partner API standards
   - Read both pm-technical-spec.md and pm-planning.md for PayslipPayment context
   - Identify relevant patterns and standards from all documents
   - Plan documentation updates alongside implementation

2. **During Implementation:**
   - Follow Partner API routing standards (no "api/" prefix)
   - Use KvFnB.Shared.Filters for authorization
   - Implement comprehensive Swagger documentation
   - Follow established architectural patterns
   - Use consistent naming and folder structures
   - Implement similar testing strategies
   - Maintain code quality standards

3. **After Implementation:**
   - Update partner-technical-spec.md if new global patterns emerge
   - Update technical specification with new components
   - Update planning document with completion status
   - Document any new patterns or lessons learned
   - Update future roadmap if applicable

### When Fixing Bugs or Issues
1. **Analysis:**
   - Check if the issue relates to Partner API standard violations
   - Check if the issue is documented in known limitations
   - Review testing strategies that might have missed the issue
   - Assess if the fix affects documented patterns

2. **Resolution:**
   - Ensure fix maintains Partner API standards compliance
   - Follow established error handling patterns
   - Maintain consistency with existing code style
   - Add appropriate test coverage following established patterns

3. **Documentation:**
   - Update partner-technical-spec.md if global patterns change
   - Update technical specification if the fix changes behavior
   - Update testing strategies if new test patterns are needed
   - Document lessons learned in planning document

### When Enhancing Existing Features
1. **Compatibility:**
   - Ensure changes are compatible with documented architecture
   - Maintain backward compatibility where documented
   - Follow established security and performance patterns

2. **Extension:**
   - Use consistent patterns for new functionality
   - Follow the same validation and error handling approaches
   - Implement similar testing coverage

3. **Documentation:**
   - Update technical specification with enhanced capabilities
   - Update planning document with completed enhancements
   - Adjust future roadmap based on changes

## Quality Assurance Requirements

### Code Quality
- ✅ Follow clean code principles documented in specifications
- ✅ Maintain established dependency injection patterns
- ✅ Use consistent error handling approaches
- ✅ Implement comprehensive input validation
- ✅ Follow security best practices

### Testing Requirements
- ✅ Achieve similar test coverage levels (100% for business logic)
- ✅ Follow established testing patterns and structure
- ✅ Test edge cases and boundary conditions
- ✅ Implement proper mocking strategies
- ✅ Test multi-tenancy isolation

### Documentation Standards
- ✅ Update both documents after any changes
- ✅ Maintain accuracy of status indicators
- ✅ Keep examples and code snippets current
- ✅ Update cross-references and file paths
- ✅ Document new patterns and decisions

## Pattern Inheritance

### Architectural Patterns
- **Hexagonal Architecture**: Continue using clean separation of concerns
- **Use Case Pattern**: Follow UseCaseBase inheritance for new use cases
- **Query Pattern**: Use IQueryService with Dapper for read operations
- **Validation Pattern**: Implement comprehensive validators with proper error messages
- **Result Pattern**: Use Result<T> for consistent success/failure handling

### Technical Patterns
- **Manual Mapping**: Continue using manual mapping for full control
- **Tenant Filtering**: Always implement automatic tenant isolation
- **Structured Logging**: Use consistent logging with context information
- **Parameterized Queries**: Always use parameterized queries for security
- **Error Handling**: Follow standardized error response patterns

### Testing Patterns
- **Unit Test Structure**: Follow established test organization
- **Mock Strategy**: Use dependency injection friendly mocking
- **Test Coverage**: Maintain comprehensive coverage standards
- **Edge Case Testing**: Test boundary conditions and error scenarios
- **Performance Testing**: Consider query optimization in tests

## Exception Handling

### When Patterns Don't Apply
If established patterns don't fit new requirements:

1. **Document the Exception**
   - Clearly explain why existing patterns don't apply
   - Document the alternative approach and rationale
   - Update technical specification with new patterns

2. **Maintain Consistency**
   - Keep the alternative approach consistent within its scope
   - Consider if the new pattern should become standard
   - Update planning document with pattern evolution

3. **Future Consideration**
   - Evaluate if existing code should adopt new patterns
   - Plan migration strategy if beneficial
   - Update roadmap with pattern unification tasks

### When Requirements Change
If business requirements change significantly:

1. **Update Specifications**
   - Revise technical specification to reflect new requirements
   - Update success criteria and quality gates
   - Adjust performance and security considerations

2. **Reassess Implementation**
   - Evaluate if existing implementation meets new requirements
   - Plan necessary changes using established patterns
   - Update testing strategies for new requirements

3. **Update Planning**
   - Adjust roadmap based on new requirements
   - Update risk assessment for changed scope
   - Revise success criteria and completion definitions

## Success Metrics

### Implementation Success
- ✅ All code follows documented patterns and standards
- ✅ Test coverage meets established standards
- ✅ Performance meets documented requirements
- ✅ Security considerations are properly addressed
- ✅ Documentation is updated and accurate

### Documentation Success
- ✅ Technical specification reflects current implementation
- ✅ Planning document shows accurate completion status
- ✅ New patterns are properly documented
- ✅ Cross-references are accurate and current
- ✅ Examples and code snippets are up-to-date

### Process Success
- ✅ pm-rule workflow was followed completely
- ✅ Both documents were consulted before implementation
- ✅ Established patterns were followed or exceptions documented
- ✅ Documentation was updated after implementation
- ✅ Quality standards were maintained

## Continuous Improvement

### Regular Reviews
- Review documentation accuracy quarterly
- Assess pattern effectiveness and evolution
- Update roadmap based on implementation experience
- Validate that success criteria remain relevant

### Pattern Evolution
- Document new patterns that emerge from implementation
- Evaluate existing patterns for improvement opportunities
- Consider technology updates that might affect patterns
- Plan migration strategies for pattern improvements

### Knowledge Management
- Use documents as training resources for new team members
- Reference documents in code reviews and design discussions
- Keep documents as living references that evolve with implementation
- Use completion status as project tracking mechanism

## Enforcement

This rule is **mandatory** for all PayslipPayment-related work. The pm-rule tag triggers automatic compliance with this workflow. Non-compliance may result in:

- Inconsistent implementation that doesn't follow established patterns
- Incomplete or inaccurate documentation
- Technical debt from deviating from proven approaches
- Reduced code maintainability and team productivity
- Security or performance issues from not following established safeguards

## Rule Evolution

This rule document should be updated when:
- New patterns are established that should become standard
- Implementation experience suggests workflow improvements
- Technology changes affect the documented approaches
- Team structure changes affect the documented roles and responsibilities

**Last Updated**: Current implementation completion  
**Next Review**: When new PayslipPayment features are planned  
**Version**: 1.0 - Initial rule establishment
