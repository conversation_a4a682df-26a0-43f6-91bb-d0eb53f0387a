# DomainEventDispatcher Technical Specification

## Overview

The `DomainEventDispatcher` is a comprehensive implementation of the `IDomainEventDispatcher` interface that follows hexagonal architecture and DDD principles. It automatically discovers and executes domain event handlers using dependency injection.

## Registration

To use the `DomainEventDispatcher`, register it in your dependency injection container:

```csharp
// In your service configuration
services.AddDomainEventDispatcher();

// Also register your domain event handlers
services.AddScoped<IDomainEventHandler<ProductDeletedEvent>, ProductDeletedEventHandler>();
services.AddScoped<IDomainEventHandler<OrderCreatedEvent>, OrderCreatedEventHandler>();
```

## Usage Examples

### 1. Basic Event Dispatching in UseCase

```csharp
public class DeleteProductUseCase
{
    private readonly IDomainEventDispatcher _eventDispatcher;
    private readonly IProductRepository _productRepository;
    private readonly ILogger<DeleteProductUseCase> _logger;

    public DeleteProductUseCase(
        IDomainEventDispatcher eventDispatcher,
        IProductRepository productRepository,
        ILogger<DeleteProductUseCase> logger)
    {
        _eventDispatcher = eventDispatcher;
        _productRepository = productRepository;
        _logger = logger;
    }

    public async Task<Result> ExecuteAsync(DeleteProductRequest request)
    {
        try
        {
            // Get product from repository
            var product = await _productRepository.GetByIdAsync(request.ProductId);
            if (product == null)
            {
                return Result.Failure(ErrorMessages.ProductNotFound);
            }

            // Delete product
            await _productRepository.DeleteAsync(product);

            // Dispatch domain event
            var domainEvent = new ProductDeletedEvent(request.ProductId);
            await _eventDispatcher.DispatchAsync(domainEvent);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete product {ProductId}", request.ProductId);
            return Result.Failure(ErrorMessages.InternalServerError);
        }
    }
}
```

### 2. Aggregate Root Event Dispatching in UseCase

```csharp
public class CreateOrderUseCase
{
    private readonly IDomainEventDispatcher _eventDispatcher;
    private readonly IOrderRepository _orderRepository;
    private readonly ICustomerRepository _customerRepository;
    private readonly ILogger<CreateOrderUseCase> _logger;

    public CreateOrderUseCase(
        IDomainEventDispatcher eventDispatcher,
        IOrderRepository orderRepository,
        ICustomerRepository customerRepository,
        ILogger<CreateOrderUseCase> logger)
    {
        _eventDispatcher = eventDispatcher;
        _orderRepository = orderRepository;
        _customerRepository = customerRepository;
        _logger = logger;
    }

    public async Task<Result<CreateOrderResponse>> ExecuteAsync(CreateOrderRequest request)
    {
        try
        {
            // Validate customer exists
            var customer = await _customerRepository.GetByIdAsync(request.CustomerId);
            if (customer == null)
            {
                return Result<CreateOrderResponse>.Failure(ErrorMessages.CustomerNotFound);
            }

            // Create order aggregate with domain logic
            var order = Order.Create(request.CustomerId, request.Items);
            
            // Domain events are automatically added by the aggregate
            // order.AddDomainEvent(new OrderCreatedEvent(order.Id, order.CustomerId));

            // Save to repository
            await _orderRepository.SaveAsync(order);

            // Dispatch all domain events and clear them from aggregate
            await _eventDispatcher.DispatchEventsAsync(order);

            var response = new CreateOrderResponse
            {
                OrderId = order.Id,
                TotalAmount = order.TotalAmount
            };

            return Result<CreateOrderResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create order for customer {CustomerId}", request.CustomerId);
            return Result<CreateOrderResponse>.Failure(ErrorMessages.InternalServerError);
        }
    }
}
```

### 3. Creating Domain Event Handlers

```csharp
// Domain Event
public class OrderCreatedEvent : DomainEvent
{
    public long OrderId { get; }
    public long CustomerId { get; }
    public decimal TotalAmount { get; }

    public OrderCreatedEvent(long orderId, long customerId, decimal totalAmount)
    {
        OrderId = orderId;
        CustomerId = customerId;
        TotalAmount = totalAmount;
    }
}

// Domain Event Handler (Infrastructure Layer)
public class OrderCreatedEventHandler : IDomainEventHandler<OrderCreatedEvent>
{
    private readonly IEmailService _emailService;
    private readonly ILogger<OrderCreatedEventHandler> _logger;

    public OrderCreatedEventHandler(
        IEmailService emailService,
        ILogger<OrderCreatedEventHandler> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    public async Task Handle(OrderCreatedEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            // Send confirmation email
            await _emailService.SendOrderConfirmationAsync(
                domainEvent.OrderId, 
                domainEvent.CustomerId,
                domainEvent.TotalAmount);
            
            _logger.LogInformation("Order confirmation email sent for order {OrderId}", domainEvent.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send order confirmation email for order {OrderId}", domainEvent.OrderId);
            throw;
        }
    }
}
```

### 4. Multiple Handlers for Same Event

You can have multiple handlers for the same domain event:

```csharp
// Analytics Handler (Infrastructure Layer)
public class OrderCreatedAnalyticsHandler : IDomainEventHandler<OrderCreatedEvent>
{
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<OrderCreatedAnalyticsHandler> _logger;

    public OrderCreatedAnalyticsHandler(
        IAnalyticsService analyticsService,
        ILogger<OrderCreatedAnalyticsHandler> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    public async Task Handle(OrderCreatedEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _analyticsService.TrackOrderCreated(
                domainEvent.OrderId, 
                domainEvent.CustomerId,
                domainEvent.TotalAmount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to track order analytics for order {OrderId}", domainEvent.OrderId);
            // Don't rethrow for analytics - it's not critical
        }
    }
}

// Inventory Handler (Infrastructure Layer)
public class OrderCreatedInventoryHandler : IDomainEventHandler<OrderCreatedEvent>
{
    private readonly IInventoryService _inventoryService;
    private readonly ILogger<OrderCreatedInventoryHandler> _logger;

    public OrderCreatedInventoryHandler(
        IInventoryService inventoryService,
        ILogger<OrderCreatedInventoryHandler> logger)
    {
        _inventoryService = inventoryService;
        _logger = logger;
    }

    public async Task Handle(OrderCreatedEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _inventoryService.ReserveInventoryForOrderAsync(domainEvent.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reserve inventory for order {OrderId}", domainEvent.OrderId);
            throw; // Inventory reservation is critical
        }
    }
}
```

### 5. Unit of Work Pattern with Events in UseCase

```csharp
public class ProcessOrderUseCase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IDomainEventDispatcher _eventDispatcher;
    private readonly ILogger<ProcessOrderUseCase> _logger;

    public ProcessOrderUseCase(
        IUnitOfWork unitOfWork,
        IDomainEventDispatcher eventDispatcher,
        ILogger<ProcessOrderUseCase> logger)
    {
        _unitOfWork = unitOfWork;
        _eventDispatcher = eventDispatcher;
        _logger = logger;
    }

    public async Task<Result> ExecuteAsync(ProcessOrderRequest request)
    {
        using var transaction = await _unitOfWork.BeginTransactionAsync();
        
        try
        {
            // Get order from repository
            var order = await _unitOfWork.Orders.GetByIdAsync(request.OrderId);
            if (order == null)
            {
                return Result.Failure(ErrorMessages.OrderNotFound);
            }

            // Domain logic - this will add domain events to the aggregate
            order.Process(request.ProcessedBy);
            
            // Save changes to database
            await _unitOfWork.SaveChangesAsync();
            
            // Dispatch events after successful save
            await _eventDispatcher.DispatchEventsAsync(order);
            
            // Commit transaction
            await transaction.CommitAsync();

            return Result.Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Failed to process order {OrderId}", request.OrderId);
            return Result.Failure(ErrorMessages.InternalServerError);
        }
    }
}
```

### 6. UseCase with Domain Service Coordination

```csharp
public class TransferProductUseCase
{
    private readonly IDomainEventDispatcher _eventDispatcher;
    private readonly IProductRepository _productRepository;
    private readonly IWarehouseRepository _warehouseRepository;
    private readonly ProductTransferDomainService _productTransferDomainService;
    private readonly ILogger<TransferProductUseCase> _logger;

    public TransferProductUseCase(
        IDomainEventDispatcher eventDispatcher,
        IProductRepository productRepository,
        IWarehouseRepository warehouseRepository,
        ProductTransferDomainService productTransferDomainService,
        ILogger<TransferProductUseCase> logger)
    {
        _eventDispatcher = eventDispatcher;
        _productRepository = productRepository;
        _warehouseRepository = warehouseRepository;
        _productTransferDomainService = productTransferDomainService;
        _logger = logger;
    }

    public async Task<Result> ExecuteAsync(TransferProductRequest request)
    {
        try
        {
            // Get aggregates
            var product = await _productRepository.GetByIdAsync(request.ProductId);
            var sourceWarehouse = await _warehouseRepository.GetByIdAsync(request.SourceWarehouseId);
            var targetWarehouse = await _warehouseRepository.GetByIdAsync(request.TargetWarehouseId);

            if (product == null || sourceWarehouse == null || targetWarehouse == null)
            {
                return Result.Failure(ErrorMessages.InvalidTransferRequest);
            }

            // Use domain service for complex business logic coordination
            var transferResult = _productTransferDomainService.TransferProduct(
                product, sourceWarehouse, targetWarehouse, request.Quantity);

            if (!transferResult.IsSuccess)
            {
                return Result.Failure(transferResult.ErrorMessage);
            }

            // Save all affected aggregates
            await _productRepository.SaveAsync(product);
            await _warehouseRepository.SaveAsync(sourceWarehouse);
            await _warehouseRepository.SaveAsync(targetWarehouse);

            // Dispatch events from all affected aggregates
            await _eventDispatcher.DispatchEventsAsync(product);
            await _eventDispatcher.DispatchEventsAsync(sourceWarehouse);
            await _eventDispatcher.DispatchEventsAsync(targetWarehouse);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to transfer product {ProductId}", request.ProductId);
            return Result.Failure(ErrorMessages.InternalServerError);
        }
    }
}
```

## Key Features

### 1. **Automatic Handler Discovery**
- Uses dependency injection to automatically find all registered handlers for a domain event type
- Supports multiple handlers for the same event type

### 2. **Error Handling**
- Comprehensive logging at different levels (Information, Debug, Error)
- Fails fast on handler exceptions while providing detailed error information
- Graceful handling of null events and aggregates

### 3. **Performance**
- Executes multiple handlers in parallel using `Task.WhenAll`
- Minimal reflection overhead with efficient method resolution

### 4. **Clean Architecture**
- Follows hexagonal architecture principles
- Domain events remain in the domain layer
- Infrastructure concerns (dispatching) are properly separated
- UseCases orchestrate domain logic and infrastructure services

### 5. **Aggregate Root Integration**
- Seamlessly integrates with aggregate roots
- Automatically clears domain events after successful dispatch
- Supports batch processing of multiple events

## Best Practices

1. **Use in Application Layer (UseCases)**:
   ```csharp
   // ✅ CORRECT - UseCase orchestrates domain and infrastructure
   public class CreateProductUseCase
   {
       private readonly IDomainEventDispatcher _eventDispatcher;
       private readonly IProductRepository _repository;
       
       public async Task<Result> ExecuteAsync(CreateProductRequest request)
       {
           var product = Product.Create(request.Name, request.Price);
           await _repository.SaveAsync(product);
           await _eventDispatcher.DispatchEventsAsync(product);
           return Result.Success();
       }
   }
   ```

2. **Always register handlers in DI container**:
   ```csharp
   services.AddScoped<IDomainEventHandler<YourEvent>, YourEventHandler>();
   ```

3. **Use descriptive event names and properties**:
   ```csharp
   public class ProductPriceChangedEvent : DomainEvent
   {
       public long ProductId { get; }
       public decimal OldPrice { get; }
       public decimal NewPrice { get; }
       public DateTime ChangedAt { get; }
   }
   ```

4. **Handle exceptions in event handlers**:
   ```csharp
   public async Task Handle(DomainEvent domainEvent, CancellationToken cancellationToken)
   {
       try
       {
           // Handler logic
       }
       catch (Exception ex)
       {
           _logger.LogError(ex, "Handler failed");
           // Decide whether to rethrow or handle gracefully
           throw;
       }
   }
   ```

5. **Dispatch events after successful persistence**:
   ```csharp
   // Save to database first
   await _repository.SaveAsync(aggregate);
   
   // Then dispatch events
   await _eventDispatcher.DispatchEventsAsync(aggregate);
   ```

6. **Follow Clean Architecture Layering**:
   - **Domain Layer**: Domain events, aggregates, domain services
   - **Application Layer**: UseCases that orchestrate domain and infrastructure
   - **Infrastructure Layer**: Event handlers, repositories, external services

## Integration with Existing Architecture

The `DomainEventDispatcher` integrates seamlessly with:

- **Entity Framework Core**: Use in `SaveChangesAsync` overrides
- **Unit of Work Pattern**: Dispatch events after successful commits
- **CQRS**: Bridge between command handlers and read model updates
- **UseCase Pattern**: Perfect fit for application layer orchestration
- **Saga Orchestration**: Trigger saga steps via domain events
- **Event Sourcing**: Complement event sourcing with domain events for side effects 