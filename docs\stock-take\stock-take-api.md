This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: KiotViet.Web.ServiceInterface/StockTakeApi.cs
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Files are sorted by Git change count (files with more changes are at the bottom)

## Additional Info

# Directory Structure
```
KiotViet.Web.ServiceInterface/StockTakeApi.cs
```

# Files

## File: KiotViet.Web.ServiceInterface/StockTakeApi.cs
```csharp
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using KiotViet.MongoDb.Entity;
using KiotViet.Persistence;
using KiotViet.Exceptions;
using KiotViet.Services.Interface;
using KiotViet.Utilities;
using Linq2Rest;
using ServiceStack;
using KiotViet.Persistence.Common;
using KiotViet.MongoServices.Interface;
using System.Text;
using System.Net;
using KiotViet.Resources;
using KiotViet.Services;
using KiotVietFnB.Application.Driving.Ports.Dtos.DamageItem;
using MediatR;
using KiotVietFnB.Application.Driving.Ports.Queries.DamageItemQueries.DamageItemByStockTakeId;
using KiotVietFnB.Application.Driving.Ports.Dtos.Category;
using KiotVietFnB.Application.Driving.Ports.Queries.CategoryQueries;
using KiotVietFnB.Application.Driving.Ports.Dtos;
using KiotVietFnB.Application.Driving.Ports.Queries.UserQueries.GetUserByIds;
namespace KiotViet.Web.Api
{
    [RequiredPermission(StockTake._Read)]
    [Route("/stocktakes", "GET")]
    public class StockTakeList : ODataReq, IReturn<PagingDataSource<object>>
    {
        public long[] Ids { get; set; }
        public string ProductKey { get; set; }
        public string[] Includes { get; set; }
        public string SerialKey { get; set; }
        public string CodeKey { get; set; }
    }
    [Route("/stocktakes/{Id}", "GET")]
    public class StockTakeGet : IReturn<object>
    {
        public long Id { get; set; }
        public string[] Includes { get; set; }
        public bool? IsForManage { get; set; }
    }
    [RequiresAnyPermission(StockTake._Create, StockTake._Update)]
    [Route("/stocktakes/{Id}", "PUT")]
    public class StockTakeUpdateDesc : IReturn<object>
    {
        public long Id { get; set; }
        public string Description { get; set; }
    }
    [Route("/stocktakes/getStockListById", "GET")]
    public class StockListTakeGet
    {
        public long[] Ids { get; set; }
         public string[] Includes { get; set; }
    }
    [RequiresAnyPermission(StockTake._Create, StockTake._Delete, StockTake._Update)]
    [Route("/stocktakes/{Id}", "DELETE")]
    public class StockTakeDelete : IReturn<StockTake>
    {
        public long Id { get; set; }
        public string CompareCode { get; set; }
        public int CompareStatusId { get; set; }
    }
    [Route("/stocktakes/getproductsbycategory", "POST GET")]
    public class GetProductsByCategory : IReturn<object>
    {
        public int CategoryId { get; set; }
        public int[] CategoryIds { get; set; }
        public bool ProductInStock { get; set; }
        public bool ProductActived { get; set; }
        public bool IsCoffee { get; set; }
        public bool FromStockTake { get; set; }
        public long[] ShelvesIds { get; set; }
    }
    [Route("/stocktakes/gettoppingsbycategory", "POST GET")]
    public class GetToppingsByCategory : IReturn<object>
    {
        public int[] CategoryIds { get; set; }
    }
    [Route("/stocktakes/{id}/details", "GET")]
    public class StockTakeGetItems : ODataReq, IReturn<PagingDataSource<StockTakeDetail>>
    {
        public long Id { get; set; }
        public string[] Includes { get; set; }
    }
    [RequiresAnyPermission(StockTake._Create, StockTake._Update)]
    [Route("/stocktakes", "POST")]
    public class StockTakeSave : IReturn<object>
    {
        public StockTake StockTake { get; set; }
        public List<StockTakeDetail> StockTakeDetail { get; set; }
        public bool IsAdjust { get; set; }
        public bool CompareIsAjust { get; set; }
        public long CopyFrom { get; set; }
    }
    [RequiresAnyPermission(StockTake._Create, StockTake._Update)]
    [Route("/stocktakes/saveStockTakeLumped", "POST")]
    public class StockTakeLumpedsSave : IReturn<object>   {
        public long[] Ids { get; set; }
        public string strCode { get; set; }
    }
    public class StockTakeApi : BaseApi
    {
        public IStockTakeService StockTakeService { get; set; }
        public IStockTakeDetailService StockTakeDetailService { get; set; }
        public ICategoryService CategoryService { get; set; }
        public IProductService ProductService { get; set; }
        public IAuditTrailService AuditTrailLogService { get; set; }
        public IBranchService BranchService { get; set; }
        public IObjectAccessService ObjectAccessService { get; set; }
        public IProductBranchService ProductBranchService { get; set; }
        public IInventoryTrackingService InventoryTrackingService { get; set; }
        public IUserService UserService { get; set; }
        public IEventMessageService EventMessageService { get; set; }
        public IShelvesService ShelvesService { get; set; }
        public KVEntities Db { get; set; }
        public IProductShelvesService ProductShelvesService { get; set; }
        public IAuthService AuthService { get; set; }
        public IProductSaleBranchService ProductSaleBranchService { get; set; }
        public IMultiCurrencyService MultiCurrencyService { get; set; }
        public IMediator Mediator { get; set; }
        public async Task<object> Get(StockTakeList req)
        {
            var retVal = new PagingDataSource<object> { Total = 0 };
            StockTakeService.DisableProxy();
            var stk =
                Include(StockTakeService.GetAll().Where(s => s.BranchId == CurrentBranchId),
                    req.Includes);
            if (!string.IsNullOrEmpty(req.SerialKey))
            {
                var lstBySerial = stk.SelectMany(
                    i => i.StockTakeDetails.Where(d => d.SerialNumbers.Contains(req.SerialKey) &&
                                                       d.Product != null && d.Product.RetailerId == CurrentRetailerId && (d.Product.isDeleted == null || !d.Product.isDeleted.Value)));
                var lstDistinctId = lstBySerial.Select(x => new { Id = x.StockTakeId }).Distinct();
                stk = from o in stk
                      join o1 in lstDistinctId on o.Id equals o1.Id
                      select o;
            }
            if (!string.IsNullOrEmpty(req.ProductKey))
            {
                var lstStockTakeIdByProduct =
                    stk.SelectMany(
                        o =>
                            o.StockTakeDetails.Where(
                                d =>
                                    (d.Product.FullName.Contains(req.ProductKey) ||
                                    d.Product.Code.Contains(req.ProductKey)) && d.Product != null && d.Product.RetailerId == CurrentRetailerId
                                    )).Select(a => a.StockTakeId).Distinct();
                stk = (from o in stk
                       join o1 in lstStockTakeIdByProduct on o.Id equals o1
                                  select o);
            }
            if (req.Orderby == null)
            {
                stk = stk.OrderByDescending(o => o.CreatedDate).ThenByDescending(o => o.Id);
            }
            if (req.CodeKey != null && req.CodeKey.Length > 0)
            {
                stk = stk.Where(i => i.Code.Contains(req.CodeKey));
            }
            if (req.Ids.Safe().Any())
                stk = stk.Where(i => req.Ids.Contains(i.Id));
            var ls = stk.Filter(req.GetModelFilter(AuthService.Context.ConfigDefaultTimeSetting));
            var data = await ls.Take(req).ToListAsync();
            var ids = data.Select(s => ((StockTake)s).Id);
            var sumaryData = await StockTakeDetailService.GetAll()
                .Where(d => ids.Contains(d.StockTakeId))
                .GroupBy(
                    d => d.StockTakeId,
                    d => new {
                        TotalPrice = d.AdjustmentValue * (double)(d.Cost ?? 0),
                        ValInc = d.AdjustmentValue >= 0 ? d.AdjustmentValue : 0,
                        ValDesc = d.AdjustmentValue < 0 ? d.AdjustmentValue : 0,
                        PriceInc = d.AdjustmentValue >= 0 ? d.AdjustmentValue * (double)(d.Cost??0) : 0,
                        PriceDesc = d.AdjustmentValue < 0 ? d.AdjustmentValue * (double)(d.Cost??0) : 0,
                        IsRealDetail = d.IsDraft != true || d.ActualCount != 0,
                        PriceActual = d.ActualCount >= 0 ? d.ActualCount * (double)(d.Cost ?? 0) : 0,
                        d.ActualCount,
                        d.AdjustmentValue,
                        d.IsDraft
                    },
                    (key, g) => new {
                        Id = key,
                        TotalActualCount = g.Sum(d => d.ActualCount),
                        TotalAdjustmentValue = g.Sum(d => d.IsRealDetail ? d.AdjustmentValue : 0),
                        TotalAdjustmentPrice = g.Sum(d => d.IsRealDetail ? d.TotalPrice : 0),
                        TotalQuantityIncrease = g.Sum(d => d.IsRealDetail ? d.ValInc : 0),
                        TotalQuantityReduced = g.Sum(d => d.IsRealDetail ? d.ValDesc : 0),
                        TotalPriceIncrease = g.Sum(d => d.IsRealDetail ? d.PriceInc : 0),
                        TotalPriceReduced = g.Sum(d => d.IsRealDetail ? d.PriceDesc : 0),
                        TotalPriceActual = g.Sum(d => d.IsRealDetail ? d.PriceActual : 0),
                        IsBlank = g.Count(d => d.IsRealDetail),
                        IsBlankCount = g.Count(d => d.IsDraft == null)
                    })
               .ToDictionaryAsync(s=>s.Id);
            retVal.Data = StockTakeService.DetachByClone(data, req.Includes);
            foreach(StockTake st in retVal.Data)
            {
                if (sumaryData.ContainsKey(st.Id))
                {
                    st.TotalAdjustmentValue = sumaryData[st.Id].TotalAdjustmentValue;
                    st.TotalActualCount = sumaryData[st.Id].TotalActualCount;
                    st.TotalAdjustmentPrice = sumaryData[st.Id].TotalAdjustmentPrice;
                    st.TotalQuantityIncrease = sumaryData[st.Id].TotalQuantityIncrease;
                    st.TotalQuantityReduced = sumaryData[st.Id].TotalQuantityReduced;
                    st.TotalPriceIncrease = sumaryData[st.Id].TotalPriceIncrease;
                    st.TotalPriceReduced = sumaryData[st.Id].TotalPriceReduced;
                    st.TotalPriceActual = sumaryData[st.Id].TotalPriceActual;
                    st.IsBlank = sumaryData[st.Id].IsBlank;
                    st.IsBlankCount = sumaryData[st.Id].IsBlankCount;
                }
            }
            retVal.Total = await ls.CountAsync();
            retVal.Filter = req;
            return retVal;
        }
        public async Task<object> Post(StockTakeSave req)
        {
            if (req.StockTake.Code != null && req.StockTake.Code.Length > 40)
                throw new KvValidateException(Resources.KVMessage.stocktake_CodeMaxLength);
            var flag = req.StockTake.Id == 0;
            var objResult = await StockTakeService.CreateOrUpdateAsync(req.StockTake, req.StockTakeDetail, req.IsAdjust);
            #region Log
            var productDetail = new StringBuilder();
            if (req.StockTakeDetail.Count > 0)
            {
                productDetail.Append(", bao gồm:<div>");
                foreach (var item in req.StockTakeDetail)
                {
                    if (item.IsDraft == true && item.ActualCount == 0)
                    {
                        productDetail.Append(
                            $"- [ProductCode]{item.ProductCode}[/ProductCode] : /{Normallize(item.SystemCount)}");
                    }
                    else
                    {
                        productDetail.Append(
                            $"- [ProductCode]{item.ProductCode}[/ProductCode] : {Normallize(item.ActualCount)}/{Normallize(item.SystemCount)}");
                    }
                    productDetail.Append("<br>");
                }
                productDetail.Append("</div>");
            }
            var log = new EventMessage
            {
                FunctionId = (int)FunctionType.StockTake,
                Action = flag ? (int)AuditTrailAction.Create : (int)AuditTrailAction.Update
            };
            if (flag)
            {
                var adjustmentDate = objResult.AdjustmentDate != null ?
                    $", ngày cân bằng kho: {DateFormat(objResult.AdjustmentDate ?? default(DateTime))}"
                    : "";
                log.Content = req.IsAdjust ?
                    $"Tạo phiếu kiểm kho: [StockTakeCode]{objResult.Code}[/StockTakeCode]{adjustmentDate}{productDetail}"
                    : $"Lưu tạm phiếu kiểm kho: [StockTakeCode]{objResult.Code}[/StockTakeCode]{adjustmentDate}{productDetail}";
                if (req.CopyFrom != 0)
                {
                    var oldStocktake = await StockTakeService.GetByIdAsync(req.CopyFrom);
                    log.Content = req.IsAdjust
                        ? $"Tạo phiếu kiểm kho: [StockTakeCode]{objResult.Code}[/StockTakeCode] (sao chép từ [StockTakeCode]{oldStocktake.Code}[/StockTakeCode]){adjustmentDate}{productDetail} "
                        : $"Lưu tạm phiếu kiểm kho: [StockTakeCode]{objResult.Code}[/StockTakeCode] (sao chép từ [StockTakeCode]{oldStocktake.Code}[/StockTakeCode]){adjustmentDate}{productDetail}";
                }
            }
            else
            {
                var updateCode = objResult.Code != req.StockTake.CompareCode ?
                    $" {req.StockTake.CompareCode}->[StockTakeCode]{objResult.Code}[/StockTakeCode]"
                    : $"[StockTakeCode]{objResult.Code}[/StockTakeCode]";
                var updateStt = !req.CompareIsAjust && req.IsAdjust ? " ( phiếu tạm->đã cân bằng kho )" : "";
                var updateAdjustmentDate = "";
                if (objResult.AdjustmentDate != req.StockTake.CompareAdjustmentDate)
                {
                    updateAdjustmentDate = objResult.AdjustmentDate != null ? req.StockTake.CompareAdjustmentDate == null ?
                        $", ngày cân bằng kho: {DateFormat(objResult.AdjustmentDate ?? default(DateTime))}"
                        : $", ngày cân bằng kho: {DateFormat(req.StockTake.CompareAdjustmentDate ?? default(DateTime))}->{DateFormat(objResult.AdjustmentDate ?? default(DateTime))}"
                        : "";
                }
                log.Content =
                    $"Cập nhật thông tin phiếu kiểm kho: {updateCode}{updateStt}{updateAdjustmentDate}{productDetail}";
            }
            if ((flag || (!flag && !req.CompareIsAjust)) && req.IsAdjust)
            {
                Func<decimal, decimal> roundMethodDefault = valueToRound => Math.Round(valueToRound);
                var totalAdjustmentPrice = MultiCurrencyService.RemoveRound((decimal)objResult.TotalAdjustmentPrice, roundMethodDefault);
                var adjustedBy = await UserService.GetCurrentUserAsync((objResult.AdjustedBy ?? objResult.CreatedBy));
                var branch = await BranchService.GetByIdAsync(objResult.BranchId);
                // Notification
                log.DocumentId = objResult.Id;
                log.DocumentCode = objResult.Code;
                log.ActionByUserId = objResult.AdjustedBy ?? objResult.CreatedBy;
                log.ActionByUserName = adjustedBy?.GivenName;
                log.BranchId = objResult.BranchId;
                log.BranchName = branch?.Name;
                log.RetailerId = CurrentRetailerId;
                log.EventType = StockTake._Create;
                log.Value = totalAdjustmentPrice;
                log.Status = (int)NotificationState.New;
                log.IsSystem = false;
                log.NotifyMessage = $"{log.ActionByUserName} cân bằng phiếu kiểm giá trị lệch {NormallizeWfp((double)totalAdjustmentPrice)} tại {log.BranchName}";
            }
            await EventMessageService.SendMessage(log);
            #endregion
            return new { Message = Resources.KVMessage._GlobalSaveSuccess, Data = StockTakeService.DetachByClone(objResult) };
        }
        public async Task<object> Post(StockTakeLumpedsSave req)
        {
            StockTake stockTake = new StockTake()
            {
                Id = 0,
                CreatedDate = DateTime.Now,
                CreatedBy = CurrentUser.Id,
                BranchId = CurrentBranchId,
                RetailerId = CurrentRetailerId,
                Status = 0,
                Code = "",
                Description = "Gộp các phiếu kiểm kho " + req.strCode
            };
            List<StockTakeDetail> stockTakeDetail = new List<StockTakeDetail>() ;
            #region get stockTakeDetail
            var retValAll = new List<StockTakeDetail>();
            var stockTakeList = new List<StockTakeDetail>();
            if (req.Ids.Length == 0)
                return retValAll;
            var objTmpList = await StockTakeService.GetAll().Where(w => req.Ids.Contains(w.Id) && w.BranchId == CurrentBranchId && w.RetailerId == CurrentRetailerId && w.Status == 0).ToListAsync();
            if (objTmpList == null || (objTmpList != null && objTmpList.Count() < 2))
                return retValAll;
            objTmpList = objTmpList.OrderByDescending(o => o.CreatedDate).ToList();
            foreach (var objTmp in objTmpList)
            {
                // update product serials list of return
                var prodList = new List<prodListInfo>();
                foreach (var ent in objTmp.StockTakeDetails)
                {
                    if (ent?.Product?.ProductSerials != null)
                    {
                        var serials = "";
                        foreach (var s in ent.Product.ProductSerials)
                        {
                            if (s.Status > 0 && s.BranchId == CurrentBranchId)
                                serials += "," + s.SerialNumber;
                        }
                        if (serials.Length > 1)
                            serials = serials.Substring(1);
                        prodListInfo prod = new prodListInfo()
                        {
                            ProductId = ent.ProductId,
                            ProdList = serials
                        };
                        prodList.Add(prod);
                    }
                }
                // update product serials list of return
                var retVal = objTmp.StockTakeDetails.Select(
                             s =>
                                 new StockTakeDetail
                                 {
                                     ProductId = s.ProductId,
                                     ProductCode = s.Product.Code,
                                     SystemCount = 0,
                                     ActualCount = s.ActualCount,
                                     AdjustmentValue = 0,
                                     ProductName = s.ProductName,
                                     SerialNumbers = s.SerialNumbers,
                                     SystemSerialNumbers = prodList.Where(o => o.ProductId == s.ProductId).Select(t => t.ProdList).FirstOrDefault(),
                                     Id = 0,
                                     IsDraft = s.IsDraft,
                                     OrderByNumber = s.OrderByNumber
                                 }).OrderBy(o => o.Id).ToList();
                stockTakeList.AddRange(retVal);
            }
            // Union Stock Take by Product ID
            foreach (var item in stockTakeList)
            {
                var stock = retValAll.FirstOrDefault(o => o.ProductId == item.ProductId);
                if (stock == null)
                {
                    retValAll.Add(item);
                }
                else
                {
                    // Update ActualCount and SerialNumbers
                    if (item.SerialNumbers != null && item.SerialNumbers.Trim().Length > 0)
                    {
                        var strSerialNumbers = "";
                        if (stock.SerialNumbers != null && stock.SerialNumbers.Trim().Length > 0)
                        {
                            strSerialNumbers = stock.SerialNumbers + "," + item.SerialNumbers;
                            // Remove Dupplice serial Number
                            List<string> listSersialNumber = strSerialNumbers.Split(',').Distinct().ToList();
                            string newStrSerialNumbers = string.Join(",", listSersialNumber);
                            retValAll.Where(o => o.ProductId == item.ProductId).Select(c => { c.ActualCount = listSersialNumber.Count; c.SerialNumbers = newStrSerialNumbers; return c; }).ToList();
                        }
                        else
                        {
                            retValAll.Where(o => o.ProductId == item.ProductId).Select(c => { c.ActualCount = item.ActualCount; c.SerialNumbers = item.SerialNumbers; return c; }).ToList();
                        }
                    }
                    else
                    {
                        // Update ActualCount
                        retValAll.Where(o => o.ProductId == item.ProductId).Select(c => { c.ActualCount = Math.Round(c.ActualCount + item.ActualCount, 3); return c; }).ToList();
                    }
                    // Update IsDraft
                    if (item.IsDraft == false)
                    {
                        retValAll.Where(o => o.ProductId == item.ProductId).Select(c => { c.IsDraft = false; return c; }).ToList();
                    }
                }
            }
            // Get  System count
            if (retValAll != null)
            {
                var productList = retValAll.Select(o => o.ProductId).ToList();
                var productBranchList = ProductBranchService.GetAll().Where(w => productList.Contains(w.ProductId) && w.BranchId == CurrentBranchId && w.RetailerId == CurrentRetailerId).ToList();
                var systemCountList = productBranchList.Select(o => new ProductBranch { OnHand = o.OnHand, ProductId = o.ProductId }).ToList();
                foreach (var ret in retValAll)
                {
                    var systemCount = systemCountList.Where(o => o.ProductId == ret.ProductId).Select(x => x.OnHand).FirstOrDefault();
                    ret.SystemCount = systemCount;
                    ret.AdjustmentValue = (ret.ActualCount == 0 && ret.IsDraft == true) ? 0 : (ret.ActualCount - ret.SystemCount);
                }
            }
            stockTakeDetail =  retValAll;
            #endregion stockTakeDetail
            // LUmped
            var objResult = StockTakeService.DetachByClone(await StockTakeService.CreateOrUpdateAsync(stockTake, stockTakeDetail, false));
            if (objResult != null && objResult.Code != null)
            {
                #region Log
                var productDetail = new StringBuilder();
                if (stockTakeDetail.Count > 0)
                {
                    productDetail.Append(", bao gồm:<div>");
                    foreach (var item in stockTakeDetail)
                    {
                        if(item.IsDraft ==true && item.ActualCount == 0)
                        {
                            productDetail.Append(
                                $"- [ProductCode]{item.ProductCode}[/ProductCode] : /{Normallize(item.SystemCount)}");
                        }
                        else
                        {
                            productDetail.Append(
                                $"- [ProductCode]{item.ProductCode}[/ProductCode] : {Normallize(item.ActualCount)}/{Normallize(item.SystemCount)}");
                        }
                        productDetail.Append("<br>");
                    }
                    productDetail.Append("</div>");
                }
                var log = new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.StockTake,
                    Action = (int)AuditTrailAction.Create
                };
                log.Content = $"Lưu tạm phiếu kiểm kho: [StockTakeCode]{objResult.Code}[/StockTakeCode] gộp từ các phiếu {req.strCode} {productDetail}";
                var task1 = AuditTrailLogService.AddLog(log).ConfigureAwait(false);
                #endregion
            }
            return new { Message = Resources.KVMessage._GlobalSaveSuccess, Data = objResult };
        }
        public async Task<PagingDataSource<StockTakeDetail>> Get(StockTakeGetItems req)
        {
            var retVal = new PagingDataSource<StockTakeDetail> { Total = 0 };
            var eagerLoad = req.Includes;
            if (req.Includes != null && req.Includes.Contains("Product"))
            {
                eagerLoad = eagerLoad.Concat(new[] { "Product.ProductAttributes" }).ToArray();
            }
            var stocktakedetails = Include(StockTakeDetailService.GetAll().Where(o => o.StockTakeId == req.Id && o.StockTake.RetailerId == AuthService.Context.RetailerId), eagerLoad);
            if (req.Orderby == null)
            {
                stocktakedetails = stocktakedetails.OrderBy(x => x.OrderByNumber);
            }
            var ls = stocktakedetails.Filter(req.GetModelFilter());
            var retls = await ls.Cast<StockTakeDetail>().Take(req).ToListAsync();
            if (req.Includes != null && req.Includes.Contains("Product"))
            {
                req.Includes = req.Includes.Concat(new[] { "ProductName" }).ToArray();
            }
            var stockTakeDetail = StockTakeDetailService.DetachByClone(retls, req.Includes);
            var prodIds = stockTakeDetail.Select(s => s.ProductId).ToList();
            var productShelveses = await (from ps in ProductShelvesService.GetAll()
                    join s in ShelvesService.GetAll() on ps.ShelvesId equals s.Id
                    where prodIds.Contains(ps.ProductId) && s.BranchId == CurrentBranchId
                    select new { Name = s.Name, ProductId = ps.ProductId }
                ).OrderBy(s => s.Name).ToListAsync();
            foreach(var stockTakeItem in stockTakeDetail)
            {
                Func<decimal, decimal> roundMethodDefault = valueToRound => Math.Round(valueToRound, 4);
                var cost = MultiCurrencyService.RemoveRound(stockTakeItem.Cost ?? 0, roundMethodDefault);
                stockTakeItem.Cost = cost * (decimal)stockTakeItem.AdjustmentValue;
                stockTakeItem.ProductShelvesStr = productShelveses.Where(x => x.ProductId == stockTakeItem.ProductId).Select(x => x.Name).Join(", ");
            }
            retVal.Data = stockTakeDetail.ToList();
            retVal.Total = await ls.CountAsync();
            return retVal;
        }
        public async Task<object> Delete(StockTakeDelete req)
        {
            await StockTakeService.DeleteAsync(req.Id);
            #region Log
            var logStatus = req.CompareStatusId == (int)StockTakeStatus.Generator
                ? EnumHelper.ToDescription(StockTakeStatus.Generator)
                : EnumHelper.ToDescription(StockTakeStatus.Approval);
            var log = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.StockTake,
                Action = (int)AuditTrailAction.Reject,
                Content = $"Hủy phiếu kiểm kho: [StockTakeCode]{req.CompareCode}[/StockTakeCode]({logStatus} -> {EnumHelper.ToDescription(DamageStatus.Cancel)})"
            };
            var task2 = AuditTrailLogService.AddLog(log).ConfigureAwait(false);
            #endregion
            return new { Message = Resources.KVMessage._GlobalDeleteSuccess };
        }
        public async Task<object> Get(StockTakeGet req)
        {
            if (req.Id == 0)
                return
                    new
                    {
                        Data =
                            new
                            {
                                Id = 0,
                                CreatedDate = DateTime.Now,
                                CreatedBy = CurrentUser.Id,
                                BranchId = CurrentBranchId,
                                RetailerId = CurrentRetailerId,
                                Status = 0,
                                Code = ""
                            },
                        StockTakeItems = new { }
                    };
            var objTmp = await GetByIdAsync(req.Id, req.IsForManage);
            var lstDamegeItem = await GetListDamageItemByStockTakeIdAsync(req);
            var categories = await GetAllCategories(req);
            Func<double, double> roundMethodDefault = valueToRound => Math.Round(valueToRound, 3);
            var retVal =
                new
                {
                    Data = StockTakeService.DetachByClone(objTmp, req.Includes),
                    StockTakeItems =
                        objTmp.StockTakeDetails.Select(
                            s =>
                                new
                                {
                                    s.ProductId,
                                    ProductCode = s.Product.Code,
                                    s.SystemCount,
                                    s.ActualCount,
                                    s.AdjustmentValue,
                                    s.ProductName,
                                    s.Product.IsLotSerialControl,
                                    s.SerialNumbers,
                                    s.SystemSerialNumbers,
                                    s.Id,
                                    s.IsDraft,
                                    Cost = s.Cost ?? 0,
                                    AdjustmentPrice = s.AdjustmentValue * MultiCurrencyService.RemoveRound((double)(s.Cost ?? 0), roundMethodDefault),
                                    s.ModifiedDate,
                                    s.ProductShelvesStr,
                                    s.OrderByNumber,
                                    CategoryName = GetCategoryName(s.Product.CategoryId, categories),
                                    s.Product.CategoryId,
                                    UnitName = s.Product.Unit,
                                    MasterCategoryId = categories.Where(x => x.Id == s.Product.CategoryId).Select(x => x.ParentId).FirstOrDefault(),
                                    MasterCategoryName = GetCategoryName(s.Product.CategoryId, categories, true)
                                }).OrderByDescending(o => o.ModifiedDate).OrderBy(x => x.OrderByNumber).ToList(),
                    DamageItems = lstDamegeItem
                };
            if (req.IsForManage == true)
            {
                await MapUserNameByStockTake(retVal.Data);
            }
            return retVal;
        }
        private static string GetCategoryName(int categoryId, List<CategoryDto> categories, bool isReturnParent = false)
        {
            var category = categories.FirstOrDefault(x => x.Id == categoryId);
            if(category == null) return "";
            if (isReturnParent)
            {
                var parent = categories.FirstOrDefault(x => x.Id == category.ParentId);
                return parent != null ? parent.Name : "";
            }
            return category.Name;
        }
        private async Task<List<CategoryDto>> GetAllCategories(StockTakeGet req)
        {
            if (!(req.IsForManage ?? false))
            {
                return new List<CategoryDto>();
            }
            var categoryResult = await Mediator.Send(new GetAllCategoryQuery());
            if (!categoryResult.IsSuccess)
            {
                Log.Error(categoryResult.Errors);
                return new List<CategoryDto>();
            }
            return categoryResult.Data;
        }
        private async Task<List<DamageItemDto>> GetListDamageItemByStockTakeIdAsync(StockTakeGet req)
        {
            var isViewDamgeItem = req.IsForManage == true && AuthService.CheckPermission(DamageItem._Read);
            if (!isViewDamgeItem)
            {
                return new List<DamageItemDto>();
            }
            var damgeItemResult = await Mediator.Send(new DamageItemByStockTakeIdQuery(req.Id));
            if (!damgeItemResult.IsSuccess)
            {
                Log.Error(damgeItemResult.Errors);
            }
            return damgeItemResult.Data;
        }
        private async Task<StockTake> GetByIdAsync(long id, bool? isForManage)
        {
            var objTmp = await StockTakeService.GetByIdAsync(id);
            if (objTmp == null)
            {
                throw new KvValidateException(KVMessage.NotFound);
            }
            if (isForManage == true)
            {
                return objTmp;
            }
            if (CurrentUser.IsLimitedByTrans && objTmp.CreatedBy != CurrentUser.Id && objTmp.AdjustedBy != CurrentUser.Id)
            {
                throw new KvValidateException(KVMessage._notAllowViewTransactionOtherUser);
            }
            return objTmp;
        }
        private async Task MapUserNameByStockTake(StockTake stockTake)
        {
            var userIdsFromStockTake = new List<long> { stockTake.CreatedBy, stockTake.AdjustedBy ?? 0, stockTake.ModifiedBy ?? 0 };
            var userIds = userIdsFromStockTake.Where(w => w != 0).Distinct().ToList();
            var userDtos = await GetListUserByIds(userIds);
            stockTake.CreatedName = userDtos.FirstOrDefault(f => f.Id == stockTake.CreatedBy)?.GivenName;
            stockTake.AdjustedName = userDtos.FirstOrDefault(f => f.Id == stockTake.AdjustedBy)?.GivenName;
            stockTake.ModifiedName = userDtos.FirstOrDefault(f => f.Id == stockTake.ModifiedBy)?.GivenName;
        }
        private async Task<List<UserDto>> GetListUserByIds(List<long> ids)
        {
            var userResult = await Mediator.Send(new GetUserByIdsQuery(ids));
            if (!userResult.IsSuccess)
            {
                Log.Error(userResult.Errors);
                return new List<UserDto>();
            }
            return userResult.Data;
        }
        public async Task<List<StockTakeDetail>> Get(StockListTakeGet req)
        {
            var retValAll = new List<StockTakeDetail>();
            var stockTakeList = new List<StockTakeDetail>();
            if (req.Ids.Length == 0)
                return retValAll;
            var totalCount = await StockTakeService.GetAll().Where(w => w.Status == 0 && req.Ids.Contains(w.Id)).SelectMany(w => w.StockTakeDetails).CountAsync();
            if(totalCount > 5000)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return null;
            }
            var objTmpList = await StockTakeService.GetAll().Where(w => req.Ids.Contains(w.Id) && w.BranchId == CurrentBranchId && w.RetailerId == CurrentRetailerId && w.Status == 0).ToListAsync();
            if (objTmpList == null || (objTmpList != null && objTmpList.Count() < 2))
                return retValAll;
            objTmpList = objTmpList.OrderByDescending(o => o.CreatedDate).ToList();
            foreach (var objTmp in objTmpList)
            {
                // update product serials list of return
                var prodList = new List<prodListInfo>();
                foreach (var ent in objTmp.StockTakeDetails)
                {
                    if (ent != null && (ent.Product != null && ent.Product.ProductSerials != null))
                    {
                        var serials = "";
                        foreach (var s in ent.Product.ProductSerials)
                        {
                            if (s.Status > 0 && s.BranchId == CurrentBranchId)
                                serials += "," + s.SerialNumber;
                        }
                        if (serials.Length > 1)
                            serials = serials.Substring(1);
                        prodListInfo prod = new prodListInfo()
                        {
                            ProductId = ent.ProductId,
                            ProdList = serials
                        };
                        prodList.Add(prod);
                    }
                }
                // update product serials list of return
                var retVal = objTmp.StockTakeDetails.Select(
                                s =>
                                    new StockTakeDetail
                                    {
                                        ProductId = s.ProductId,
                                        ProductCode = s.Product.Code,
                                        SystemCount = 0,
                                        ActualCount = s.ActualCount,
                                        AdjustmentValue = 0,
                                        ProductName = s.ProductName,
                                        SerialNumbers = s.SerialNumbers,
                                        SystemSerialNumbers = prodList.Where(o => o.ProductId == s.ProductId).Select(t => t.ProdList).FirstOrDefault(),
                                        Id = s.Id,
                                        IsDraft = s.IsDraft,
                                        ReasonOfDiff = s.Product.IsLotSerialControl == true ? "1": "0"
                                    }).OrderBy(o => o.CreatedDate).ThenBy(o => o.Id).ToList();
                stockTakeList.AddRange(retVal);
            }
            foreach (var item in stockTakeList)
            {
                var stock = retValAll.FirstOrDefault(o => o.ProductId == item.ProductId);
                if (stock == null)
                {
                    retValAll.Add(item);
                }
                else
                {
                    if (item.SerialNumbers != null && item.SerialNumbers.Trim().Length > 0)
                    {
                        var strSerialNumbers = "";
                        if (stock.SerialNumbers != null && stock.SerialNumbers.Trim().Length > 0)
                        {
                            strSerialNumbers = stock.SerialNumbers + "," + item.SerialNumbers;
                            // Remove Dupplice serial Number
                            List<string> listSersialNumber = strSerialNumbers.Split(',').Distinct().ToList();
                            string newStrSerialNumbers = string.Join(",", listSersialNumber);
                            retValAll.Where(o => o.ProductId == item.ProductId).Select(c => { c.ActualCount = listSersialNumber.Count; c.SerialNumbers = newStrSerialNumbers; return c; }).ToList();
                        }
                        else
                        {
                            retValAll.Where(o => o.ProductId == item.ProductId).Select(c => { c.ActualCount = item.ActualCount; c.SerialNumbers = item.SerialNumbers; return c; }).ToList();
                        }
                    }
                    else
                    {
                        // Update ActualCount
                        retValAll = retValAll.Where(o => o.ProductId == item.ProductId).ToList();
                        foreach(var stockTakeDetail in retValAll)
                        {
                            Func<double, double> roundMethodDefault = valueToRound => Math.Round(valueToRound, 3);
                            var actualCount = MultiCurrencyService.RemoveRound(stockTakeDetail.ActualCount + item.ActualCount, roundMethodDefault);
                            stockTakeDetail.ActualCount = actualCount;
                        }
                    }
                    // Update IsDraft
                    if (item.IsDraft == false)
                    {
                        retValAll.Where(o => o.ProductId == item.ProductId).Select(c => { c.IsDraft = false; return c; }).ToList();
                    }
                }
            }
            // Get  System count
            if (retValAll != null)
            {
                var productList = retValAll.Select(o => o.ProductId).ToList();
                var productBranchList = ProductBranchService.GetAll().Where(w => productList.Contains(w.ProductId) && w.BranchId == CurrentBranchId && w.RetailerId == CurrentRetailerId).ToList();
                var systemCountList = productBranchList.Select(o => new ProductBranch { OnHand = o.OnHand, ProductId = o.ProductId }).ToList();
                foreach (var ret in retValAll)
                {
                    var systemCount = systemCountList.Where(o => o.ProductId == ret.ProductId).Select(x => x.OnHand).FirstOrDefault();
                    ret.SystemCount = systemCount;
                    ret.AdjustmentValue = (ret.ActualCount == 0 && ret.IsDraft == true)? 0: (ret.ActualCount - ret.SystemCount);
                }
            }
            return retValAll;
        }
        public async Task<object> Get(GetProductsByCategory req)
        {
            return await GetProductsByCategory(req);
        }
        public async Task<object> Post(GetProductsByCategory req)
        {
            return await GetProductsByCategory(req);
        }
        public async Task<object> Get(GetToppingsByCategory req)
        {
            return await GetToppingsByCategory(req);
        }
        public async Task<object> Post(GetToppingsByCategory req)
        {
            return await GetToppingsByCategory(req);
        }
        public async Task<object> Put(StockTakeUpdateDesc req)
        {
            var result = StockTakeService.DetachByClone(await StockTakeService.UpdateDescriptionAsync(req.Id, req.Description));
            return new { Message = Resources.KVMessage._GlobalSaveSuccess, Data = result };
        }
        private async Task<object> GetProductsByCategory(GetProductsByCategory req)
        {
            if (req.CategoryIds != null && req.CategoryIds.Length > 0)
            {
                var query = ProductService.GetProductByBranch(0).Where(p => p.IsTimeType != true
                                                                             && (p.ProductType == (int)ProductType.Purchased || req.IsCoffee)
                                                                             && !(p.ProductType == (int)ProductType.Manufactured && p.IsProcessedGoods != true)
                                                                             && (p.ProductType != (int)ProductType.Customizable)
                                                                             && (req.FromStockTake || p.IsTopping != true)
                                                                             && (!req.FromStockTake || p.InventoryTrackingIgnore == null || p.InventoryTrackingIgnore == false)
                                                                             );
                if (!req.CategoryIds.Contains(0))
                {
                    var result = await CategoryService.GetAllSub(req.CategoryIds);
                    var ls = await result.Select(s => s.Id).ToListAsync();
                    query = query.Where(p => ls.Contains(p.CategoryId));
                }
                if (req.ProductActived)
                {
                    query = query.Where(p => p.isActive);
                }
                if (req.ProductInStock)
                {
                    query = query.Where(p => p.OnHand > 0);
                }
                if (req.FromStockTake)
                {
                    query = query.Where(p => p.ProductType == (byte)ProductType.Purchased);
                }
                if (req.ShelvesIds != null && req.ShelvesIds.Any())
                {
                    var productShelves = await ProductShelvesService.GetAll().Where(p => req.ShelvesIds.Contains(p.ShelvesId)).Select(p => p.ProductId).Distinct().ToListAsync();
                    if (productShelves != null)
                    {
                        query = query.Where(p => productShelves.Contains(p.Id));
                    }
                }
                var lstProductNotAllowedForSale = ProductSaleBranchService.GetListProductIdsNotAllowSale(AuthService.Context.BranchId);
                if(lstProductNotAllowedForSale != null && lstProductNotAllowedForSale.Any())
                {
                    query = query.Where(p => !lstProductNotAllowedForSale.Contains(p.Id));
                }
                var productShelve = await (from p in query
                                    join pds in Db.ProductShelves on p.Id equals pds.ProductId
                                    join sh in Db.Shelves on pds.ShelvesId equals sh.Id
                                    group sh.Name by p.Id).ToListAsync();
                var validProducts = (await query.ToListAsync()).Select(p => new
                {
                    Id = 0,
                    ProductSerials = p.ProductSerials.Where(w => w.BranchId == CurrentBranchId), //Re: #3675 - Get Only Serial/Imei in current Branch
                    p.IsLotSerialControl,
                    ProductId = p.Id,
                    ProductName = p.AttributedName,
                    ProductCode = p.Code,
                    SystemCount = p.OnHand,
                    ActualCount = "undefined",
                    AdjustmentValue = -p.OnHand,
                    p.Cost,
                    StockTakeId = 0,
                    p.isActive,
                    p.BasePrice,
                    p.CategoryId,
                    ProductShelvesStr = productShelve.Find(pd => pd.Key == p.Id) != null  ? string.Join("|", productShelve.Find(pd => pd.Key == p.Id).ToArray()) : string.Empty
                }).ToList<object>();
                return validProducts;
            }
            else
            {
                IQueryable<Category> result;
                if (req.CategoryId != 0)
                {
                    var categoryExisted = await CategoryService.GetByIdAsync(req.CategoryId);
                    if (categoryExisted == null || (categoryExisted.isDeleted ?? false)) throw new KvValidateException(KVMessage.category_NotFound);
                    result = await CategoryService.GetAllSub(req.CategoryId);
                }
                else
                {
                    result = CategoryService.GetAll().Where(c => c.isDeleted != true);
                }
                var ls = await result.ToListAsync();
                if (ls != null)
                {
                    var catIds = ls.Select(s => s.Id).ToList();
                    var query = ProductService.GetProductByBranch(0).Where(p => p.isActive && catIds.Contains(p.CategoryId) && p.IsTimeType != true && (p.ProductType == (byte)ProductType.Purchased || req.IsCoffee)
                                                                                    && !(p.ProductType == (byte)ProductType.Manufactured && p.IsProcessedGoods != true)
                                                                                    && ((req.IsCoffee && p.IsTopping != true) || !req.IsCoffee));
                    if (req.FromStockTake)
                    {
                        query = query.Where(p => p.ProductType == (byte)ProductType.Purchased);
                    }
                    if (req.ShelvesIds != null && req.ShelvesIds.Any())
                    {
                        var productShelves = await ProductShelvesService.GetAll().Where(p => req.ShelvesIds.Contains(p.ShelvesId)).Select(p => p.ProductId).Distinct().ToListAsync();
                        if (productShelves.Any())
                        {
                            query = query.Where(p => productShelves.Contains(p.Id));
                        }
                    }
                    var validProducts = (await query.ToListAsync()).Select(p => new
                    {
                        Id = 0,
                        ProductSerials = p.ProductSerials.Where(w => w.BranchId == CurrentBranchId),
                        p.IsLotSerialControl,
                        ProductId = p.Id,
                        ProductName = p.AttributedName,
                        ProductCode = p.Code,
                        SystemCount = p.OnHand,
                        ActualCount = "undefined",
                        AdjustmentValue = -p.OnHand,
                        p.Cost,
                        StockTakeId = 0,
                        p.isActive,
                        p.BasePrice,
                        p.CategoryId
                    }).ToList<object>();
                    return validProducts;
                }
            }
            return string.Empty;
        }
        private async Task<object> GetToppingsByCategory(GetToppingsByCategory req)
        {
            var result = req.CategoryIds?.Any() == true ? await CategoryService.GetAllSub(req.CategoryIds)
                                             : CategoryService.GetAll().Where(c => c.isDeleted != true);
            var ls = await result.ToListAsync();
            if (ls != null)
            {
                var catIds = ls.Select(s => s.Id).ToList();
                var validProducts = (await ProductService.GetProductByBranch(0)
                                                            .Where(p => p.isActive && catIds.Contains(p.CategoryId) && p.IsTopping == true).ToListAsync())
                                                            .Select(p => new
                                                            {
                                                                Id = 0,
                                                                ProductId = p.Id,
                                                                ProductName = p.AttributedName,
                                                                ProductCode = p.Code,
                                                                isActive = p.isActive,
                                                                BasePrice = p.BasePrice
                                                            }).ToList<object>();
                return validProducts;
            }
            return string.Empty;
        }
	}
    public class StockTakeDetailInfo: StockTakeDetail
    {
        public object ProductSerials { get; set; }
        public bool? IsLotSerialControl { get; set; }
    }
    public class prodListInfo
    {
        public long ProductId { get; set; }
        public string ProdList { get; set; }
    }
}
```
