# Stock Take Business Specification

## Overview

The Stock Take system is a comprehensive inventory management solution that enables businesses to perform physical inventory counts, compare them with system records, and make necessary adjustments to maintain accurate inventory levels. This document outlines the complete business logic, workflows, and specifications for the Stock Take functionality.

## Table of Contents

1. [Business Entities](#business-entities)
2. [Business Rules](#business-rules)
3. [Workflow Processes](#workflow-processes)
4. [Status Management](#status-management)
5. [Validation Rules](#validation-rules)
6. [Integration Points](#integration-points)
7. [User Permissions](#user-permissions)
8. [API Specifications](#api-specifications)
9. [Error Handling](#error-handling)

## Business Entities

### StockTake (Master Entity)

The main entity representing a stock take operation with the following business attributes:

| Property | Type | Description | Business Rules |
|----------|------|-------------|----------------|
| Id | long | Unique identifier | Auto-generated |
| Code | string | Business code for the stock take | Must be unique, max 40 characters, auto-generated if empty |
| BranchId | int | Branch where stock take is performed | Must be valid branch, defaults to current user's branch |
| RetailerId | int | Retailer identifier | System assigned |
| Status | short | Current status of stock take | 0=Draft/Generator, 1=Approved/Adjustment, 2=Cancelled |
| AdjustedBy | long? | User who approved the stock take | Set when status changes to Approved |
| AdjustmentDate | DateTime? | Date when stock take was approved | Must not be future date |
| CreatedDate | DateTime | Creation timestamp | Auto-set |
| CreatedBy | long | Creator user ID | Current user |
| ModifiedBy | long? | Last modifier user ID | Updated on changes |
| ModifiedDate | DateTime? | Last modification timestamp | Auto-updated |
| Description | string | Business description | Optional, used for audit trail |
| RecentHistory | string | Change history | System maintained |

### StockTakeDetail (Detail Entity)

Individual line items within a stock take representing product-specific inventory adjustments:

| Property | Type | Description | Business Rules |
|----------|------|-------------|----------------|
| Id | long | Unique identifier | Auto-generated |
| StockTakeId | long | Parent stock take reference | Required, foreign key |
| ProductId | long | Product being counted | Must be valid, active product |
| SystemCount | double | Current system inventory | Auto-populated from inventory |
| ActualCount | double | Physical count performed | User input, must be >= 0 |
| AdjustmentValue | double | Difference (Actual - System) | Auto-calculated |
| ReasonOfDiff | string | Reason for discrepancy | Optional user input |
| SerialNumbers | string | Actual serial numbers found | Required for serial-controlled products |
| SystemSerialNumbers | string | System serial numbers | Auto-populated |
| IsDraft | bool? | Draft status indicator | true=not counted, false=counted |
| Cost | decimal? | Unit cost for valuation | Auto-populated from product cost |
| OrderByNumber | int? | Display order | Optional sorting |

## Business Rules

### Core Business Rules

1. **Branch Isolation**
   - Stock takes are branch-specific
   - Users can only access stock takes from their assigned branch
   - Cross-branch stock take operations are not permitted

2. **Product Eligibility**
   - Only purchased products can be included in stock takes
   - Products must be active and not deleted
   - Time-based products are excluded
   - Manufactured products without processed goods flag are excluded
   - Customizable products are excluded

3. **Serial Number Management**
   - For serial/lot controlled products, actual count must match serial number count
   - Serial numbers must be unique within the same product
   - System automatically tracks existing serial numbers by branch

4. **Cost Calculation**
   - Unit cost is derived from ProductBranch cost
   - For child products, cost is calculated: MasterUnitCost × ConversionValue
   - Maximum total cost validation: ActualCount × Cost ≤ MAX_TOTAL_COST (99,999,999,999,999.90)

5. **Code Generation**
   - Codes are auto-generated if not provided
   - Clone operations append suffix with incremental numbering
   - Format: OriginalCode.001, OriginalCode.002, etc.

### Inventory Integration Rules

1. **System Count Calculation**
   - For items ≤ 100: Uses historical inventory tracking by adjustment date
   - For items > 100: Uses current ProductBranch.OnHand for performance
   - Child products: SystemCount = MasterProductInventory / ConversionValue

2. **Inventory Impact**
   - Draft stock takes do not affect inventory
   - Approved stock takes create inventory adjustments
   - Cancelled stock takes reverse inventory adjustments

3. **Close Book Validation**
   - Stock takes cannot be created/approved before branch close book date
   - Deletion of approved stock takes validates against close book date

## Workflow Processes

### 1. Stock Take Creation Process

```mermaid
graph TD
    A[Start] --> B[Create Draft Stock Take]
    B --> C[Add Products to Count]
    C --> D[Enter Physical Counts]
    D --> E[Save as Draft]
    E --> F{Ready to Approve?}
    F -->|No| D
    F -->|Yes| G[Approve Stock Take]
    G --> H[Create Inventory Adjustments]
    H --> I[Send Notifications]
    I --> J[End]
```

### 2. Product Selection Process

```mermaid
graph TD
    A[Select Category] --> B[Filter Products]
    B --> C{Product Criteria}
    C -->|Pass| D[Add to Stock Take]
    C -->|Fail| E[Exclude Product]
    D --> F[Populate System Count]
    F --> G[Ready for Physical Count]
```

### 3. Bulk Operations Process

```mermaid
graph TD
    A[Select Multiple Stock Takes] --> B[Validate Status = Draft]
    B --> C[Merge Product Lines]
    C --> D[Remove Duplicates]
    D --> E[Update Quantities]
    E --> F[Create Combined Stock Take]
    F --> G[Mark Originals for Deletion]
```

## Status Management

### Status Transitions

| From Status | To Status | Trigger | Business Rules |
|-------------|-----------|---------|----------------|
| Draft (0) | Approved (1) | User Approval | Must have details, validate close book |
| Draft (0) | Cancelled (2) | User Cancellation | Update status only |
| Approved (1) | Cancelled (2) | User Cancellation | Reverse inventory impact, validate close book |

### Status-Based Permissions

- **Draft (Generator)**: Full edit access, can delete
- **Approved (Approval)**: View only, can update description, can cancel with proper permissions
- **Cancelled**: View only, cannot modify

## Validation Rules

### Data Validation

1. **Stock Take Level**
   - Code must be unique within retailer
   - Description maximum length validation
   - Branch must be accessible to user
   - Adjustment date cannot be in the future

2. **Detail Level**
   - At least one detail required for approval
   - Product must exist and be valid
   - Actual count must be non-negative
   - Serial count must match actual count for serial-controlled products
   - Cost validation against maximum limits

3. **Business Logic Validation**
   - Cannot modify approved stock takes (except description)
   - Cannot delete approved stock takes after close book date
   - Cannot create/approve before close book date
   - Cannot exceed maximum total cost per line item

### Permission Validation

1. **Operation-Based Permissions**
   - `StockTake_Read`: View stock takes
   - `StockTake_Create`: Create new stock takes
   - `StockTake_Update`: Modify existing stock takes
   - `StockTake_Delete`: Delete/cancel stock takes
   - `StockTake_Finish`: Approve stock takes

2. **Data-Based Permissions**
   - Branch-level access control
   - Transaction-level user restrictions
   - Object-level access validation

## Integration Points

### Inventory Management Integration

1. **Real-time Inventory Updates**
   - Approved stock takes create InventoryTracking records
   - System maintains running inventory balances
   - Historical inventory queries for accurate system counts

2. **Product Management Integration**
   - Real-time product validation
   - Cost information retrieval
   - Serial number tracking
   - Product attribute validation

3. **Branch Management Integration**
   - Close book date validation
   - Branch access control
   - Multi-branch inventory isolation

### Audit and Notification Integration

1. **Audit Trail**
   - Complete activity logging
   - User action tracking
   - Data change history
   - System event recording

2. **Event Notifications**
   - Stock take approval notifications
   - Significant value adjustment alerts
   - System event broadcasting
   - User activity streams

## User Permissions

### Role-Based Access Control

| Role | Create | Read | Update | Delete | Approve |
|------|--------|------|--------|---------|---------|
| Inventory Manager | ✓ | ✓ | ✓ | ✓ | ✓ |
| Store Manager | ✓ | ✓ | ✓ | ✓ | ✓ |
| Assistant | ✓ | ✓ | ✓ | ✗ | ✗ |
| Viewer | ✗ | ✓ | ✗ | ✗ | ✗ |

### Data Access Restrictions

1. **Branch-Level Security**
   - Users can only access stock takes from their assigned branches
   - Cross-branch visibility restricted by configuration
   - Super admin can access all branches

2. **Transaction-Level Security**
   - Limited users can only view their own transactions
   - Full access users can view all transactions in their branch
   - Admin users have unrestricted access

## API Specifications

### Core Endpoints

#### Stock Take Management

| Endpoint | Method | Description | Permissions |
|----------|--------|-------------|-------------|
| `/stocktakes` | GET | List stock takes with filtering | StockTake_Read |
| `/stocktakes/{id}` | GET | Get specific stock take | StockTake_Read |
| `/stocktakes` | POST | Create/update stock take | StockTake_Create/Update |
| `/stocktakes/{id}` | PUT | Update description only | StockTake_Update |
| `/stocktakes/{id}` | DELETE | Cancel stock take | StockTake_Delete |

#### Detail Management

| Endpoint | Method | Description | Permissions |
|----------|--------|-------------|-------------|
| `/stocktakes/{id}/details` | GET | Get stock take details | StockTake_Read |

#### Utility Endpoints

| Endpoint | Method | Description | Permissions |
|----------|--------|-------------|-------------|
| `/stocktakes/getproductsbycategory` | POST | Get products for stock take | StockTake_Read |
| `/stocktakes/getStockListById` | GET | Get merged stock take details | StockTake_Read |
| `/stocktakes/saveStockTakeLumped` | POST | Merge multiple stock takes | StockTake_Create |

### Request/Response Models

#### StockTakeSave Request
```json
{
  "StockTake": {
    "Id": 0,
    "Code": "ST-001",
    "Description": "Monthly inventory count",
    "BranchId": 1,
    "AdjustmentDate": "2024-01-15T10:00:00Z"
  },
  "StockTakeDetail": [
    {
      "ProductId": 123,
      "ActualCount": 50,
      "SerialNumbers": "SN001,SN002",
      "IsDraft": false
    }
  ],
  "IsAdjust": true
}
```

#### Stock Take Response
```json
{
  "Id": 1,
  "Code": "ST-001",
  "Status": 1,
  "Description": "Monthly inventory count",
  "BranchId": 1,
  "CreatedDate": "2024-01-15T09:00:00Z",
  "AdjustmentDate": "2024-01-15T10:00:00Z",
  "TotalAdjustmentValue": -15.5,
  "TotalAdjustmentPrice": -450.75,
  "StockTakeDetails": []
}
```

## Error Handling

### Common Error Scenarios

1. **Validation Errors**
   - `KV001`: Invalid product selection
   - `KV002`: Serial number count mismatch
   - `KV003`: Insufficient permissions
   - `KV004`: Close book date violation
   - `KV005`: Code duplication

2. **Business Logic Errors**
   - `BL001`: Cannot modify approved stock take
   - `BL002`: Cannot delete after close book
   - `BL003`: Invalid status transition
   - `BL004`: Maximum cost exceeded

3. **System Errors**
   - `SYS001`: Database connection failure
   - `SYS002`: Inventory service unavailable
   - `SYS003`: Concurrent modification conflict

### Error Response Format

```json
{
  "IsSuccess": false,
  "ErrorCode": "KV001",
  "Message": "Product not found or inactive",
  "Details": {
    "ProductId": 123,
    "Field": "ProductId"
  }
}
```

## Performance Considerations

### Optimization Strategies

1. **Large Dataset Handling**
   - Inventory queries limited to 100 products for historical lookup
   - Bulk operations limited to 5000 items
   - Pagination for list operations

2. **Database Optimization**
   - Indexed queries on frequently used filters
   - Batch operations for multiple inserts
   - Lazy loading for navigation properties

3. **Caching Strategy**
   - Product information caching
   - Branch configuration caching
   - User permission caching

## Future Enhancements

### Planned Features

1. **Advanced Analytics**
   - Stock take variance reporting
   - Trend analysis
   - Predictive inventory alerts

2. **Mobile Integration**
   - Barcode scanning support
   - Offline capability
   - Photo documentation

3. **Integration Expansion**
   - ERP system integration
   - External inventory management
   - Third-party warehouse systems

---

*This document serves as the comprehensive business specification for the Stock Take functionality. For technical implementation details, refer to the corresponding API documentation and code comments.* 