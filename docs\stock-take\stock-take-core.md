This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: KiotViet.Persistence/StockTake.cs, KiotViet.Persistence/StockTakeDetail.cs, KiotViet.Services/Impl/StockTakeService.cs, KiotViet.Services/Impl/StockTakeDetailService.cs
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Files are sorted by Git change count (files with more changes are at the bottom)

## Additional Info

# Directory Structure
```
KiotViet.Persistence/StockTake.cs
KiotViet.Persistence/StockTakeDetail.cs
KiotViet.Services/Impl/StockTakeDetailService.cs
KiotViet.Services/Impl/StockTakeService.cs
```

# Files

## File: KiotViet.Persistence/StockTake.cs
```csharp
namespace KiotViet.Persistence
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using KiotViet.Persistence.Partial;
    using KiotViet.Persistence.Interface;
    using System.Runtime.Serialization;
    using Newtonsoft.Json;
    public partial class StockTake : IEntityId_long, ICoded, IBranchId, IRetailerId, ICreatedDate, ICreatedBy, IModifiedBy, IModifiedDate
    {
        public StockTake()
        {
            this.StockTakeDetails = new HashSet<StockTakeDetail>();
        }
    	public const string _Read = "StockTake_Read";
    	public const string _Create = "StockTake_Create";
    	public const string _Update = "StockTake_Update";
    	public const string _Delete = "StockTake_Delete";
    	public StockTake(StockTake source) : this()
    	{
    		CopyFrom(source);
    	}
    	    public long Id { get; set; }
        public string Code { get; set; }
        public int BranchId { get; set; }
        public int RetailerId { get; set; }
        public short Status { get; set; }
        public Nullable<long> AdjustedBy { get; set; }
        public Nullable<System.DateTime> AdjustmentDate { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public long CreatedBy { get; set; }
        public Nullable<long> ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public string Description { get; set; }
        public string RecentHistory { get; set; }
    	public void CopyFrom(StockTake source)
    	{
    			this.Id = source.Id;
    			this.Code = source.Code;
    			this.BranchId = source.BranchId;
    			this.RetailerId = source.RetailerId;
    			this.Status = source.Status;
    			this.AdjustedBy = source.AdjustedBy;
    			this.AdjustmentDate = source.AdjustmentDate;
    			this.CreatedDate = source.CreatedDate;
    			this.CreatedBy = source.CreatedBy;
    			this.ModifiedBy = source.ModifiedBy;
    			this.ModifiedDate = source.ModifiedDate;
    			this.Description = source.Description;
    			this.RecentHistory = source.RecentHistory;
    		}
    		    public virtual User User { get; set; }
        public virtual ICollection<StockTakeDetail> StockTakeDetails { get; set; }
        public virtual User UserCreate { get; set; }
        public virtual Branch Branch { get; set; }
        public virtual Retailer Retailer { get; set; }
    	public static Func<StockTake,StockTake> OnDeserialize()
    	{
    		return x =>{
    				if(x==null) return null;
    				x.User = null;
    				x.UserCreate = null;
    				x.Branch = null;
    				x.Retailer = null;
    				if(x.StockTakeDetails!=null)
    				{
    					foreach(var item in x.StockTakeDetails)
    					{
    						StockTakeDetail.OnDeserialize().Invoke(item);
    					}
    				}
    				return x;
    		};
    	}
    	[OnDeserialized]
    	 internal void OnDeserializedMethod(StreamingContext context)
    	{
    	      OnDeserialize().Invoke(this);
    	}
    }
}
```

## File: KiotViet.Persistence/StockTakeDetail.cs
```csharp
namespace KiotViet.Persistence
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using KiotViet.Persistence.Partial;
    using KiotViet.Persistence.Interface;
    using System.Runtime.Serialization;
    using Newtonsoft.Json;
    public partial class StockTakeDetail : IEntityId_long, IModifiedDate, ICreatedDate
    {
    	public StockTakeDetail(){}
    	public const string _Read = "StockTakeDetail_Read";
    	public const string _Create = "StockTakeDetail_Create";
    	public const string _Update = "StockTakeDetail_Update";
    	public const string _Delete = "StockTakeDetail_Delete";
    	public StockTakeDetail(StockTakeDetail source) : this()
    	{
    		CopyFrom(source);
    	}
    	    public long Id { get; set; }
        public long StockTakeId { get; set; }
        public long ProductId { get; set; }
        public double SystemCount { get; set; }
        public double ActualCount { get; set; }
        public double AdjustmentValue { get; set; }
        public string ReasonOfDiff { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public string SerialNumbers { get; set; }
        public string SystemSerialNumbers { get; set; }
        public Nullable<bool> IsDraft { get; set; }
        public Nullable<decimal> Cost { get; set; }
        public Nullable<int> OrderByNumber { get; set; }
        public System.DateTime CreatedDate { get; set; }
    	public void CopyFrom(StockTakeDetail source)
    	{
    			this.Id = source.Id;
    			this.StockTakeId = source.StockTakeId;
    			this.ProductId = source.ProductId;
    			this.SystemCount = source.SystemCount;
    			this.ActualCount = source.ActualCount;
    			this.AdjustmentValue = source.AdjustmentValue;
    			this.ReasonOfDiff = source.ReasonOfDiff;
    			this.ModifiedDate = source.ModifiedDate;
    			this.SerialNumbers = source.SerialNumbers;
    			this.SystemSerialNumbers = source.SystemSerialNumbers;
    			this.IsDraft = source.IsDraft;
    			this.Cost = source.Cost;
    			this.OrderByNumber = source.OrderByNumber;
    			this.CreatedDate = source.CreatedDate;
    		}
    		    public virtual Product Product { get; set; }
        public virtual StockTake StockTake { get; set; }
    	public static Func<StockTakeDetail,StockTakeDetail> OnDeserialize()
    	{
    		return x =>{
    				if(x==null) return null;
    				x.Product = null;
    				x.StockTake = null;
    				return x;
    		};
    	}
    	[OnDeserialized]
    	 internal void OnDeserializedMethod(StreamingContext context)
    	{
    	      OnDeserialize().Invoke(this);
    	}
    }
}
```

## File: KiotViet.Services/Impl/StockTakeDetailService.cs
```csharp
using System.Collections.Generic;
using System.Threading.Tasks;
using KiotViet.Persistence;
using KiotViet.Services.Interface;
namespace KiotViet.Services.Impl
{
    public class StockTakeDetailService : BaseService<StockTakeDetail>, IStockTakeDetailService
    {
        public StockTakeDetailService(KVEntities db, IAuthService authService)
            : base(db, authService)
        {
        }
        public async Task MultiAddAsync(IEnumerable<StockTakeDetail> entities)
        {
            await BatchAddAsync(entities);
        }
    }
}
```

## File: KiotViet.Services/Impl/StockTakeService.cs
```csharp
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using KiotViet.Persistence;
using KiotViet.Persistence.Common;
using KiotViet.Resources;
using KiotViet.Services.Common;
using KiotViet.Exceptions;
using KiotViet.Services.Interface;
using KiotViet.Utilities;
namespace KiotViet.Services.Impl
{
    public class StockTakeService : BaseService<StockTake>, IStockTakeService
    {
        #region Property
        private IStockTakeDetailService _stockTakeDetailService;
        public IStockTakeDetailService StockTakeDetailService => _stockTakeDetailService ??
                                                                 (_stockTakeDetailService =
                                                                     new StockTakeDetailService(Db, AuthService));
        private IProductService _productService;
        private IProductService ProductService
            => _productService ?? (_productService = new ProductService(Db, AuthService, PosSetting));
        private IProductSerialService _productSerialService;
        public IProductSerialService ProductSerialService
        {
            get
            {
                _productSerialService = _productSerialService ?? new ProductSerialService(Db, AuthService);
                return _productSerialService;
            }
            set
            {
                _productSerialService = value;
            }
        }
        private IProductBranchService _productBranchService;
        public IProductBranchService ProductBranchService
        {
            get
            {
                _productBranchService = _productBranchService ?? new ProductBranchService(Db, AuthService, PosSetting);
                return _productBranchService;
            }
            set
            {
                _productBranchService = value;
            }
        }
        private IBranchService _branchService;
        private IBranchService BranchService => _branchService ?? (_branchService = new BranchService(Db, AuthService, PosSetting));
        private IInventoryTrackingService _inventoryTrackingService;
        public IInventoryTrackingService InventoryTrackingService
        {
            get
            {
                _inventoryTrackingService = _inventoryTrackingService ?? new InventoryTrackingService(Db, AuthService);
                return _inventoryTrackingService;
            }
            set
            {
                _inventoryTrackingService = value;
            }
        }
        #endregion
        public const double MAX_ONHAND = 9999999999999.99;
        public const double MAX_TOTAL_COST = MAX_ONHAND * 10;
        #region Constructor
        public StockTakeService(KVEntities db, IAuthService authService, PosSetting setting)
            : base(db, authService, setting)
        {
        }
        public StockTakeService(KVEntities db)
            : base(db)
        {
        }
        public StockTakeService(KVEntities db, IAuthService authService)
            : base(db, authService)
        {
        }
        #endregion
        public async Task DeleteAsync(long id)
        {
            var existing = await GetByIdAsync(id);
            if (existing.Status == (int)StockTakeStatus.Approval)
            {
                var closeBookMessageStocktake = string.Format(KVMessage._canNotDeleteBeforeBookClosing, Labels.stocktake_Lbl);
                await BranchService.ValidCloseDate(existing.AdjustmentDate, closeBookMessageStocktake, existing.BranchId);
            }
            if (existing != null && existing.Status == (int)StockTakeStatus.Generator)
            {
                existing.Status = (int) StockTakeStatus.Cancel;
                await UpdateAsync(existing);
            }
            else if (existing != null && existing.Status == (int)StockTakeStatus.Approval)
            {
                using (var stockHelper = new TrackingHelper(AuthService.Context))
                {
                    existing.Status = (int)StockTakeStatus.Cancel;
                    stockHelper.Enqueue(existing, ImpactDirection.Remove);
                    await UpdateAsync(existing);
                }
            }
            else
            {
                throw new KvValidateException(KVMessage._GlobalDeleteReferenceDataUnsuccessfully);
            }
        }
        public async Task<StockTake> CreateOrUpdateAsync(StockTake entity, List<StockTakeDetail> itemDetails, bool isAdjust, bool? isAddNewProduct = false)
        {
            NormalizeData(itemDetails);
            var isClone = false;
            if (!string.IsNullOrEmpty(entity.Code)) isClone = entity.Code.Contains(StockTake.ClonePrefix);
            if (entity.BranchId != AuthService.Context.BranchId && isClone)
            {
                throw new KvException(KVMessage.transfer_InvalidBranch);
            }
            if (isClone)
            {
                var originCode = entity.Code.Substring(entity.Code.IndexOf(StockTake.ClonePrefix, StringComparison.Ordinal) +
                                                       StockTake.ClonePrefix.Length);
                if (originCode.Contains("."))
                {
                    int index = originCode.LastIndexOf(".", StringComparison.Ordinal);
                    var checkAfterDotIsNum = originCode.Split('.').LastOrDefault().IsNumeric();
                    if (index > 0 && checkAfterDotIsNum)
                        originCode = originCode.Substring(0, originCode.LastIndexOf(".", StringComparison.Ordinal));
                }
                var listCode = await GetAll()
                    .AsNoTracking()
                    .Where(a => a.Code.StartsWith(originCode) &&
                                a.Code.Length <= originCode.Length + StockTake.ClonedPad + 1).Select(x => x.Code)
                    .ToListAsync();
                if (listCode.Any())
                {
                    var codeExtend =
                        listCode.Where(y => y.Contains(".") && !y.EndsWith(".") &&
                                            y.Split('.').LastOrDefault().IsNumeric())
                            .Select(code => int.Parse(code.Substring(code.LastIndexOf('.') + 1)))
                            .OrderByDescending(x => x)
                            .FirstOrDefault();
                    string lastCloned;
                    if (codeExtend != 0)
                    {
                        if (originCode.EndsWith("."))
                        {
                            originCode = originCode.Remove(originCode.Length - 1);
                        }
                        lastCloned = originCode + "." + codeExtend;
                    }
                    else
                    {
                        lastCloned = originCode;
                    }
                    var num = 1;
                    var tempCode = lastCloned;
                    if (tempCode.EndsWith("."))
                    {
                        tempCode = tempCode.Remove(tempCode.Length - 1);
                    }
                    if (lastCloned.Contains(".") &&
                        lastCloned.Substring(lastCloned.LastIndexOf('.') + 1).IsNumeric() &&
                        lastCloned.Length > originCode.Length)
                    {
                        num = int.Parse(lastCloned.Substring(lastCloned.LastIndexOf('.') + 1)) + 1;
                        tempCode = lastCloned.Remove(lastCloned.LastIndexOf('.'));
                    }
                    entity.Code = $"{tempCode}.{num.ToString().PadLeft(StockTake.ClonedPad, '0')}";
                }
            }
            var branchId = entity.BranchId == 0 ? AuthService.Context.BranchId : entity.BranchId;
            if (isAdjust && !AuthService.CheckPermission(StockTake.Finish, branchId))
            {
                throw new KvValidateException(KVMessage._invalid_Permission);
            }
            if (itemDetails == null || !itemDetails.Any())
            {
                throw new KvValidateException(KVMessage.stocktake_EmptyDetail);
            }
            if (isAdjust)
            {
                var closeBookMessageStocktake = string.Format(KVMessage._canNotCreateBeforeBookClosing, Labels.stocktake_Lbl);
                await BranchService.ValidCloseDate(entity.AdjustmentDate, closeBookMessageStocktake, entity.BranchId);
            }
            var lstProductId = itemDetails.Select(d => d.ProductId).ToList();
            var lstProduct = await ProductService.GetAll().Where(p => lstProductId.Contains(p.Id)).ToListAsync();
            foreach (var detail in itemDetails)
            {
                if (detail.Cost > 0 && detail.ActualCount * (double)detail.Cost > MAX_TOTAL_COST)
                {
                    throw new KvValidateException(Labels.onHandValidate);
                }
                var obj = lstProduct.FirstOrDefault(p => p.Id == detail.ProductId);
                if (obj == null)
                {
                    throw new KvValidateException(string.Format(KVMessage.Product_Not_Exists,
                        string.IsNullOrEmpty(detail.ProductName)
                            ? detail.ProductId.ToString(CultureInfo.InvariantCulture)
                            : detail.ProductName));
                }
                if (obj.IsLotSerialControl == true)
                {
                    var numberSerials = string.IsNullOrWhiteSpace(detail.SerialNumbers)
                        ? 0
                        : detail.SerialNumbers.Split(',').Length;
                    if (detail.ActualCount != numberSerials)
                        throw new KvValidateException(KVMessage.InvalidSerialQuantity);
                }
            }
            var existing = await GetByIdAsync(entity.Id);
            var oldStatus = existing?.Status;
            using (var stockHelper = new TrackingHelper(AuthService.Context))
            {
                var childProducts = lstProduct.Where(p => p.MasterUnitId > 0).ToList();
                var masterIds = lstProduct.Select(p => p.MasterUnitId).Distinct().ToList();
                var pIds = itemDetails.Select(i => i.ProductId);
                var validProducts =
                    await
                        Db.Products.Where(p => p.RetailerId == AuthService.Context.RetailerId && pIds.Contains(p.Id))
                            .Select(p => p.Id)
                            .ToListAsync();
                if (validProducts.Count != itemDetails.Count)
                {
                    throw new KvException("InValid stock take details");
                }
                var actualItems = itemDetails.Where(i => validProducts.Contains(i.ProductId));
                if (!actualItems.Any())
                {
                    throw new KvException("Empty stock take details");
                }
                if (existing != null)
                {
                    if (await GetAll().AnyAsync(c => c.Code.Equals(entity.Code, StringComparison.CurrentCulture) && c.Id != entity.Id))
                    {
                        throw new KvValidateException(string.Format(KVMessage._GlobalDuplicateData, entity.Code));
                    }
                    if (existing.Status == (int)StockTakeStatus.Approval)
                        throw new KvValidateException(KVMessage._GlobalErrorSummary);
                    if (entity.AdjustmentDate != null)
                        existing.AdjustmentDate = entity.AdjustmentDate;
                    if (isAdjust)
                    {
                        existing.Status = (int)StockTakeStatus.Approval;
                        existing.AdjustedBy = AuthService.Context.User.Id;
                        if (entity.AdjustmentDate == null)
                            existing.AdjustmentDate = DateTime.Now;
                        var r = (existing.AdjustmentDate ?? DateTime.Now).RoughCompare(DateTime.Now) <= 0;
                        if (!r)
                        {
                            throw new KvValidateException(KVMessage.GreaterThanNow);
                        }
                    }
                    if (string.IsNullOrWhiteSpace(entity.Code))
                    {
                        throw new KvValidateException(KVMessage.stocktake_CodeEmpty);
                    }
                    existing.Code = entity.Code;
                    existing.CreatedDate = entity.CreatedDate;
                    existing.Description = entity.Description;
                    existing.BranchId = entity.BranchId;
                    existing.RecentHistory = entity.RecentHistory;
                    await UpdateAsync(existing);
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(entity.Code) && await GetAll().AnyAsync(c => c.Code.Equals(entity.Code, StringComparison.CurrentCulture)))
                    {
                        throw new KvValidateException(string.Format(KVMessage._GlobalDuplicateData, entity.Code));
                    }
                    existing = new StockTake();
                    if (entity.AdjustmentDate != null)
                        existing.AdjustmentDate = entity.AdjustmentDate;
                    if (isAdjust)
                    {
                        existing.Status = (int)StockTakeStatus.Approval;
                        existing.AdjustedBy = AuthService.Context.User.Id;
                        if (entity.AdjustmentDate == null)
                            existing.AdjustmentDate = DateTime.Now;
                        var r = (existing.AdjustmentDate ?? DateTime.Now).RoughCompare(DateTime.Now) <= 0;
                        if (!r)
                        {
                            throw new KvValidateException(KVMessage.GreaterThanNow);
                        }
                    }
                    existing.Code = entity.Code;
                    existing.CreatedDate = entity.CreatedDate;
                    existing.Description = entity.Description;
                    existing.BranchId = entity.BranchId;
                    existing.RecentHistory = entity.RecentHistory;
                    await AddAsync(existing);
                }
                if (existing.StockTakeDetails != null)
                {
                    if (oldStatus == null || oldStatus != (int)StockTakeStatus.Generator)
                    {
                        stockHelper.Enqueue(existing, ImpactDirection.Remove);
                    }
                    Db.StockTakeDetails.RemoveRange(existing.StockTakeDetails);
                    await Db.SaveChangesAsync();
                }
                var inventory = new List<InventoryOfProduct>();
                if (itemDetails.Count < 101)
                {
                    var masterProductIds = masterIds.Count > 0 ? await ProductService.GetAll().Where(p => masterIds.Contains(p.Id)).Select(p => p.Id).ToListAsync() : new List<long>();
                    var trackingProductIds = validProducts.Union(masterProductIds).ToList();
                    inventory = await GetInventoryByTime(trackingProductIds, entity.AdjustmentDate ?? DateTime.Now);
                }
                else
                {
                    inventory = await Db.ProductBranches
                        .Where(b => validProducts.Contains(b.ProductId) && b.BranchId == existing.BranchId
                                    && b.RetailerId == AuthService.Context.RetailerId)
                        .Select(p=> new InventoryOfProduct { ProductId = p.ProductId, EndingStocks = p.OnHand})
                        .ToListAsync();
                }
                var listProductID = actualItems.Select(x => x.ProductId).ToList();
                var productLstCost = await (from product in ProductService.GetAll().Where(p => listProductID.Contains(p.Id))
                                            join productBranch in ProductBranchService.GetAll().Where(pb => pb.BranchId == branchId)
                                            on product.MasterUnitId ?? product.Id equals productBranch.ProductId
                                            select new { product.Id, product.MasterUnitId, product.ConversionValue, productBranch.Cost }).ToListAsync();
                var dictProductCost = productLstCost
                    .Select(p => new { p.Id, ConversionValue = ((p.MasterUnitId == null && p.ConversionValue <= 0) ? 1 : p.ConversionValue), p.Cost })
                    .ToDictionary(p => p.Id, p => (decimal)p.ConversionValue * p.Cost);
                foreach (var item in actualItems)
                {
                    double endingStocks = 0;
                    var childProduct = childProducts.FirstOrDefault(p => p.Id == item.ProductId);
                    if (childProduct != null) {
                        var record = inventory.SingleOrDefault(p => p.ProductId == childProduct.MasterProductId);
                        if (record != null)
                        {
                            if (childProduct.ConversionValue > 0)
                            {
                                endingStocks = record.EndingStocks / childProduct.ConversionValue;
                            }
                        }
                    } else
                    {
                        var record = inventory.SingleOrDefault(p => p.ProductId == item.ProductId);
                        if (record != null)
                        {
                            endingStocks = record.EndingStocks;
                        }
                    }
                    item.SystemCount = (isAddNewProduct != null && isAddNewProduct == false) ? endingStocks : 0;
                    item.StockTakeId = existing.Id;
                    var currentSerials = await Db.ProductSerials.Where(a => a.ProductId == item.ProductId && a.BranchId == existing.BranchId && a.Status > 0).ToListAsync();
                    var beforeSerials = currentSerials.Aggregate("", (current, sr) => current + ("," + sr.SerialNumber));
                    if (!string.IsNullOrEmpty(beforeSerials))
                        beforeSerials = beforeSerials.Substring(1); // save to table stocktake detail
                    item.SystemSerialNumbers = beforeSerials;
                    decimal productCost;
                    dictProductCost.TryGetValue(item.ProductId, out productCost);
                    item.Cost = productCost;
                }
                await StockTakeDetailService.MultiAddAsync(actualItems);
                existing.StockTakeDetails =
                    await StockTakeDetailService.GetAll().Where(d => d.StockTakeId == existing.Id).ToListAsync();
                stockHelper.Enqueue(existing, ImpactDirection.Add);
            }
            //End
            return existing;
        }
        private void NormalizeData(List<StockTakeDetail> itemDetails)
        {
            if (itemDetails == null || !itemDetails.Any())
            {
                return;
            }
            foreach (var detail in itemDetails)
            {
                if (string.IsNullOrEmpty(detail.SerialNumbers))
                {
                    continue;
                }
                detail.SerialNumbers = Regex.Replace(detail.SerialNumbers, @"\u200E", string.Empty);
            }
        }
        public async Task<string> GetNewCode(bool useDbContext = true)
        {
            var nextCode = useDbContext ? await GetNextCodeAsync() : await GenerateNextCodeWithoutDbContext();
            return nextCode;
        }
        public async Task<object> CreateStockTakeForUpdateProduct(long productId, string productCode, double actualCount, int branchId, int retailerId, long? isAdd)
        {
            var objStockTake = new StockTake
            {
                Description = isAdd > 0 ? KVMessage.stocktake_StockTakeCreatedautomatically + productCode : KVMessage.stoctake_CreatedAutoWhenAdd + productCode,
                CreatedDate = DateTime.Now,
                BranchId = branchId,
                RetailerId = retailerId
            };
            var listStockTake = new List<StockTakeDetail>
            {
                new StockTakeDetail {ProductId = productId, ActualCount = actualCount, SystemCount = 0}
            };
            return await CreateOrUpdateAsync(objStockTake, listStockTake, true);
        }
        public async Task<StockTake> CreateStockTakeForUpdateMultiProductAsync(int retailerId, int branchId, List<ProductAddStockTake> products, bool isAdd)
        {
            if (products == null || products.Count() == 0) return null;
            var strProductCodes = string.Join(", ", products.Select(s => s.ProductCode).ToArray());
            var objStockTake = new StockTake
            {
                Description = !isAdd ? KVMessage.stocktake_StockTakeCreatedautomatically + strProductCodes : KVMessage.stoctake_CreatedAutoWhenAdd + strProductCodes,
                CreatedDate = DateTime.Now,
                BranchId = branchId,
                RetailerId = retailerId
            };
            var listStockTake = products.Select(p => new StockTakeDetail
            {
                ProductId = p.ProductId,
                ActualCount = p.ActualCount,
                SystemCount = 0
            }).ToList();
            return await CreateOrUpdateAsync(objStockTake, listStockTake, true, isAdd);
        }
        public async Task<KeyValuePair<string, bool>> IsHaveStockTakeNewer(int branchId, long productId,
            DateTime transDate, DateTime? newTransDate, long docId, int docType)
        {
            if (newTransDate == null)
            {
                var stockTakeLast =
                    await
                        Db.StockTakes
                            .Where(
                                x => x.RetailerId == AuthService.Context.RetailerId &&
                                     x.BranchId == branchId && x.Status == (int)StockTakeStatus.Approval &&
                                     x.AdjustmentDate >= transDate &&
                                     x.StockTakeDetails.Any(s => s.ProductId == productId))
                            .OrderBy(x => x.AdjustmentDate)
                            .Select(x => x.Code)
                            .FirstOrDefaultAsync();
                return stockTakeLast == null
                    ? new KeyValuePair<string, bool>(string.Empty, true)
                    : new KeyValuePair<string, bool>(stockTakeLast, false);
            }
            if (transDate == newTransDate) return new KeyValuePair<string, bool>(string.Empty, true);
            var dateFrom = transDate;
            var dateTo = transDate;
            var typeCheck = 1;
            if (transDate < newTransDate)
            {
                dateTo = newTransDate.Value;
            }
            else
            {
                dateFrom = newTransDate.Value;
                typeCheck = 2;
            }
            var isExists =
                await
                    InventoryTrackingService.IsExistsAnyTrans(branchId, productId, dateFrom, dateTo,
                        docId, docType);
            if (!isExists) return new KeyValuePair<string, bool>(string.Empty, true);
            var stockTakePrevious =
                await
                    Db.StockTakes
                        .Where(
                            x => x.RetailerId == AuthService.Context.RetailerId &&
                                 x.BranchId == branchId && x.Status == (int)StockTakeStatus.Approval &&
                                 ((typeCheck == 1 && x.AdjustmentDate <= newTransDate) ||
                                  (typeCheck == 2 && x.AdjustmentDate >= newTransDate))
                                 && x.StockTakeDetails.Any(s => s.ProductId == productId))
                        .OrderBy(x => x.AdjustmentDate)
                        .Select(x => x.Code)
                        .FirstOrDefaultAsync();
            return stockTakePrevious != null
                ? new KeyValuePair<string, bool>(stockTakePrevious, false)
                : new KeyValuePair<string, bool>(string.Empty, true);
        }
        protected override void Guard(StockTake entity, Ops op)
        {
            try
            {
                base.Guard(entity, op);
            }
            catch (KvUnauthorizedException e)
            {
                Log.Info(e.Message, e);
                if ((op == Ops.Update || op == Ops.Delete) && entity != null)
                {
                    var originStatus = Db.Entry(entity).Property(u => u.Status).OriginalValue;
                    if (originStatus == (int)StockTakeStatus.Generator)
                    {
                        Log.Warn("Changing temp. stock take. Check on Create right");
                        base.Guard(entity, Ops.Create);
                    }
                    else
                    {
                        throw;
                    }
                }
                else
                {
                    throw;
                }
            }
        }
        public async Task<StockTake> UpdateDescriptionAsync(long id, string desc)
        {
            if (id <= 0)
            {
                throw new KvValidateException(KVMessage._GlobalErrorSummary);
            }
            var existing = await GetByIdAsync(id);
            if (existing == null || existing.Status != (int) StockTakeStatus.Approval)
            {
                throw new KvValidateException(KVMessage._GlobalErrorSummary);
            }
            existing.Description = desc;
            await UpdateAsync(existing);
            return existing;
        }
        public IQueryable<Activity> GetStream(long? userId, int? pageIndex = null, int? pageSize = null)
        {
            if (!AuthService.IsAllowed<StockTake>(null, Ops.Read))
            {
                return null;
            }
            return (from s in Db.StockTakes.Where(x => x.RetailerId == AuthService.Context.RetailerId)
                    join u in Db.Users.Where(x => x.RetailerId == AuthService.Context.RetailerId)
                        on s.CreatedBy equals u.Id
                    where s.BranchId == AuthService.Context.BranchId
                        && s.Status == (int)StockTakeStatus.Approval
                        && (userId == null || s.CreatedBy == userId)
                    select new Activity
                    {
                        Action = Labels.doashboard_With,
                        SubjectCode = s.Code,
                        SubjectId = s.Id,
                        SubjectLabel = Labels.doashboard_Stocktake,
                        SubjectType = "StockTakes",
                        Timestamp = s.CreatedDate,
                        UserId = s.CreatedBy,
                        UserName = u.GivenName,
                        Value = 0
                    });
        }
        public async Task<object> CreateStockTakeForDeleteProduct(long productId, string productCode, double actualCount, int branchId, int retailerId)
        {
            var objStockTake = new StockTake
            {
                Description = KVMessage.stocktake_StockTakeDeletedautomatically + productCode,
                CreatedDate = DateTime.Now,
                BranchId = branchId,
                RetailerId = retailerId
            };
            var listStockTake = new List<StockTakeDetail>
            {
                new StockTakeDetail {ProductId = productId, ActualCount = actualCount, SystemCount = 0}
            };
            return await CreateOrUpdateAsync(objStockTake, listStockTake, true);
        }
        public async Task<object> CreateStockTakeForDeleteMultiProduct(int retailerId, int branchId, List<ProductBranch> products)
        {
            if (products == null || !products.Any()) return null;
            var objStockTake = new StockTake
            {
                Description = KVMessage.stocktake_StockTakeDeletedMultiAutomatically,
                CreatedDate = DateTime.Now,
                BranchId = branchId,
                RetailerId = retailerId
            };
            var listStockTake = products.Select(p => new StockTakeDetail
            {
                ProductId = p.ProductId,
                ActualCount = 0,
                SystemCount = 0
            }).ToList();
            return await CreateOrUpdateAsync(objStockTake, listStockTake, true);
        }
        private async Task<List<InventoryOfProduct>> GetInventoryByTime(List<long> productIds, DateTime time)
        {
            var res = new List<InventoryOfProduct>();
            IQueryable<InventoryOfProduct> query = null;
            int i = 0;
            foreach (var productId in productIds)
            {
                var tempQuery = InventoryTrackingService.GetAll()
                .Where(p => p.BranchId == AuthService.Context.BranchId
                    && p.TransDate <= time
                    && p.ProductId == productId)
                .OrderByDescending(p => p.TransDate)
                .Select(p => new InventoryOfProduct { ProductId = p.ProductId, EndingStocks = p.EndingStocks })
                .Take(1);
                if (query == null)
                {
                    query = tempQuery;
                }
                else
                {
                    query = query.Concat(tempQuery);
                }
                if ((i > 0 && i % 10 == 0) || i == productIds.Count - 1)
                {
                    res.AddRange(await query.ToListAsync());
                    query = null;
                    i++;
                    continue;
                }
                i++;
            }
            return res;
        }
        class InventoryOfProduct
        {
            public long ProductId { get; set; }
            public double EndingStocks { get; set; }
        }
    }
}
```
