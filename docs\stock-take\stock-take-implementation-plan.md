# Stock Take Implementation Plan

## Table of Contents

1. [Overview](#overview)
2. [Module Architecture](#module-architecture)
3. [Implementation Phases](#implementation-phases)
4. [Domain Layer Implementation](#domain-layer-implementation)
5. [Application Layer Implementation](#application-layer-implementation)
6. [Infrastructure Layer Implementation](#infrastructure-layer-implementation)
7. [Presentation Layer Implementation](#presentation-layer-implementation)
8. [Database Schema](#database-schema)
9. [Testing Strategy](#testing-strategy)
10. [Integration Points](#integration-points)
11. [Development Checklist](#development-checklist)

## Overview

This implementation plan provides a comprehensive roadmap for developing the Stock Take functionality within the KvFnB Core system. The implementation follows hexagonal architecture principles, Domain-Driven Design (DDD), and the established project patterns.

### Implementation Scope
- Complete Stock Take management system
- Support for physical inventory counting
- Inventory adjustment processing
- Integration with existing product and inventory systems
- Multi-tenant branch isolation
- Comprehensive audit trail

### Key Business Requirements
- Branch-specific stock take operations
- Serial number tracking for controlled products
- Automated inventory adjustments upon approval
- Role-based access control
- Historical inventory tracking
- Bulk operations support

## Module Architecture

### Directory Structure
```
src/Modules/StockTake/
├── Domain/
│   ├── Models/          # ✅ IMPLEMENTED
│   │   └── StockTake.cs # ✅ EXISTS in Inventory module
│   ├── Entities/        # ✅ IMPLEMENTED 
│   │   └── StockTakeDetail.cs # ✅ EXISTS in Inventory module
│   ├── ValueObjects/
│   │   └── StockTakeStatus.cs
│   ├── Events/
│   │   ├── StockTakeApprovedEvent.cs
│   │   ├── StockTakeCancelledEvent.cs
│   │   └── StockTakeCreatedEvent.cs
│   ├── Repositories/    # ✅ PARTIALLY IMPLEMENTED
│   │   ├── IStockTakeRepository.cs # ✅ EXISTS in Inventory module (basic)
│   │   └── IStockTakeDetailRepository.cs
│   └── Services/        # ✅ INTERFACE EXISTS
│       └── Interfaces/
│           └── IStockTakeDomainService.cs # ✅ EXISTS in Inventory module (empty)
├── Application/
│   ├── Contracts/
│   │   ├── StockTakeDto.cs
│   │   ├── StockTakeDetailDto.cs
│   │   └── ProductForStockTakeDto.cs
│   └── UseCases/
│       ├── StockTakeUseCase/
│       │   ├── CreateStockTake/
│       │   ├── UpdateStockTake/
│       │   ├── ApproveStockTake/
│       │   ├── CancelStockTake/
│       │   ├── GetStockTake/
│       │   ├── ListStockTakes/
│       │   ├── DeleteStockTake/
│       │   ├── CloneStockTake/
│       │   ├── MergeStockTakes/
│       │   └── GetProductsByCategory/
├── Infrastructure/
│   ├── DependencyInjection/
│   │   └── StockTakeModuleRegistrar.cs
│   ├── Mapping/
│   │   └── StockTakeMappingProfile.cs
│   ├── Repositories/
│   │   ├── StockTakeRepository.cs
│   │   └── StockTakeDetailRepository.cs
│   └── Services/
│       ├── InventoryService.cs
│       ├── ProductValidationService.cs
│       └── StockTakeDomainService.cs
├── Presentation/
│   └── Restful/
│       └── StockTakeController.cs
└── Tests/
    ├── Domain/
    ├── Application/
    │   └── UseCases/
    └── Infrastructure/
```

**Current Implementation Status:**
- ✅ StockTake entity exists in `src/Modules/Inventory/Domain/Models/StockTake.cs`
- ✅ StockTakeDetail entity exists in `src/Modules/Inventory/Domain/Entities/StockTakeDetail.cs`
- ✅ IStockTakeRepository interface exists in `src/Modules/Inventory/Domain/Repositories/IStockTakeRepository.cs`
- ✅ IStockTakeDomainService interface exists in `src/Modules/Inventory/Domain/Services/Interfaces/IStockTakeDomainService.cs`

## Implementation Phases

### Phase 1: Foundation (Week 1-2)
- Domain entities and value objects
- Repository interfaces
- Basic DTO models
- Core validation rules
- Database schema design

### Phase 2: Core Use Cases (Week 3-4)
- Create Stock Take use case
- Update Stock Take use case
- Get Stock Take use case
- List Stock Takes use case
- Basic validation and mapping

### Phase 3: Advanced Operations (Week 5-6)
- Approve Stock Take use case
- Cancel Stock Take use case
- Delete Stock Take use case
- Inventory integration
- Serial number handling

### Phase 4: Bulk Operations (Week 7-8)
- Clone Stock Take use case
- Merge Stock Takes use case
- Get Products by Category use case
- Performance optimization
- Advanced validation

### Phase 5: Integration & Testing (Week 9-10)
- API controller implementation
- Integration testing
- Performance testing
- Security testing
- Documentation completion

## Domain Layer Implementation

### Entities

#### StockTake Entity (✅ IMPLEMENTED)
**Location:** `src/Modules/Inventory/Domain/Models/StockTake.cs`

```csharp
namespace KvFnB.Modules.Inventory.Domain.Models
{
    public class StockTake : AggregateRoot<long>, IAuditableEntity, ICode
    {
        public string Code { get; set; }
        public int BranchId { get; set; }
        public int RetailerId { get; set; }
        public int Status { get; set; }
        public string RecentHistory { get; set; }
        public long AdjustedBy { get; set; }
        public DateTime AdjustmentDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public long CreatedBy { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public long? ModifiedBy { get; set; }

        // Factory method and business methods implemented
        public static StockTake Create(string code, int branchId, int retailerId, int status, long adjustedBy, DateTime adjustmentDate)
        public void UpdateCode(string code)
    }
}
```

#### StockTakeDetail Entity (✅ IMPLEMENTED)
**Location:** `src/Modules/Inventory/Domain/Entities/StockTakeDetail.cs`

```csharp
namespace KvFnB.Modules.Inventory.Domain.Entities
{
    public class StockTakeDetail : Entity<long>, ICreatedAt
    {
        public long StockTakeId { get; set; }
        public long ProductId { get; set; }
        public double SystemCount { get; set; }
        public double ActualCount { get; set; }
        public double AdjustmentValue { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public bool? IsDraft { get; set; }
        public decimal? Cost { get; set; }
        public int? OrderByNumber { get; set; }
        public DateTime CreatedAt { get; set; }

        // Factory method implemented
        public static StockTakeDetail Create(long stockTakeId, long productId, double actualCount, double systemCount)
    }
}
```

**Note:** The current implementation needs to be enhanced with:
- Navigation properties between StockTake and StockTakeDetail
- Domain events
- Additional business methods
- Missing properties like SerialNumbers, ReasonOfDiff, SystemSerialNumbers

#### Repository Interfaces (✅ PARTIALLY IMPLEMENTED)

**Current Implementation:** `src/Modules/Inventory/Domain/Repositories/IStockTakeRepository.cs`
```csharp
namespace KvFnB.Modules.Inventory.Domain.Repositories
{
    public interface IStockTakeRepository : IRepository<StockTake, long>
    {
        // Basic repository - needs enhancement
    }
}
```

**Required Enhancements:**
```csharp
namespace KvFnB.Modules.Inventory.Domain.Repositories
{
    public interface IStockTakeRepository : IRepository<StockTake, long>
    {
        Task<StockTake> GetByIdWithDetailsAsync(long id, CancellationToken cancellationToken = default);
        Task<IEnumerable<StockTake>> GetByBranchAsync(int branchId, CancellationToken cancellationToken = default);
        Task<bool> IsCodeUniqueAsync(string code, int retailerId, long? excludeId = null, CancellationToken cancellationToken = default);
        Task<IEnumerable<StockTake>> GetForMergeAsync(IEnumerable<long> ids, CancellationToken cancellationToken = default);
    }

    // Still needs to be created
    public interface IStockTakeDetailRepository : IRepository<StockTakeDetail, long>
    {
        Task<IEnumerable<StockTakeDetail>> GetByStockTakeIdAsync(long stockTakeId, CancellationToken cancellationToken = default);
        Task<bool> HasDetailsAsync(long stockTakeId, CancellationToken cancellationToken = default);
    }
}
```

### Domain Services (✅ INTERFACE EXISTS)

**Current Implementation:** `src/Modules/Inventory/Domain/Services/Interfaces/IStockTakeDomainService.cs`
```csharp
namespace KvFnB.Modules.Inventory.Domain.Services.Interfaces
{
    public interface IStockTakeDomainService
    {
        // Empty interface - needs implementation
    }
}
```

**Required Implementation:**
```csharp
namespace KvFnB.Modules.Inventory.Domain.Services.Interfaces
{
    public interface IStockTakeDomainService
    {
        Task<double> CalculateSystemCountAsync(long productId, int branchId, CancellationToken cancellationToken = default);
        Task<IEnumerable<string>> GetSystemSerialNumbersAsync(long productId, int branchId, CancellationToken cancellationToken = default);
        Task ValidateProductForStockTakeAsync(long productId, int branchId, CancellationToken cancellationToken = default);
        Task<bool> CanApproveStockTakeAsync(long stockTakeId, CancellationToken cancellationToken = default);
        Task CreateInventoryAdjustmentsAsync(StockTake stockTake, CancellationToken cancellationToken = default);
    }
}
```

## Application Layer Implementation

### DTO Models (Contracts)

#### StockTakeDto.cs
```csharp
namespace KvFnB.Modules.StockTake.Application.Contracts
{
    /// <summary>
    /// Represents a stock take data transfer object
    /// </summary>
    public record StockTakeDto
    {
        /// <summary>
        /// The unique identifier of the stock take
        /// </summary>
        [JsonPropertyName("id")]
        public long Id { get; init; }

        /// <summary>
        /// The business code for the stock take
        /// </summary>
        [JsonPropertyName("code")]
        public string Code { get; init; } = string.Empty;

        /// <summary>
        /// The branch identifier where the stock take is performed
        /// </summary>
        [JsonPropertyName("branch_id")]
        public int BranchId { get; init; }

        /// <summary>
        /// The current status of the stock take (0=Draft, 1=Approved, 2=Cancelled)
        /// </summary>
        [JsonPropertyName("status")]
        public short Status { get; init; }

        /// <summary>
        /// The business description of the stock take
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; init; } = string.Empty;

        /// <summary>
        /// The date when the stock take was approved
        /// </summary>
        [JsonPropertyName("adjustment_date")]
        public DateTime? AdjustmentDate { get; init; }

        /// <summary>
        /// The total adjustment value across all products
        /// </summary>
        [JsonPropertyName("total_adjustment_value")]
        public double TotalAdjustmentValue { get; init; }

        /// <summary>
        /// The collection of stock take detail items
        /// </summary>
        [JsonPropertyName("details")]
        public IEnumerable<StockTakeDetailDto> Details { get; init; } = Enumerable.Empty<StockTakeDetailDto>();
    }
}
```

### Use Case Implementation Examples

#### CreateStockTakeUseCase
```csharp
namespace KvFnB.Modules.StockTake.Application.UseCases.StockTakeUseCase.CreateStockTake
{
    /// <summary>
    /// Implements the CreateStockTake use case for creating new stock take records
    /// </summary>
    public class CreateStockTakeUseCase : UseCaseBase<CreateStockTakeRequest, CreateStockTakeResponse>
    {
        private readonly IStockTakeRepository _stockTakeRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ITenantProvider _tenantProvider;
        private readonly IUserProvider _userProvider;

        public CreateStockTakeUseCase(
            IValidator<CreateStockTakeRequest> validator,
            IStockTakeRepository stockTakeRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ITenantProvider tenantProvider,
            IUserProvider userProvider)
        {
            _stockTakeRepository = stockTakeRepository ?? throw new ArgumentNullException(nameof(stockTakeRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _userProvider = userProvider ?? throw new ArgumentNullException(nameof(userProvider));
        }

        public override async Task<Result<CreateStockTakeResponse>> ExecuteAsync(
            CreateStockTakeRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Business logic implementation
                var stockTake = Domain.Entities.StockTake.Create(
                    request.Code,
                    request.BranchId ?? _tenantProvider.GetBranchId(),
                    _tenantProvider.GetTenantId(),
                    _userProvider.GetUserId(),
                    request.Description);

                // Add details if provided
                if (request.Details?.Any() == true)
                {
                    foreach (var detail in request.Details)
                    {
                        stockTake.AddDetail(detail.ProductId, detail.ActualCount, detail.SerialNumbers);
                    }
                }

                var createdStockTake = await _stockTakeRepository.AddAsync(stockTake, cancellationToken);
                await _unitOfWork.CommitAsync(cancellationToken);

                var response = _mapper.Map<CreateStockTakeResponse>(createdStockTake);
                return Result<CreateStockTakeResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating stock take");
                return Result<CreateStockTakeResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
    }
}
```

#### ApproveStockTakeUseCase
```csharp
namespace KvFnB.Modules.StockTake.Application.UseCases.StockTakeUseCase.ApproveStockTake
{
    /// <summary>
    /// Implements the ApproveStockTake use case for approving stock take and creating inventory adjustments
    /// </summary>
    public class ApproveStockTakeUseCase : UseCaseBase<ApproveStockTakeRequest, ApproveStockTakeResponse>
    {
        private readonly IStockTakeRepository _stockTakeRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserProvider _userProvider;
        private readonly IInventoryService _inventoryService;

        public override async Task<Result<ApproveStockTakeResponse>> ExecuteAsync(
            ApproveStockTakeRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var stockTake = await _stockTakeRepository.GetByIdWithDetailsAsync(request.Id, cancellationToken);
                if (stockTake == null)
                {
                    return Result<ApproveStockTakeResponse>.Failure($"Stock take with ID {request.Id} not found");
                }

                // Approve the stock take
                stockTake.Approve(_userProvider.GetUserId(), request.AdjustmentDate);

                // Create inventory adjustments
                await _inventoryService.CreateInventoryAdjustmentsAsync(stockTake, cancellationToken);

                await _stockTakeRepository.UpdateAsync(stockTake, cancellationToken);
                await _unitOfWork.CommitAsync(cancellationToken);

                var response = _mapper.Map<ApproveStockTakeResponse>(stockTake);
                return Result<ApproveStockTakeResponse>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving stock take {StockTakeId}", request.Id);
                return Result<ApproveStockTakeResponse>.Failure(ErrorMessages.InternalServerError);
            }
        }
    }
}
```

### Validators

#### CreateStockTakeValidator
```csharp
namespace KvFnB.Modules.StockTake.Application.UseCases.StockTakeUseCase.CreateStockTake
{
    /// <summary>
    /// Validates the CreateStockTake request
    /// </summary>
    public class CreateStockTakeValidator : Validator<CreateStockTakeRequest>
    {
        public CreateStockTakeValidator()
        {
            RuleFor(x => x.Code)
                .MaxLength(40, "Code must be less than 40 characters");

            RuleFor(x => x.Description)
                .MaxLength(500, "Description must be less than 500 characters");

            RuleFor(x => x.BranchId)
                .GreaterThan(0, "Branch ID must be valid")
                .When(x => x.BranchId.HasValue);

            // Collection validation for details
            RuleFor(x => (IEnumerable<StockTakeDetailRequest>)x.Details)
                .ForEach(detail =>
                {
                    detail.Rule(x => x.ProductId)
                        .GreaterThan(0, "Product ID must be valid");

                    detail.Rule(x => x.ActualCount)
                        .GreaterThanOrEqualTo(0, "Actual count must be non-negative");
                })
                .When(x => x.Details?.Any() == true);
        }
    }
}
```

## Infrastructure Layer Implementation

### Repository Implementation
```csharp
namespace KvFnB.Modules.StockTake.Infrastructure.Repositories
{
    public class StockTakeRepository : Repository<Domain.Entities.StockTake>, IStockTakeRepository
    {
        public StockTakeRepository(DbContext context) : base(context) { }

        public async Task<Domain.Entities.StockTake> GetByIdWithDetailsAsync(long id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(st => st.Details)
                .FirstOrDefaultAsync(st => st.Id == id, cancellationToken);
        }

        public async Task<bool> IsCodeUniqueAsync(string code, int retailerId, long? excludeId = null, CancellationToken cancellationToken = default)
        {
            var query = _dbSet.Where(st => st.Code == code && st.RetailerId == retailerId);
            
            if (excludeId.HasValue)
            {
                query = query.Where(st => st.Id != excludeId.Value);
            }

            return !await query.AnyAsync(cancellationToken);
        }
    }
}
```

### Mapping Profile
```csharp
namespace KvFnB.Modules.StockTake.Infrastructure.Mapping
{
    public class StockTakeMappingProfile : Profile
    {
        public StockTakeMappingProfile()
        {
            // Entity to DTO mappings
            CreateMap<Domain.Entities.StockTake, StockTakeDto>()
                .ForMember(dest => dest.TotalAdjustmentValue, opt => opt.MapFrom(src => src.Details.Sum(d => d.AdjustmentValue)));

            CreateMap<Domain.Entities.StockTakeDetail, StockTakeDetailDto>();

            // Request/Response mappings
            CreateMap<CreateStockTakeRequest, Domain.Entities.StockTake>()
                .ConstructUsing((src, ctx) => Domain.Entities.StockTake.Create(
                    src.Code, 
                    src.BranchId ?? 0, 
                    0, 
                    0, 
                    src.Description));

            CreateMap<Domain.Entities.StockTake, CreateStockTakeResponse>()
                .ForMember(dest => dest.StockTake, opt => opt.MapFrom(src => src));
        }
    }
}
```

### Dependency Injection Registration
```csharp
namespace KvFnB.Modules.StockTake.Infrastructure.DependencyInjection
{
    public static class StockTakeModuleRegistrar
    {
        public static IServiceCollection RegisterStockTakeModule(this IServiceCollection services)
        {
            // Register repositories
            services.AddScoped<IStockTakeRepository, StockTakeRepository>();
            services.AddScoped<IStockTakeDetailRepository, StockTakeDetailRepository>();

            // Register use cases
            services.AddScoped<CreateStockTakeUseCase>();
            services.AddScoped<UpdateStockTakeUseCase>();
            services.AddScoped<ApproveStockTakeUseCase>();
            services.AddScoped<CancelStockTakeUseCase>();
            services.AddScoped<GetStockTakeUseCase>();
            services.AddScoped<ListStockTakesUseCase>();
            services.AddScoped<DeleteStockTakeUseCase>();
            services.AddScoped<CloneStockTakeUseCase>();
            services.AddScoped<MergeStockTakesUseCase>();
            services.AddScoped<GetProductsByCategoryUseCase>();

            // Register validators
            services.AddScoped<IValidator<CreateStockTakeRequest>, CreateStockTakeValidator>();
            services.AddScoped<IValidator<UpdateStockTakeRequest>, UpdateStockTakeValidator>();
            services.AddScoped<IValidator<ApproveStockTakeRequest>, ApproveStockTakeValidator>();
            services.AddScoped<IValidator<CancelStockTakeRequest>, CancelStockTakeValidator>();
            services.AddScoped<IValidator<GetStockTakeRequest>, GetStockTakeValidator>();
            services.AddScoped<IValidator<ListStockTakesRequest>, ListStockTakesValidator>();
            services.AddScoped<IValidator<DeleteStockTakeRequest>, DeleteStockTakeValidator>();
            services.AddScoped<IValidator<CloneStockTakeRequest>, CloneStockTakeValidator>();
            services.AddScoped<IValidator<MergeStockTakesRequest>, MergeStockTakesValidator>();
            services.AddScoped<IValidator<GetProductsByCategoryRequest>, GetProductsByCategoryValidator>();

            // Register services
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<IProductValidationService, ProductValidationService>();

            return services;
        }
    }
}
```

## Presentation Layer Implementation

### API Controller
```csharp
namespace KvFnB.Modules.StockTake.Presentation.Restful
{
    [ApiController]
    [Route("api/[controller]")]
    public class StockTakeController : ControllerBase
    {
        private readonly CreateStockTakeUseCase _createUseCase;
        private readonly GetStockTakeUseCase _getUseCase;
        private readonly ApproveStockTakeUseCase _approveUseCase;
        // Other use cases...

        [HttpPost]
        [HasPermission("StockTake_Create")]
        public async Task<IActionResult> CreateStockTake([FromBody] CreateStockTakeRequest request)
        {
            var result = await _createUseCase.ExecuteAsync(request);
            return result.IsSuccess ? Ok(result.Value) : BadRequest(result.Errors);
        }

        [HttpGet("{id}")]
        [HasPermission("StockTake_Read")]
        public async Task<IActionResult> GetStockTake(long id)
        {
            var request = new GetStockTakeRequest { Id = id };
            var result = await _getUseCase.ExecuteAsync(request);
            return result.IsSuccess ? Ok(result.Value) : NotFound(result.Errors);
        }

        [HttpPut("{id}/approve")]
        [HasPermission("StockTake_Finish")]
        public async Task<IActionResult> ApproveStockTake(long id, [FromBody] ApproveStockTakeRequest request)
        {
            request.Id = id;
            var result = await _approveUseCase.ExecuteAsync(request);
            return result.IsSuccess ? Ok(result.Value) : BadRequest(result.Errors);
        }
    }
}
```

## Database Schema

### Tables Design
```sql
-- StockTake Master Table
CREATE TABLE StockTake (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    Code NVARCHAR(40) NOT NULL,
    BranchId INT NOT NULL,
    RetailerId INT NOT NULL,
    Status SMALLINT NOT NULL DEFAULT 0,
    AdjustedBy BIGINT NULL,
    AdjustmentDate DATETIME2 NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy BIGINT NOT NULL,
    ModifiedBy BIGINT NULL,
    ModifiedDate DATETIME2 NULL,
    Description NVARCHAR(500) NULL,
    RecentHistory NVARCHAR(MAX) NULL,
    
    CONSTRAINT UQ_StockTake_Code_RetailerId UNIQUE (Code, RetailerId),
    INDEX IX_StockTake_BranchId_Status (BranchId, Status),
    INDEX IX_StockTake_CreatedDate (CreatedDate DESC)
);

-- StockTakeDetail Table
CREATE TABLE StockTakeDetail (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    StockTakeId BIGINT NOT NULL,
    ProductId BIGINT NOT NULL,
    SystemCount FLOAT NOT NULL DEFAULT 0,
    ActualCount FLOAT NOT NULL DEFAULT 0,
    AdjustmentValue AS (ActualCount - SystemCount) PERSISTED,
    ReasonOfDiff NVARCHAR(500) NULL,
    SerialNumbers NVARCHAR(MAX) NULL,
    SystemSerialNumbers NVARCHAR(MAX) NULL,
    IsDraft BIT NULL,
    Cost DECIMAL(18,2) NULL,
    OrderByNumber INT NULL,
    
    CONSTRAINT FK_StockTakeDetail_StockTake FOREIGN KEY (StockTakeId) REFERENCES StockTake(Id) ON DELETE CASCADE,
    INDEX IX_StockTakeDetail_StockTakeId (StockTakeId),
    INDEX IX_StockTakeDetail_ProductId (ProductId)
);
```

## Testing Strategy

### Unit Tests Structure
```
Tests/
├── Domain/
│   ├── Entities/
│   │   ├── StockTakeTests.cs
│   │   └── StockTakeDetailTests.cs
│   └── ValueObjects/
│       └── StockTakeStatusTests.cs
├── Application/
│   └── UseCases/
│       ├── CreateStockTakeUseCaseTests.cs
│       ├── ApproveStockTakeUseCaseTests.cs
│       ├── CancelStockTakeUseCaseTests.cs
│       └── GetStockTakeUseCaseTests.cs
└── Infrastructure/
    ├── Repositories/
    │   └── StockTakeRepositoryTests.cs
    └── Services/
        └── InventoryServiceTests.cs
```

### Test Coverage Requirements
- Domain entities: 100% coverage
- Use cases: 95% coverage minimum
- Repositories: 90% coverage minimum
- Controllers: 85% coverage minimum

### Integration Tests
- Database operations
- API endpoints
- External service integration
- Multi-tenant scenarios

## Integration Points

### Internal Integrations
1. **Product Module**: Product validation and information retrieval
2. **Inventory Module**: System count calculation and adjustment creation
3. **Branch Module**: Branch validation and close book date checks
4. **User Module**: User permissions and audit trail

### External Integrations
1. **Audit Trail**: Complete action logging
2. **Notifications**: Stock take approval/cancellation events
3. **Reporting**: Stock take analytics and variance reports

## Development Checklist

### Phase 1: Foundation
- [x] ✅ Create StockTake domain entity (EXISTS in Inventory module)
- [x] ✅ Create StockTakeDetail domain entity (EXISTS in Inventory module)  
- [x] ✅ Create basic IStockTakeRepository interface (EXISTS in Inventory module)
- [x] ✅ Create IStockTakeDomainService interface (EXISTS in Inventory module)
- [ ] 🔄 **NEEDS ENHANCEMENT:** Add missing properties to entities (SerialNumbers, ReasonOfDiff, etc.)
- [ ] 🔄 **NEEDS ENHANCEMENT:** Add navigation properties between StockTake and StockTakeDetail
- [ ] 🔄 **NEEDS ENHANCEMENT:** Enhance IStockTakeRepository with required methods
- [ ] ❌ Create IStockTakeDetailRepository interface
- [ ] ❌ Implement IStockTakeDomainService with business logic
- [ ] ❌ Create basic DTO models
- [ ] ❌ Set up database schema
- [ ] ❌ Create domain events
- [ ] ❌ Create value objects (StockTakeStatus)

### Phase 2: Core Use Cases
- [ ] Implement CreateStockTakeUseCase with all components
- [ ] Implement UpdateStockTakeUseCase with all components
- [ ] Implement GetStockTakeUseCase with all components
- [ ] Implement ListStockTakesUseCase with all components
- [ ] Configure dependency injection
- [ ] Set up mapping profiles

### Phase 3: Advanced Operations
- [ ] Implement ApproveStockTakeUseCase with inventory integration
- [ ] Implement CancelStockTakeUseCase with reversal logic
- [ ] Implement DeleteStockTakeUseCase with validation
- [ ] Add serial number handling
- [ ] Implement audit trail integration

### Phase 4: Bulk Operations
- [ ] Implement CloneStockTakeUseCase
- [ ] Implement MergeStockTakesUseCase
- [ ] Implement GetProductsByCategoryUseCase
- [ ] Add performance optimizations
- [ ] Implement advanced validation

### Phase 5: Integration & Testing
- [ ] Create API controller with all endpoints
- [ ] Write comprehensive unit tests
- [ ] Write integration tests
- [ ] Perform security testing
- [ ] Complete documentation
- [ ] Performance testing and optimization

### Immediate Next Steps (Based on Current Implementation)

#### 1. Entity Enhancements Required
```csharp
// Add to StockTake entity:
- Navigation property: public virtual ICollection<StockTakeDetail> Details { get; set; }
- Missing properties: SerialNumbers tracking
- Domain events: StockTakeCreated, StockTakeApproved, etc.
- Business methods: AddDetail(), RemoveDetail(), Approve(), Cancel()

// Add to StockTakeDetail entity:
- Navigation property: public virtual StockTake StockTake { get; set; }
- Missing properties: ReasonOfDiff, SerialNumbers, SystemSerialNumbers
- Business methods: UpdateCounts(), ValidateSerialNumbers()
```

#### 2. Repository Interface Enhancements
```csharp
// IStockTakeRepository needs additional methods for:
- GetByIdWithDetailsAsync()
- IsCodeUniqueAsync()
- GetByBranchAsync()
- Search and filtering methods

// Create IStockTakeDetailRepository for:
- GetByStockTakeIdAsync()
- Bulk operations
- Serial number queries
```

#### 3. Domain Service Implementation
```csharp
// IStockTakeDomainService needs implementation for:
- System count calculations
- Serial number validation
- Inventory adjustment logic
- Business rule validation
```

### Quality Gates
- [x] ✅ Basic domain entities created
- [ ] 🔄 **IN PROGRESS:** All validation scenarios covered
- [ ] ❌ Error handling implemented and tested
- [ ] ❌ Permissions validated
- [ ] ❌ Multi-tenant isolation verified
- [ ] ❌ Performance benchmarks met
- [ ] ❌ Security vulnerabilities addressed
- [ ] ❌ Code review completed
- [ ] ❌ Documentation updated

---

*This implementation plan provides a comprehensive roadmap for developing the Stock Take functionality. Each phase builds upon the previous one, ensuring a solid foundation and systematic implementation approach.* 