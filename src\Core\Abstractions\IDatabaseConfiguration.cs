namespace KvFnB.Core.Abstractions
{
    /// <summary>
    /// Database configuration interface for the application
    /// </summary>
    public interface IDatabaseConfiguration
    {
        /// <summary>
        /// Command timeout in seconds for database operations
        /// </summary>
        int CommandTimeout { get; }
        
        /// <summary>
        /// Command timeout in seconds for delete data operations
        /// </summary>
        int CommandDeleteDataTimeout { get; }
    }

    /// <summary>
    /// Default implementation of database configuration
    /// </summary>
    public class DatabaseConfiguration : IDatabaseConfiguration
    {
        /// <summary>
        /// Command timeout in seconds for database operations (default: 30 seconds)
        /// </summary>
        public int CommandTimeout { get; set; } = 30;
        
        /// <summary>
        /// Command timeout in seconds for delete data operations (default: 300 seconds / 5 minutes)
        /// </summary>
        public int CommandDeleteDataTimeout { get; set; } = 300;
    }
} 