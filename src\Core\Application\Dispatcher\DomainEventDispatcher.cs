using System.Reflection;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Domain;

namespace KvFnB.Core.Application.Dispatcher
{
    /// <summary>
    /// Implementation of IDomainEventDispatcher that uses dependency injection to resolve and dispatch domain events to handlers
    /// </summary>
    public class DomainEventDispatcher : IDomainEventDispatcher
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="DomainEventDispatcher"/> class
        /// </summary>
        /// <param name="serviceProvider">The service provider for resolving handlers</param>
        /// <param name="logger">The logger instance</param>
        public DomainEventDispatcher(
            IServiceProvider serviceProvider,
            ILogger logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Dispatches a domain event to all registered handlers
        /// </summary>
        /// <param name="domainEvent">The domain event to dispatch</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task DispatchAsync(DomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            if (domainEvent == null)
            {
                _logger.Warning("Attempted to dispatch null domain event");
                return;
            }

            var eventType = domainEvent.GetType();
            _logger.Information($"Dispatching domain event of type {eventType.Name}");

            try
            {
                await DispatchToHandlersAsync(domainEvent, eventType, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Error($"Error occurred while dispatching domain event of type {eventType.Name}", ex);
                throw;
            }
        }

        /// <summary>
        /// Dispatches all domain events from an aggregate root to their respective handlers
        /// </summary>
        /// <param name="aggregateRoot">The aggregate root containing domain events</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task DispatchEventsAsync(IAggregateRoot aggregateRoot, CancellationToken cancellationToken = default)
        {
            if (aggregateRoot?.DomainEvents == null || aggregateRoot.DomainEvents.Count == 0)
            {
                _logger.Debug("No domain events to dispatch for aggregate root");
                return;
            }

            var events = aggregateRoot.DomainEvents.ToList();
            _logger.Information($"Dispatching {events.Count} domain events from aggregate root");

            var dispatchTasks = events.Select(async domainEvent =>
            {
                try
                {
                    await DispatchAsync(domainEvent, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.Error($"Failed to dispatch domain event of type {domainEvent.GetType().Name}", ex);
                    throw;
                }
            });

            await Task.WhenAll(dispatchTasks);
            
            // Clear events after successful dispatch
            aggregateRoot.ClearDomainEvents();
            _logger.Information("Successfully dispatched all domain events and cleared aggregate root events");
        }

        /// <summary>
        /// Dispatches a domain event to its registered handlers
        /// </summary>
        /// <param name="domainEvent">The domain event to dispatch</param>
        /// <param name="eventType">The type of the domain event</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task DispatchToHandlersAsync(DomainEvent domainEvent, Type eventType, CancellationToken cancellationToken)
        {
            var handlerType = typeof(IDomainEventHandler<>).MakeGenericType(eventType);
            var handlers = _serviceProvider.GetServices(handlerType);

            if (handlers.Count == 0)
            {
                _logger.Debug($"No handlers found for domain event type {eventType.Name}");
                return;
            }

            var handleTasks = handlers.Select(async handler =>
            {
                try
                {
                    var handleMethod = GetHandleMethod(handler, eventType);
                    var result = handleMethod.Invoke(handler, [domainEvent, cancellationToken]);
                    var task = result != null ? (Task)result : Task.CompletedTask;
                    await task;
                    
                    _logger.Debug($"Successfully handled domain event {eventType.Name} with handler {handler.GetType().Name}");
                }
                catch (Exception ex)
                {
                    _logger.Error($"Handler {handler.GetType().Name} failed to handle domain event {eventType.Name}", ex);
                    throw;
                }
            });

            await Task.WhenAll(handleTasks);
        }

        /// <summary>
        /// Gets the Handle method from the handler using reflection
        /// </summary>
        /// <param name="handler">The handler instance</param>
        /// <param name="eventType">The type of the domain event</param>
        /// <returns>The Handle method info</returns>
        private static MethodInfo GetHandleMethod(object handler, Type eventType)
        {
            var handlerType = handler.GetType();
            var handleMethod = handlerType.GetMethod(nameof(IDomainEventHandler<DomainEvent>.Handle), BindingFlags.Public | BindingFlags.Instance);
            
            if (handleMethod == null)
            {
                throw new InvalidOperationException($"Handler {handlerType.Name} does not implement Handle method");
            }

            return handleMethod;
        }
    }
} 