namespace KvFnB.Core.Application.Dispatcher
{
    /// <summary>
    /// Extension methods for IServiceProvider to avoid Microsoft package dependencies
    /// </summary>
    public static class ServiceProviderExtensions
    {
        /// <summary>
        /// Gets all services of the specified type from the service provider
        /// </summary>
        /// <param name="serviceProvider">The service provider</param>
        /// <param name="serviceType">The type of service to retrieve</param>
        /// <returns>A list of services of the specified type</returns>
        public static IList<object> GetServices(this IServiceProvider serviceProvider, Type serviceType)
        {
            if (serviceProvider == null)
                throw new ArgumentNullException(nameof(serviceProvider));

            if (serviceType == null)
                throw new ArgumentNullException(nameof(serviceType));

            var services = new List<object>();

            // Try to get a single service first
            var service = serviceProvider.GetService(serviceType);
            if (service != null)
            {
                services.Add(service);
            }

            // Try to get enumerable of services
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(serviceType);
            var enumerableService = serviceProvider.GetService(enumerableType);

            if (enumerableService != null && enumerableService is System.Collections.IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    if (item != null && !services.Contains(item))
                    {
                        services.Add(item);
                    }
                }
            }

            return services;
        }

        /// <summary>
        /// Gets all services of the specified type from the service provider
        /// </summary>
        /// <typeparam name="T">The type of service to retrieve</typeparam>
        /// <param name="serviceProvider">The service provider</param>
        /// <returns>A list of services of the specified type</returns>
        public static IList<T> GetServices<T>(this IServiceProvider serviceProvider)
        {
            var services = GetServices(serviceProvider, typeof(T));
            return services.Cast<T>().ToList();
        }
    }
}