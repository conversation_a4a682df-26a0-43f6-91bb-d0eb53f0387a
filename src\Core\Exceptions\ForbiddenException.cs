﻿using System.Runtime.Serialization;

namespace KvFnB.Core.Exceptions
{
    [Serializable]
    public class ForbiddenException : Exception
    {
        public ForbiddenException() { }

        public ForbiddenException(string message) : base(message) { }

        public ForbiddenException(string message, Exception innerException) : base(message, innerException) { }

        [Obsolete("This constructor is obsolete. Use other constructors. Formatter-based serialization is no longer supported.")]
        protected ForbiddenException(SerializationInfo info, StreamingContext context) : base(info, context) { }
    }
}