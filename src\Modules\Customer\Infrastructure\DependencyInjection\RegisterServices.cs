using Microsoft.Extensions.DependencyInjection;
using KvFnB.Modules.Customer.Application.Abstractions;
using KvFnB.Modules.Customer.Infrastructure.Configuration;
using KvFnB.Core.Abstractions;
using KvFnB.Modules.Customer.Infrastructure.Services;
using Microsoft.Extensions.Configuration;

namespace KvFnB.Modules.Customer.Infrastructure.DependencyInjection
{
    public static class RegisterServices
    {

        public static IServiceCollection AddKafkaCommandDispatcherProducer(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IKafkaCommandDispatcherProducerConfiguration>(provider =>
            {
                var kafkaConfiguration = new KafkaCommandDispatcherProducerConfiguration();
                configuration.GetSection("KafkaCommandDispatcherProducer").Bind(kafkaConfiguration);
                return kafkaConfiguration;
            });

            services.AddSingleton<IKafkaCommandDispatcherProducer>(provider =>
            {
                var kafkaProducerConfiguration = provider.GetRequiredService<IKafkaCommandDispatcherProducerConfiguration>();
                var logger = provider.GetRequiredService<ILogger>();
                return new KafkaCommandDispatcherProducer(kafkaProducerConfiguration, logger);
            });

            return services;
        }
    }
}
