using KvFnB.Core.Abstractions;
using KvFnB.Modules.Customer.Application.Abstractions;
using KvFnB.Shared.MessageQueueProvider.Kafka;

namespace KvFnB.Modules.Customer.Infrastructure.Services
{
    public class KafkaCommandDispatcherProducer: ManagedProducer, IKafkaCommandDispatcherProducer
    {
        public KafkaCommandDispatcherProducer(IKafkaCommandDispatcherProducerConfiguration configuration, ILogger logger)
            : base(configuration, logger)
        {
        }
    }
}
