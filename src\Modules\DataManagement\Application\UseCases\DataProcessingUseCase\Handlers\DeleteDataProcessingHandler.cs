using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Domain.Models;


namespace KvFnB.Modules.DataManagement.Application.UseCases.DataProcessingUseCase.Handlers
{
    /// <summary>
    /// Handler for processing delete data messages
    /// </summary>
    public class DeleteDataProcessingHandler : IDataProcessingHandler
    {
        private readonly ILogger _logger;
        private readonly IQueryService _queryService;
        private readonly Dictionary<string, Func<DataProcessingMessage, Task<Result<ProcessingResult>>>> _processors;
        private readonly IDatabaseConfiguration _databaseConfiguration;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeleteDataProcessingHandler"/> class
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="queryService">The query service</param>
        /// <param name="databaseConfiguration">The database configuration</param>
        public DeleteDataProcessingHandler(
            ILogger logger,
            IQueryService queryService,
            IDatabaseConfiguration databaseConfiguration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _databaseConfiguration = databaseConfiguration ?? throw new ArgumentNullException(nameof(databaseConfiguration));
            
            _processors = new Dictionary<string, Func<DataProcessingMessage, Task<Result<ProcessingResult>>>>
            {
                { "DeleteInvoice", ProcessDeleteInvoiceAsync },
                { "DeletePayment", ProcessDeletePaymentAsync },
                { "DeletePurchaseOrder", ProcessDeletePurchaseOrderAsync },
                { "DeletePurchaseReturn", ProcessDeletePurchaseReturnAsync },
                { "DeleteReturn", ProcessDeleteReturnAsync },
                { "DeleteTransfer", ProcessDeleteTransferAsync },
                { "DeleteCashFlow", ProcessDeleteCashFlowAsync },
                { "DeleteManufacturing", ProcessDeleteManufacturingAsync },
                { "DeleteDamageItem", ProcessDeleteDamageItemAsync },
                { "DeleteOrder", ProcessDeleteOrderAsync },
                { "DeleteStockTake", ProcessDeleteStockTakeAsync },
                { "DeleteInventoryTracking", ProcessDeleteInventoryTrackingAsync },
                { "DeleteBalanceTracking", ProcessDeleteBalanceTrackingAsync },
                { "DeleteBalanceAdjustment", ProcessDeleteBalanceAdjustmentAsync },
                { "DeleteCostAdjustment", ProcessDeleteCostAdjustmentAsync },
                { "DeletePartnerOrder", ProcessDeletePartnerOrderAsync },
                { "DeletePointTracking", ProcesDeletePointTrackingAsync },
                { "DeletePurchasePayment", ProcessDeletePurchasePaymentAsync },

                { "AdjustmentCustomer", ProcessAdjustmentCustomerAsync },
                { "AdjustmentProduct", ProcessAdjustmentProductAsync },
                { "AdjustmentSupplier", ProcessAdjustmentSupplierAsync },
                { "AdjustmentPartnerDelivery", ProcessAdjustmentPartnerDeliveryAsync }
            };
        }

        /// <inheritdoc/>
        public async Task<Result<ProcessingResult>> ProcessDeleteAsync(DataProcessingMessage message, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(message.Type))
            {
                _logger.Error("Transaction Type cannot be null for delete operation");
                return Result<ProcessingResult>.Failure("Transaction Type is required for delete operation");
            }

            if (_processors.TryGetValue(message.Type, out var processor))
            {
                return await processor(message);
            }
            
            _logger.Error($"Unsupported transaction Type: {message.Type}");
            return Result<ProcessingResult>.Failure($"Unsupported transaction Type: {message.Type}");
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteInvoiceAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, message.FilterCondition.IgnoreEInvoice, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteInvoiceData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Invoice deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Invoice deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeletePaymentAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeletePaymentData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Payment deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Payment deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeletePurchaseOrderAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeletePurchaseOrderData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed PurchaseOrder deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing PurchaseOrder deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeletePurchaseReturnAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeletePurchaseReturnData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed PurchaseReturn deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing PurchaseReturn deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteReturnAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteReturnData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Return deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Return deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteTransferAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteTransferData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Transfer deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Transfer deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteCashFlowAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteCashFlowData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed CashFlow deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing CashFlow deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteManufacturingAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteManufacturingData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Manufacturing deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Manufacturing deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteDamageItemAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteDamageItemData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed DamageItem deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing DamageItem deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteOrderAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteOrderData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Order deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Order deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }


        private async Task<Result<ProcessingResult>> ProcessDeleteStockTakeAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteStockTakeData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed StockTake deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing StockTake deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteInventoryTrackingAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteInventoryTrackingData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed InventoryTracking deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing InventoryTracking deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteBalanceTrackingAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteBalanceTrackingData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed BalanceTracking deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing BalanceTracking deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteBalanceAdjustmentAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteBalanceAdjustmentData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed BalanceAdjustment deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing BalanceAdjustment deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeleteCostAdjustmentAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeleteCostAdjustmentData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);               
                
                _logger.Information($"Successfully processed CostAdjustment deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing CostAdjustment deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeletePartnerOrderAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeletePartnerOrderData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed PartnerOrder deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing PartnerOrder deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcesDeletePointTrackingAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeletePointTrackingData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);

                _logger.Information($"Successfully processed PointTracking deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing PointTracking deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessDeletePurchasePaymentAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.BranchId, message.RequestId, message.FilterCondition.FromDate, message.FilterCondition.ToDate, BatchSize = 1000 };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_DeletePurchasePaymentData", parameters, _databaseConfiguration.CommandDeleteDataTimeout);

                _logger.Information($"Successfully processed PurchasePayment deletion for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing PurchasePayment deletion for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessAdjustmentCustomerAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.RequestId, message.BranchId };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_GeneratePostDeleteAdjustmentForCustomer", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Customer Adjustment for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Customer Adjustment for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessAdjustmentProductAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.RequestId, message.BranchId };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_GeneratePostDeleteAdjustmentForProduct", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Product Adjustment for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Product Adjustment for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessAdjustmentPartnerDeliveryAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.RequestId, message.BranchId };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_GeneratePostDeleteAdjustmentForPartnerDelivery", parameters, _databaseConfiguration.CommandDeleteDataTimeout);

                _logger.Information($"Successfully processed PartnerDelivery Adjustment for RetailerId: {message.RetailerId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing PartnerDelivery Adjustment for RetailerId: {message.RetailerId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }

        private async Task<Result<ProcessingResult>> ProcessAdjustmentSupplierAsync(DataProcessingMessage message)
        {
            try
            {
                var parameters = new { message.RetailerId, message.RequestId, message.BranchId };
                await _queryService.QueryStoredProcedureAsync<int>("pr_DataManagement_GeneratePostDeleteAdjustmentForSupplier", parameters, _databaseConfiguration.CommandDeleteDataTimeout);
                
                _logger.Information($"Successfully processed Supplier Adjustment for RetailerId: {message.RetailerId}-{message.BranchId}");
                return Result<ProcessingResult>.Success(new ProcessingResult { Status = "Success" });
            }
            catch (Exception ex)
            {
                _logger.Error($"Error processing Supplier Adjustment for RetailerId: {message.RetailerId}-{message.BranchId}", ex);
                return Result<ProcessingResult>.Failure("Internal server error");
            }
        }
    }
} 