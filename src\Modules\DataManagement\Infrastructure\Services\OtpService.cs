using KiotViet.OTP.Abstractions;
using KiotViet.OTP.Configurations;
using KiotViet.OTP.Exceptions;
using KiotViet.OTP.Helpers;
using KiotViet.OTP.Models;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Modules.DataManagement.Domain.Services;
using KvFnB.Modules.DataManagement.Infrastructure.Models;
using KvFnB.Modules.DataManagement.Infrastructure.Services.Otp;
using KvFnB.Shared.MultiTenancy;
using Newtonsoft.Json;

namespace KvFnB.Modules.DataManagement.Infrastructure.Services
{
    public class OtpService : IOtpService
    {
        private readonly ILogger _logger;
        private readonly IKvOTPGenerator _otpGenerator;
        private readonly IKvOTPSender _otpSender;
        private readonly IOtpRedisClients _redisClientsManager;
        private readonly OtpConfiguration _otpConfig;
        private readonly IAuthUser _authUser;
        private readonly TenantInfoProvider _tenantInfoProvider;
        private readonly IKvOTPVerificator _kvOTPVerificator;

        public OtpService(
            ILogger logger,
            IKvOTPGenerator otpGenerator,
            IKvOTPSender otpSender,
            IOtpRedisClients redisClientsManager,
            IKvOTPVerificator kvOTPVerificator,
            OtpConfiguration otpConfig,
            IAuthUser authUser,
            TenantInfoProvider tenantInfoProvider)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _otpGenerator = otpGenerator ?? throw new ArgumentNullException(nameof(otpGenerator));
            _otpSender = otpSender ?? throw new ArgumentNullException(nameof(otpSender));
            _redisClientsManager = redisClientsManager ?? throw new ArgumentNullException(nameof(redisClientsManager));
            _otpConfig = otpConfig ?? throw new ArgumentNullException(nameof(otpConfig));
            _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
            _tenantInfoProvider = tenantInfoProvider ?? throw new ArgumentNullException(nameof(tenantInfoProvider));
            _kvOTPVerificator = kvOTPVerificator ?? throw new ArgumentNullException(nameof(kvOTPVerificator));
        }

        public async Task<bool> GenerateAndSendOtpAsync(
            string phoneNumber,
            string fingerPrintKey)
        {
            try
            {
                if (!ValidateLimitSmsRequest(_authUser.TenantId))
                {
                    _logger.Warning($"SMS limit exceeded for retailer {_authUser.TenantId}");
                    return false;
                }
                var tenantInfo = await _tenantInfoProvider.GetTenantInfoAsync();
                var otpConfig = CreateOtpConfiguration(fingerPrintKey, tenantInfo.Code);
                var otp = await _otpGenerator.GenerateOTPAsync(otpConfig);
                await _otpSender.SendAsync(otp, phoneNumber, new SmsRequestContext
                {
                    FromDevice = (int)TfaDevice.Web,
                    RequestId = Guid.NewGuid().ToString(),
                    SessionId = _authUser.SessionId,
                    RetailerId = _authUser.TenantId,
                    RetailerCode = tenantInfo.Code,
                    FingerPrintKey = fingerPrintKey,
                    UserName = _authUser.UserName,
                    UserId = _authUser.Id,
                    FunctionId = (int)SmsAction.Login
                });

                return true;
            }
            catch(Exception ex)
            {
                _logger.Error("Error generating and sending OTP");
                return false;
            }
        }

        private bool ValidateLimitSmsRequest(long retailerId)
        {
            if (retailerId <= 0)
            {
                _logger.Warning("RetailerId in SmsMessageRequest is null or zero");
                return true;
            }

            var toDay = DateTime.Now.ToString("ddMMyyyy");
            var cacheKey = $"{_otpConfig.SmsCountKeyPrefix}{retailerId}_{toDay}";
            
            using (var redisClient = _redisClientsManager.GetClient())
            {
                var currentSmsAmount = redisClient.Get<int?>(cacheKey);

                if (currentSmsAmount != null && currentSmsAmount >= _otpConfig.MaximumSmsRequestFromRetailerPerDay)
                {
                    return false;
                }
            }

            return true;
        }

        private KvOTPConfiguration CreateOtpConfiguration(string fingerPrintKey, string tenantCode)
        {
            if (string.IsNullOrEmpty(fingerPrintKey))
                throw new KiotVietOTPException("Thiết bị không tồn tại. Vui lòng tải lại trang Đăng Nhập.");

            var userIdentify = new { TenantCode = tenantCode, _authUser.UserName, fingerPrintKey };
            var identify = HMACHelpers.HMACSigningAsHex(JsonConvert.SerializeObject(userIdentify), _otpConfig.OtpSecret, Algorithm.SHA256);
            var otpConfig = KvOTPConfiguration.CreateSMSOTPConfiguration(_otpConfig.OtpIssuer, identify);
            return otpConfig;
        }

        public async Task<bool> VerifyOtpAsync(string otp, string fingerPrintKey, CancellationToken cancellationToken = default)
        {
            var tenantInfo = await _tenantInfoProvider.GetTenantInfoAsync();
            var otpConfig = CreateOtpConfiguration(fingerPrintKey, tenantInfo.Code);
            var otpValidateResult = await _kvOTPVerificator.VerifyAsync(otp, otpConfig);

            return otpValidateResult.IsSuccess;
        }
    }
} 