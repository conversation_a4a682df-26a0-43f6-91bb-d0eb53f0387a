using KvFnB.Core.Domain;

namespace KvFnB.Modules.Inventory.Domain.Entities
{
    public class StockTakeDetail : Entity<long>, ICreatedAt
    {
        public long StockTakeId { get; set; }
        public long ProductId { get; set; }
        public double SystemCount { get; set; }
        public double ActualCount { get; set; }
        public double AdjustmentValue { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public bool? IsDraft { get; set; }
        public decimal? Cost { get; set; }
        public int? OrderByNumber { get; set; }
        public DateTime CreatedAt { get; set; }
        
        private StockTakeDetail() { }

        public StockTakeDetail(long stockTakeId, long productId, double actualCount, double systemCount)
        {
            StockTakeId = stockTakeId;
            ProductId = productId;
            ActualCount = actualCount;
            SystemCount = systemCount;
        }
        
        public static StockTakeDetail Create(long stockTakeId, long productId, double actualCount, double systemCount)
        {
            return new StockTakeDetail(stockTakeId, productId, actualCount, systemCount);
        }
    }
}