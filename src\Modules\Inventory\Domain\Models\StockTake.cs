using KvFnB.Core.Domain;

namespace KvFnB.Modules.Inventory.Domain.Models
{
    public class StockTake : AggregateRoot<long>, IAuditableEntity, ICode
    {
        public string Code { get; set; }
        
        public int BranchId { get; set; }
        
        public int RetailerId { get; set; }
        
        public int Status { get; set; }

        public string RecentHistory { get; set; }
        
        public long AdjustedBy { get; set; }
        
        public DateTime AdjustmentDate { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public long CreatedBy { get; set; }
        
        public string? Description { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public long? ModifiedBy { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        public StockTake()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        {
        }

        public static StockTake Create(
            string code,
            int branchId,
            int retailerId,
            int status,
            long adjustedBy,
            DateTime adjustmentDate)
        {
            return new StockTake
            {
                Code = code,
                BranchId = branchId,
                RetailerId = retailerId,
                Status = status,
                AdjustedBy = adjustedBy,
                AdjustmentDate = adjustmentDate
            };
        }

        public void UpdateCode(string code)
        {
            Code = code;
        }
    }
}