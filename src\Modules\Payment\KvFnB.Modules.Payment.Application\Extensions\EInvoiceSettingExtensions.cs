using System.Text.Json;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;
using KvFnB.Modules.Payment.Domain.Models;

namespace KvFnB.Modules.Payment.Application.Extensions;

/// <summary>
/// Extension methods for converting between EInvoice domain entities and DTOs
/// </summary>
public static class EInvoiceSettingExtensions
{
    /// <summary>
    /// Converts an EInvoice setting domain entity to a DTO
    /// </summary>
    /// <param name="setting">The EInvoice setting domain entity</param>
    /// <returns>The corresponding EInvoice setting DTO</returns>
    public static EInvoiceSettingDto ToDto(this EInvoiceSetting setting)
    {
        ArgumentNullException.ThrowIfNull(setting);

        EInvoiceConfigDto? configDto = null;
        if (!string.IsNullOrWhiteSpace(setting.EInvoiceConfig))
        {
            try
            {
                configDto = JsonSerializer.Deserialize<EInvoiceConfigDto>(
                    setting.EInvoiceConfig,
                    GetJsonSerializerOptions());
            }
            catch (JsonException)
            {
                // Log error and return null config if deserialization fails
                // This prevents the entire operation from failing due to corrupted JSON
                configDto = null;
            }
        }

        return new EInvoiceSettingDto(
            Id: setting.Id,
            TenantId: setting.TenantId,
            BranchId: setting.BranchId,
            Status: setting.Status.Id,
            EInvoiceConfig: configDto,
            PartnerType: setting.PartnerType.Id,
            CreatedBy: setting.CreatedBy,
            CreatedAt: setting.CreatedAt,
            ModifiedBy: setting.ModifiedBy,
            ModifiedAt: setting.ModifiedAt
        );
    }

    /// <summary>
    /// Converts a collection of EInvoice setting domain entities to DTOs
    /// </summary>
    /// <param name="settings">The collection of EInvoice setting domain entities</param>
    /// <returns>A list of corresponding EInvoice setting DTOs</returns>
    public static List<EInvoiceSettingDto> ToDto(this IEnumerable<EInvoiceSetting> settings)
    {
        ArgumentNullException.ThrowIfNull(settings);

        return [.. settings.Select(ToDto)];
    }

    /// <summary>
    /// Converts an EInvoice configuration DTO to JSON string
    /// </summary>
    /// <param name="configDto">The EInvoice configuration DTO</param>
    /// <returns>JSON string representation of the configuration, or null if configDto is null</returns>
    public static string? ToJsonString(this EInvoiceConfigDto? configDto)
    {
        if (configDto == null)
            return null;

        try
        {
            return JsonSerializer.Serialize(configDto, GetJsonSerializerOptions());
        }
        catch (JsonException)
        {
            // Return null if serialization fails
            return null;
        }
    }

    /// <summary>
    /// Converts a JSON string to EInvoice configuration DTO
    /// </summary>
    /// <param name="jsonString">The JSON string representation</param>
    /// <returns>The EInvoice configuration DTO, or null if deserialization fails</returns>
    public static EInvoiceConfigDto? FromJsonString(string? jsonString)
    {
        if (string.IsNullOrWhiteSpace(jsonString))
            return null;

        try
        {
            return JsonSerializer.Deserialize<EInvoiceConfigDto>(jsonString, GetJsonSerializerOptions());
        }
        catch (JsonException)
        {
            // Return null if deserialization fails
            return null;
        }
    }

    /// <summary>
    /// Gets the JSON serializer options with consistent configuration
    /// </summary>
    /// <returns>JsonSerializerOptions configured for snake_case property naming</returns>
    private static JsonSerializerOptions GetJsonSerializerOptions()
    {
        return new JsonSerializerOptions
        {
            PropertyNamingPolicy = null,
            WriteIndented = false, // Compact JSON for storage
            PropertyNameCaseInsensitive = false
        };
    }
}
