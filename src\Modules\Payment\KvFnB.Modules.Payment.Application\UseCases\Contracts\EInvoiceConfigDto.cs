using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.Contracts;

/// <summary>
/// Data transfer object representing EInvoice configuration settings
/// </summary>
public record EInvoiceConfigDto(
    /// <summary>
    /// Indicates whether invoices should be automatically pushed from POS system
    /// </summary>
    bool IsPushFromPos,

    /// <summary>
    /// The default template ID used for EInvoice generation. This is required for invoice formatting.
    /// </summary>
    string DefaultTemplateId,

    /// <summary>
    /// The URL endpoint used for searching and retrieving invoice information from the partner system
    /// </summary>
    string UrlSearch,

    /// <summary>
    /// Indicates whether item notes should be included when pushing invoice data to the partner system
    /// </summary>
    bool IsPushInvoiceItemNote,

    /// <summary>
    /// The tax identification code of the business. This is required for tax compliance and invoice validation.
    /// </summary>
    string TaxCode
); 