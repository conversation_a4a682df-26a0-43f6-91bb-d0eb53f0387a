using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.Contracts;

/// <summary>
/// Data transfer object representing an EInvoice setting configuration
/// </summary>
public record EInvoiceSettingDto(
    /// <summary>
    /// The unique identifier of the EInvoice setting
    /// </summary>
    [property: JsonPropertyName("id")] 
    long Id,

    /// <summary>
    /// The tenant/retailer ID that owns this EInvoice setting
    /// </summary>
    [property: JsonPropertyName("tenant_id")] 
    int TenantId,

    /// <summary>
    /// The branch ID where this EInvoice setting is applied
    /// </summary>
    [property: JsonPropertyName("branch_id")] 
    int BranchId,

    /// <summary>
    /// The status ID of the EInvoice setting (0 = InActive, 1 = Active)
    /// </summary>
    [property: JsonPropertyName("status")] 
    int Status,

    /// <summary>
    /// The EInvoice configuration settings. Can be null if no configuration is set.
    /// </summary>
    [property: JsonPropertyName("einvoice_config")] 
    EInvoiceConfigDto? EInvoiceConfig,

    /// <summary>
    /// The partner type ID for EInvoice integration (1=MISA, 2=VIETTEL, 3=VNPT, 4=KIOTVIET)
    /// </summary>
    [property: JsonPropertyName("partner_type")] 
    int PartnerType,


    /// <summary>
    /// The ID of the user who created this EInvoice setting
    /// </summary>
    [property: JsonPropertyName("created_by")] 
    long CreatedBy,

    /// <summary>
    /// The date and time when this EInvoice setting was created
    /// </summary>
    [property: JsonPropertyName("created_at")] 
    DateTime CreatedAt,

    /// <summary>
    /// The ID of the user who last modified this EInvoice setting. Can be null if never modified.
    /// </summary>
    [property: JsonPropertyName("modified_by")] 
    long? ModifiedBy,

    /// <summary>
    /// The date and time when this EInvoice setting was last modified. Can be null if never modified.
    /// </summary>
    [property: JsonPropertyName("modified_at")] 
    DateTime? ModifiedAt
); 