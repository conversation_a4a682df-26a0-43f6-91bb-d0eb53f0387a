using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.Contracts
{
    /// <summary>
    /// Represents a PayslipPayment data transfer object
    /// </summary>
    public class PayslipPaymentDto
    {
        /// <summary>
        /// The unique identifier of the payslip payment
        /// </summary>
        [JsonPropertyName("id"), Description("The unique identifier of the payslip payment")]
        public long Id { get; set; }

        /// <summary>
        /// Payment reference code
        /// </summary>
        [JsonPropertyName("code"), Description("Payment reference code")]
        public string? Code { get; set; }

        /// <summary>
        /// Final payment amount after calculations
        /// </summary>
        [JsonPropertyName("amount"), Description("Final payment amount after calculations")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Original payment amount before adjustments
        /// </summary>
        [JsonPropertyName("amount_original"), Description("Original payment amount before adjustments")]
        public decimal AmountOriginal { get; set; }

        /// <summary>
        /// Whether this payment is an allocation
        /// </summary>
        [JsonPropertyName("is_allocation"), Description("Whether this payment is an allocation")]
        public bool IsAllocation { get; set; }

        /// <summary>
        /// Payment method (BANK, CASH, etc.)
        /// </summary>
        [JsonPropertyName("method"), Description("Payment method (BANK, CASH, etc.)")]
        public string Method { get; set; } = string.Empty;

        /// <summary>
        /// Associated branch identifier
        /// </summary>
        [JsonPropertyName("branch_id"), Description("Associated branch identifier")]
        public int BranchId { get; set; }

        /// <summary>
        /// Associated payslip identifier
        /// </summary>
        [JsonPropertyName("payslip_id"), Description("Associated payslip identifier")]
        public long PayslipId { get; set; }

        /// <summary>
        /// Payment description or notes
        /// </summary>
        [JsonPropertyName("description"), Description("Payment description or notes")]
        public string? Description { get; set; }

        /// <summary>
        /// Employee receiving the payment
        /// </summary>
        [JsonPropertyName("employee_id"), Description("Employee receiving the payment")]
        public long EmployeeId { get; set; }

        /// <summary>
        /// Transaction date
        /// </summary>
        [JsonPropertyName("trans_date"), Description("Transaction date")]
        public DateTime TransDate { get; set; }

        /// <summary>
        /// User who processed the payment
        /// </summary>
        [JsonPropertyName("user_id"), Description("User who processed the payment")]
        public long? UserId { get; set; }

        /// <summary>
        /// Payment status
        /// </summary>
        [JsonPropertyName("status"), Description("Payment status")]
        public byte Status { get; set; }

        /// <summary>
        /// Associated account identifier
        /// </summary>
        [JsonPropertyName("account_id"), Description("Associated account identifier")]
        public int? AccountId { get; set; }

        /// <summary>
        /// When the record was created
        /// </summary>
        [JsonPropertyName("created_date"), Description("When the record was created")]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Who created the record
        /// </summary>
        [JsonPropertyName("created_by"), Description("Who created the record")]
        public long CreatedBy { get; set; }

        /// <summary>
        /// Who last modified the record
        /// </summary>
        [JsonPropertyName("modified_by"), Description("Who last modified the record")]
        public long? ModifiedBy { get; set; }

        /// <summary>
        /// When the record was last modified
        /// </summary>
        [JsonPropertyName("modified_date"), Description("When the record was last modified")]
        public DateTime? ModifiedDate { get; set; }
    }
} 