using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.CreateEInvoiceSettingUseCase;

/// <summary>
/// Request model for creating a new EInvoice setting
/// </summary>
public record CreateEInvoiceSettingRequest
{
    /// <summary>
    /// The branch ID where this EInvoice setting will be applied
    /// </summary>
    [Required]
    [JsonPropertyName("branch_id")]
    [Description("The ID of the branch for this EInvoice setting")]
    public int BranchId { get; set; }

    /// <summary>
    /// The partner type for EInvoice integration (1=MISA, 2=VIETTEL, 3=VNPT, 4=KIOTVIET)
    /// </summary>
    [Required]
    [Range(1, 4)]
    [JsonPropertyName("partner_type")]
    [Description("The partner type ID (1=MISA, 2=VIETTEL, 3=VNPT, 4=KIOTVIET)")]
    public int PartnerType { get; set; }

    /// <summary>
    /// The EInvoice configuration settings (optional at creation)
    /// </summary>
    [JsonPropertyName("einvoice_config")]
    [Description("The EInvoice configuration settings (optional)")]
    public EInvoiceConfigDto? EInvoiceConfig { get; set; }
} 