﻿using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Payment.Domain.Models;
using KvFnB.Modules.Payment.Domain.Enums;
using KvFnB.Modules.Payment.Domain.Repositories;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;
using KvFnB.Modules.Payment.Application.Extensions;
using KvFnB.Modules.Payment.Domain.Services;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.CreateEInvoiceSettingUseCase;

/// <summary>
/// Use case for creating a new EInvoice setting with business validation
/// Includes duplicate validation to ensure only one setting per branch
/// </summary>
public class CreateEInvoiceSettingUseCase : UseCaseBase<CreateEInvoiceSettingRequest, EInvoiceSettingDto>
{
    private readonly IEInvoiceSettingRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IValidator<CreateEInvoiceSettingRequest> _validator;
    private readonly IBranchService _branchService;

    /// <summary>
    /// Initializes a new instance of the CreateEInvoiceSettingUseCase
    /// </summary>
    /// <param name="repository">The EInvoice setting repository</param>
    /// <param name="unitOfWork">The unit of work for transaction management</param>
    /// <param name="validator">The request validator</param>
    public CreateEInvoiceSettingUseCase(
        IEInvoiceSettingRepository repository,
        IUnitOfWork unitOfWork,
        IValidator<CreateEInvoiceSettingRequest> validator,
        IBranchService branchService)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        _branchService = branchService ?? throw new ArgumentNullException(nameof(branchService));
    }

    /// <summary>
    /// Executes the create EInvoice setting use case with duplicate validation
    /// </summary>
    /// <param name="request">The create request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the created EInvoice setting DTO</returns>
    public override async Task<Result<EInvoiceSettingDto>> ExecuteAsync(
        CreateEInvoiceSettingRequest request,
        CancellationToken cancellationToken = default)
    {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<EInvoiceSettingDto>.Failure(validationResult.Errors);
            }
            bool branchesValid = await _branchService.ValidateBranchAsync([request.BranchId], cancellationToken);
            if (!branchesValid)
            {
                return Result<EInvoiceSettingDto>.Failure("Chi nhánh không tồn tại, ngừng hoạt động hoặc đã bị xóa");
            }
        // Check if EInvoice setting already exists for this branch
        var existsForBranch = await _repository.ExistsByBranchAsync(request.BranchId, cancellationToken);
            if (existsForBranch)
            {
                return Result<EInvoiceSettingDto>.Failure(
                    "EInvoice setting already exists for this branch. Only one setting per branch is allowed.");
            }

            // Convert configuration DTO to JSON string
            var configJson = request.EInvoiceConfig?.ToJsonString();

            // Create EInvoice setting using domain factory method
            var setting = EInvoiceSetting.Create(
                request.BranchId,
                EInvoicePartnerType.FromId(request.PartnerType),
                configJson);

            // Add to repository
            setting = await _repository.AddAsync(setting, cancellationToken);

            // Save changes
            await _unitOfWork.CommitAsync(cancellationToken);

            // Map to DTO using extension method
            var dto = setting.ToDto();
            return Result<EInvoiceSettingDto>.Success(dto);
    }
} 