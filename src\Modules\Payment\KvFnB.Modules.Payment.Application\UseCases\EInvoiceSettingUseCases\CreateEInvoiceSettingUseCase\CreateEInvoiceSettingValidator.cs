using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.CreateEInvoiceSettingUseCase;

/// <summary>
/// Validator for CreateEInvoiceSettingRequest
/// </summary>
public class CreateEInvoiceSettingValidator : Validator<CreateEInvoiceSettingRequest>
{
    public CreateEInvoiceSettingValidator()
    {
        RuleFor(x => x.BranchId)
            .GreaterThan(0, "Branch ID is required and must be greater than 0.");
    }
} 