using System.ComponentModel;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.GetAllEInvoiceSettingsUseCase;

/// <summary>
/// Request model for retrieving all EInvoice settings for all branches of the current tenant
/// </summary>
public record GetAllEInvoiceSettingsRequest
{
    // Empty request - tenant context handled by base query, returns all branches
} 