using System.ComponentModel;
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.GetAllEInvoiceSettingsUseCase;

/// <summary>
/// Response model for retrieving all EInvoice settings for all branches of the current tenant
/// </summary>
public class GetAllEInvoiceSettingsResponse
{
    [JsonPropertyName("einvoice_settings"), Description("The list of EInvoice settings for all branches")]
    public List<EInvoiceSettingDto> EInvoiceSettings { get; set; } = new List<EInvoiceSettingDto>();

    [JsonPropertyName("total_count"), Description("The total number of EInvoice settings found")]
    public int TotalCount { get; set; }
} 