using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Modules.Payment.Application.Extensions;
using KvFnB.Modules.Payment.Domain.Repositories;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.GetAllEInvoiceSettingsUseCase;

/// <summary>
/// Use case for retrieving all EInvoice settings for all branches of the current tenant
/// (Tenant context is automatically handled by repository base query)
/// </summary>
public class GetAllEInvoiceSettingsUseCase : UseCaseBase<GetAllEInvoiceSettingsRequest, GetAllEInvoiceSettingsResponse>
{
    private readonly IEInvoiceSettingRepository _repository;

    /// <summary>
    /// Initializes a new instance of the GetAllEInvoiceSettingsUseCase
    /// </summary>
    /// <param name="repository">The EInvoice setting repository with built-in tenant context filtering</param>
    public GetAllEInvoiceSettingsUseCase(IEInvoiceSettingRepository repository)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
    }

    /// <summary>
    /// Executes the get all EInvoice settings use case
    /// </summary>
    /// <param name="request">The request (empty - tenant context handled by repository base query)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing all EInvoice settings for the current tenant's branches</returns>
    public override async Task<Result<GetAllEInvoiceSettingsResponse>> ExecuteAsync(
        GetAllEInvoiceSettingsRequest request, 
        CancellationToken cancellationToken = default)
    {
        // Repository automatically filters by current tenant context through base query
        var settings = await _repository.GetAllAsync(cancellationToken);
        var settingDtos = settings.ToDto();

        var response = new GetAllEInvoiceSettingsResponse
        {
            EInvoiceSettings = settingDtos,
            TotalCount = settingDtos.Count
        };

        return Result<GetAllEInvoiceSettingsResponse>.Success(response);
    }
} 