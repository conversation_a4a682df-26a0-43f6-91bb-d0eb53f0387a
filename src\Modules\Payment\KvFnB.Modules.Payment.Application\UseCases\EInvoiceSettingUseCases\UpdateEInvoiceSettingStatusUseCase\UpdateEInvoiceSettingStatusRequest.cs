using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingStatusUseCase;

/// <summary>
/// Request model for updating the status of an existing EInvoice setting
/// </summary>
public record UpdateEInvoiceSettingStatusRequest
{
    /// <summary>
    /// The unique identifier of the EInvoice setting to update
    /// </summary>
    [Required]
    [JsonPropertyName("id")]
    [Description("The ID of the EInvoice setting to update status")]
    public long Id { get; set; }

    /// <summary>
    /// The new status for the EInvoice setting (0 = InActive, 1 = Active)
    /// </summary>
    [Required]
    [Range(0, 1)]
    [JsonPropertyName("status")]
    [Description("The new status (0 = InActive, 1 = Active)")]
    public int Status { get; set; }
} 