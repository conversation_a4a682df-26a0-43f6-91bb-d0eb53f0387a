using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Payment.Domain.Repositories;
using KvFnB.Modules.Payment.Domain.Enums;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;
using KvFnB.Modules.Payment.Application.Extensions;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingStatusUseCase;

/// <summary>
/// Use case for updating the status of an existing EInvoice setting
/// This is a focused operation for activate/deactivate functionality
/// </summary>
public class UpdateEInvoiceSettingStatusUseCase : UseCaseBase<UpdateEInvoiceSettingStatusRequest, EInvoiceSettingDto>
{
    private readonly IValidator<UpdateEInvoiceSettingStatusRequest> _validator;
    private readonly IEInvoiceSettingRepository _repository;
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the UpdateEInvoiceSettingStatusUseCase
    /// </summary>
    /// <param name="validator">The request validator</param>
    /// <param name="repository">The EInvoice setting repository</param>
    /// <param name="unitOfWork">The unit of work for transaction management</param>
    public UpdateEInvoiceSettingStatusUseCase(
        IValidator<UpdateEInvoiceSettingStatusRequest> validator,
        IEInvoiceSettingRepository repository,
        IUnitOfWork unitOfWork)
    {
        _validator = validator ?? throw new ArgumentNullException(nameof(validator));
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Executes the update EInvoice setting status use case
    /// </summary>
    /// <param name="request">The status update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the updated EInvoice setting DTO</returns>
    public override async Task<Result<EInvoiceSettingDto>> ExecuteAsync(
        UpdateEInvoiceSettingStatusRequest request,
        CancellationToken cancellationToken = default)
    {
            // Validate request
            var validationResult = _validator.Validate(request);
            if (!validationResult.IsValid)
            {
                return Result<EInvoiceSettingDto>.Failure(validationResult.Errors);
            }

            // Get existing setting
            var setting = await _repository.GetAsync(request.Id, cancellationToken);
            if (setting == null)
            {
                return Result<EInvoiceSettingDto>.Failure("EInvoice setting not found.");
            }

            // Update status
            var newStatus = EInvoiceStatus.FromId(request.Status);
            setting.UpdateStatus(newStatus);

            // Update in repository
            await _repository.UpdateAsync(setting, cancellationToken);

            // Save changes
            await _unitOfWork.CommitAsync(cancellationToken);

            // Map to DTO using extension method
            var dto = setting.ToDto();
            return Result<EInvoiceSettingDto>.Success(dto);
    }
} 