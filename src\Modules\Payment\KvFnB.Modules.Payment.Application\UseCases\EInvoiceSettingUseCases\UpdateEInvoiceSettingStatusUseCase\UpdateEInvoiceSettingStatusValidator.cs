using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingStatusUseCase;

/// <summary>
/// Validator for UpdateEInvoiceSettingStatusRequest
/// </summary>
public class UpdateEInvoiceSettingStatusValidator : Validator<UpdateEInvoiceSettingStatusRequest>
{
    public UpdateEInvoiceSettingStatusValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0, "EInvoice setting ID is required and must be greater than 0.");

    }
} 