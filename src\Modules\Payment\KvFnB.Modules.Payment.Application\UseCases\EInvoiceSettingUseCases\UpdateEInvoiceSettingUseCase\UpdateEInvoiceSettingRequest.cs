using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingUseCase;

/// <summary>
/// Request model for updating an existing EInvoice setting
/// </summary>
public record UpdateEInvoiceSettingRequest
{
    /// <summary>
    /// The unique identifier of the EInvoice setting to update
    /// </summary>
    [Required]
    [JsonPropertyName("id")]
    [Description("The ID of the EInvoice setting to update")]
    public long Id { get; set; }

    /// <summary>
    /// The status to set (0 = InActive, 1 = Active). Optional - if not provided, status won't be changed.
    /// </summary>
    [Range(0, 1)]
    [JsonPropertyName("status")]
    [Description("The status of the EInvoice setting (0 = InActive, 1 = Active)")]
    public int? Status { get; set; }

    /// <summary>
    /// The partner type for EInvoice integration (1=MISA, 2=VIETTEL, 3=VNPT, 4=KIOTVIET). Optional - if not provided, partner type won't be changed.
    /// </summary>
    [Range(1, 4)]
    [JsonPropertyName("partner_type")]
    [Description("The partner type ID (1=MISA, 2=VIETTEL, 3=VNPT, 4=KIOTVIET)")]
    public int? PartnerType { get; set; }

    /// <summary>
    /// The EInvoice configuration settings. Optional - if not provided, configuration won't be changed.
    /// </summary>
    [JsonPropertyName("einvoice_config")]
    [Description("The EInvoice configuration settings")]
    public EInvoiceConfigDto? EInvoiceConfig { get; set; }
} 