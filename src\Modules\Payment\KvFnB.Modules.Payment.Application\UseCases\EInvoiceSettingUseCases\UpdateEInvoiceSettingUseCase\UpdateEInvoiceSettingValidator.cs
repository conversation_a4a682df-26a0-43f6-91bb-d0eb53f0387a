using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingUseCase;

/// <summary>
/// Validator for UpdateEInvoiceSettingRequest
/// </summary>
public class UpdateEInvoiceSettingValidator : Validator<UpdateEInvoiceSettingRequest>
{
    public UpdateEInvoiceSettingValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0, "EInvoice setting ID is required and must be greater than 0.");
    }
}