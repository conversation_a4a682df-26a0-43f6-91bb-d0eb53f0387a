using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace KvFnB.Modules.Payment.Application.UseCases.PayslipPaymentUseCases.GetPayslipPaymentByIds
{
    /// <summary>
    /// Represents the request model for the GetPayslipPaymentByIds use case
    /// </summary>
    public record GetPayslipPaymentByIdsRequest
    {
        /// <summary>
        /// List of PayslipPayment IDs to retrieve
        /// </summary>
        [Required]
        [JsonPropertyName("payslip_payment_ids")]
        [Description("List of PayslipPayment IDs to retrieve")]
        public List<long> PayslipPaymentIds { get; init; } = [];
    }
} 