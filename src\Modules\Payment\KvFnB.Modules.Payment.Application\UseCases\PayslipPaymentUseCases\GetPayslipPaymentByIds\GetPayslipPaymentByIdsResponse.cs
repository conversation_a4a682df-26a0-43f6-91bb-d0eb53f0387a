using System.ComponentModel;
using System.Text.Json.Serialization;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.PayslipPaymentUseCases.GetPayslipPaymentByIds
{
    /// <summary>
    /// Represents the response model for the GetPayslipPaymentByIds use case
    /// </summary>
    public record GetPayslipPaymentByIdsResponse
    {
        /// <summary>
        /// List of PayslipPayment records
        /// </summary>
        [JsonPropertyName("data"), Description("List of PayslipPayment records")]
        public List<PayslipPaymentDto> Data { get; init; } = [];

        /// <summary>
        /// Total number of records found
        /// </summary>
        [JsonPropertyName("total"), Description("Total number of records found")]
        public int Total { get; init; }
    }
} 