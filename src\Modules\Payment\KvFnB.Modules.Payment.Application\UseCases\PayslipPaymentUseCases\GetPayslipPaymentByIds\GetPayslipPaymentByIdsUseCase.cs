using Dapper;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;

namespace KvFnB.Modules.Payment.Application.UseCases.PayslipPaymentUseCases.GetPayslipPaymentByIds;

/// <summary>
/// Implements the GetPayslipPaymentByIds use case for querying PayslipPayment data
/// </summary>
public class GetPayslipPaymentByIdsUseCase : UseCaseBase<GetPayslipPaymentByIdsRequest, GetPayslipPaymentByIdsResponse>
{
    private readonly IQueryService _queryService;
    private readonly ITenantProvider _tenantProvider;
    private readonly ILogger _logger;

    public GetPayslipPaymentByIdsUseCase(
        ILogger logger,
        IQueryService queryService,
        ITenantProvider tenantProvider
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
        _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
    }

    public override async Task<Result<GetPayslipPaymentByIdsResponse>> ExecuteAsync(
        GetPayslipPaymentByIdsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get tenant context
            var retailerId = _tenantProvider.GetTenantId() ?? 0;
            
            var logMessage = $"Getting PayslipPayments by IDs for RetailerId: {retailerId}";
            _logger.Information(logMessage);

            // Prepare parameters for Dapper query
            var parameters = new DynamicParameters();
            parameters.Add("retailerId", retailerId);
            parameters.Add("payslipPaymentIds", request.PayslipPaymentIds);

            // SQL query to get PayslipPayments by IDs with tenant filtering
            var query = @"
                SELECT 
                    p.Id,
                    p.Code,
                    p.Amount,
                    p.AmountOriginal,
                    p.IsAllowcation as IsAllocation,
                    p.Method,
                    p.BranchId,
                    p.PayslipId,
                    p.Description,
                    p.EmployeeId,
                    p.TransDate,
                    p.UserId,
                    p.Status,
                    p.AccountId,
                    p.CreatedDate,
                    p.CreatedBy,
                    p.ModifiedBy,
                    p.ModifiedDate
                FROM PayslipPayment p WITH(NOLOCK)
                WHERE p.RetailerId = @retailerId
                    AND p.Id IN @payslipPaymentIds
                ORDER BY p.Id DESC";

            // Execute query using Dapper through IQueryService
            var payslipPayments = await _queryService.QueryPlainTextAsync<dynamic>(query, parameters);

            // Map results to DTOs
            var payslipPaymentDtos = payslipPayments.Select(p => new PayslipPaymentDto
            {
                Id = p.Id,
                Code = p.Code,
                Amount = p.Amount,
                AmountOriginal = p.AmountOriginal,
                IsAllocation = p.IsAllocation,
                Method = p.Method ?? string.Empty,
                BranchId = p.BranchId,
                PayslipId = p.PayslipId,
                Description = p.Description,
                EmployeeId = p.EmployeeId,
                TransDate = p.TransDate,
                UserId = p.UserId,
                Status = p.Status,
                AccountId = p.AccountId,
                CreatedDate = p.CreatedDate,
                CreatedBy = p.CreatedBy,
                ModifiedBy = p.ModifiedBy,
                ModifiedDate = p.ModifiedDate
            }).ToList();

            var resultLogMessage = $"Found {payslipPaymentDtos.Count} PayslipPayments for RetailerId: {retailerId}";
            _logger.Information(resultLogMessage);

            // Create response
            var response = new GetPayslipPaymentByIdsResponse
            {
                Data = payslipPaymentDtos,
                Total = payslipPaymentDtos.Count
            };

            return Result<GetPayslipPaymentByIdsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            var errorMessage = "Error occurred while getting PayslipPayments by IDs";
            _logger.Error(errorMessage, ex);
            
            return Result<GetPayslipPaymentByIdsResponse>.Failure("Failed to retrieve PayslipPayment records");
        }
    }
}