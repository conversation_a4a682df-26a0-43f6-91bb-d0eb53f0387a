using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;

namespace KvFnB.Modules.Payment.Application.UseCases.PayslipPaymentUseCases.GetPayslipPaymentByIds
{
    /// <summary>
    /// Validates the GetPayslipPaymentByIds request
    /// </summary>
    public class GetPayslipPaymentByIdsValidator : Validator<GetPayslipPaymentByIdsRequest>
    {
        public GetPayslipPaymentByIdsValidator()
        {
            // Validate that PayslipPaymentIds is not null or empty
            RuleFor(x => x.PayslipPaymentIds)
                .NotNull("PayslipPayment IDs list cannot be null")
                .Must(ids => ids.Count > 0, "At least one PayslipPayment ID is required")
                .Must(ids => ids.Count <= 100, "Maximum 100 PayslipPayment IDs allowed per request")
                .Must(ids => ids.All(id => id > 0), "All PayslipPayment IDs must be greater than zero");
        }
    }
} 