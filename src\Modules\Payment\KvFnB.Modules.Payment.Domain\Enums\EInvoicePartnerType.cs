using KvFnB.Core.Domain;

namespace KvFnB.Modules.Payment.Domain.Enums;

/// <summary>
/// Represents the different EInvoice partner types supported by the system
/// </summary>
public class EInvoicePartnerType : Enumeration
{
    /// <summary>
    /// MISA partner type - EInvoice integration with MISA system
    /// </summary>
    public static readonly EInvoicePartnerType MISA = new(1, nameof(MISA));
    
    /// <summary>
    /// VIETTEL partner type - EInvoice integration with Viettel system
    /// </summary>
    public static readonly EInvoicePartnerType VIETTEL = new(2, nameof(VIETTEL));
    
    /// <summary>
    /// VNPT partner type - EInvoice integration with VNPT system
    /// </summary>
    public static readonly EInvoicePartnerType VNPT = new(3, nameof(VNPT));
    
    /// <summary>
    /// KIOTVIET partner type - EInvoice integration with KiotViet system
    /// </summary>
    public static readonly EInvoicePartnerType KIOTVIET = new(4, nameof(KIOTVIET));

    /// <summary>
    /// Initializes a new instance of EInvoicePartnerType
    /// </summary>
    /// <param name="id">The unique identifier for the partner type</param>
    /// <param name="name">The name of the partner type</param>
    public EInvoicePartnerType(int id, string name) : base(id, name)
    {
    }

    /// <summary>
    /// Gets all available EInvoice partner types
    /// </summary>
    /// <returns>Collection of all EInvoice partner types</returns>
    public static IEnumerable<EInvoicePartnerType> GetAll()
    {
        yield return MISA;
        yield return VIETTEL;
        yield return VNPT;
        yield return KIOTVIET;
    }

    /// <summary>
    /// Gets EInvoice partner type by ID
    /// </summary>
    /// <param name="id">The partner type ID</param>
    /// <returns>The corresponding EInvoice partner type</returns>
    /// <exception cref="ArgumentException">Thrown when invalid ID is provided</exception>
    public static EInvoicePartnerType FromId(int id)
    {
        return GetAll().FirstOrDefault(p => p.Id == id)
            ?? throw new ArgumentException($"Invalid EInvoice partner type ID: {id}", nameof(id));
    }

    /// <summary>
    /// Gets EInvoice partner type by name
    /// </summary>
    /// <param name="name">The partner type name</param>
    /// <returns>The corresponding EInvoice partner type</returns>
    /// <exception cref="ArgumentException">Thrown when invalid name is provided</exception>
    public static EInvoicePartnerType FromName(string name)
    {
        return GetAll().FirstOrDefault(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
            ?? throw new ArgumentException($"Invalid EInvoice partner type name: {name}", nameof(name));
    }
} 