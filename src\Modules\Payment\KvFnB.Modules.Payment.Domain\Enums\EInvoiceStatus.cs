using KvFnB.Core.Domain;

namespace KvFnB.Modules.Payment.Domain.Enums;

/// <summary>
/// Represents the status of an EInvoice setting
/// </summary>
public class EInvoiceStatus : Enumeration
{
    /// <summary>
    /// Inactive status - EInvoice setting is disabled
    /// </summary>
    public static readonly EInvoiceStatus InActive = new(0, nameof(InActive));
    
    /// <summary>
    /// Active status - EInvoice setting is enabled and operational
    /// </summary>
    public static readonly EInvoiceStatus Active = new(1, nameof(Active));

    /// <summary>
    /// Initializes a new instance of EInvoiceStatus
    /// </summary>
    /// <param name="id">The unique identifier for the status</param>
    /// <param name="name">The name of the status</param>
    public EInvoiceStatus(int id, string name) : base(id, name)
    {
    }

    /// <summary>
    /// Gets all available EInvoice statuses
    /// </summary>
    /// <returns>Collection of all EInvoice statuses</returns>
    public static IEnumerable<EInvoiceStatus> GetAll()
    {
        yield return InActive;
        yield return Active;
    }

    /// <summary>
    /// Gets EInvoice status by ID
    /// </summary>
    /// <param name="id">The status ID</param>
    /// <returns>The corresponding EInvoice status</returns>
    /// <exception cref="ArgumentException">Thrown when invalid ID is provided</exception>
    public static EInvoiceStatus FromId(int id)
    {
        return GetAll().FirstOrDefault(s => s.Id == id)
            ?? throw new ArgumentException($"Invalid EInvoice status ID: {id}", nameof(id));
    }

    /// <summary>
    /// Gets EInvoice status by name
    /// </summary>
    /// <param name="name">The status name</param>
    /// <returns>The corresponding EInvoice status</returns>
    /// <exception cref="ArgumentException">Thrown when invalid name is provided</exception>
    public static EInvoiceStatus FromName(string name)
    {
        return GetAll().FirstOrDefault(s => s.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
            ?? throw new ArgumentException($"Invalid EInvoice status name: {name}", nameof(name));
    }
} 