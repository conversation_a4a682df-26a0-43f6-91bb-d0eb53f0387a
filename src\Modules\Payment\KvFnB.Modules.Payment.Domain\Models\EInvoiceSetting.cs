using KvFnB.Core.Domain;
using KvFnB.Modules.Payment.Domain.Enums;

namespace KvFnB.Modules.Payment.Domain.Models;

/// <summary>
/// Represents an EInvoice setting configuration for a specific branch
/// Aggregate root for EInvoice setting domain
/// </summary>
public class EInvoiceSetting : AggregateRoot<long>, IAuditableEntity
{
    /// <summary>
    /// Gets the branch ID associated with this EInvoice setting
    /// </summary>
    public int BranchId { get; private set; }

    /// <summary>
    /// Gets the current status of this EInvoice setting
    /// </summary>
    public EInvoiceStatus Status { get; private set; } = default!; // Default initialization to avoid nullability issues

    /// <summary>
    /// Gets the EInvoice configuration as JSON string
    /// </summary>
    public string? EInvoiceConfig { get; private set; }

    /// <summary>
    /// Gets the partner type for EInvoice integration
    /// </summary>
    public EInvoicePartnerType PartnerType { get; private set; } = default!; // Default initialization to avoid nullability issues

    /// <summary>
    /// Gets or sets the ID of the user who created this setting
    /// </summary>
    public long CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the date and time when this setting was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the ID of the user who last modified this setting
    /// </summary>
    public long? ModifiedBy { get; set; }

    /// <summary>
    /// Gets or sets the date and time when this setting was last modified
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Protected parameterless constructor for EF Core
    /// </summary>
    protected EInvoiceSetting() { }

    /// <summary>
    /// Creates a new EInvoice setting for a specific branch
    /// </summary>
    /// <param name="branchId">The branch ID (must be greater than 0)</param>
    /// <param name="partnerType">The EInvoice partner type</param>
    /// <param name="eInvoiceConfig">Optional EInvoice configuration JSON</param>
    /// <returns>A new EInvoice setting instance</returns>
    /// <exception cref="ArgumentException">Thrown when branchId is less than or equal to 0</exception>
    /// <exception cref="ArgumentNullException">Thrown when partnerType is null</exception>
    public static EInvoiceSetting Create(int branchId, EInvoicePartnerType partnerType, string? eInvoiceConfig = null)
    {
        if (branchId <= 0)
            throw new ArgumentException("BranchId must be greater than 0", nameof(branchId));

        return new EInvoiceSetting
        {
            BranchId = branchId,
            PartnerType = partnerType,
            EInvoiceConfig = eInvoiceConfig,
            Status = EInvoiceStatus.Active // Default to Active when created
        };
    }

    /// <summary>
    /// Updates the status of this EInvoice setting
    /// </summary>
    /// <param name="status">The new status</param>
    /// <exception cref="ArgumentNullException">Thrown when status is null</exception>
    public void UpdateStatus(EInvoiceStatus status)
    {
        ArgumentNullException.ThrowIfNull(status);

        Status = status;
    }

    /// <summary>
    /// Updates the partner type and configuration of this EInvoice setting
    /// </summary>
    /// <param name="partnerType">The new partner type</param>
    /// <param name="eInvoiceConfig">The new EInvoice configuration JSON</param>
    /// <exception cref="ArgumentNullException">Thrown when partnerType is null</exception>
    public void Update(EInvoicePartnerType partnerType, string? eInvoiceConfig)
    {
        PartnerType = partnerType;
        EInvoiceConfig = eInvoiceConfig;
    }

    /// <summary>
    /// Updates the partner type of this EInvoice setting
    /// </summary>
    /// <param name="partnerType">The new partner type</param>
    /// <exception cref="ArgumentNullException">Thrown when partnerType is null</exception>
    public void UpdatePartnerType(EInvoicePartnerType partnerType)
    {
        PartnerType = partnerType;
    }

    /// <summary>
    /// Updates the EInvoice configuration of this setting
    /// </summary>
    /// <param name="eInvoiceConfig">The new EInvoice configuration JSON</param>
    public void UpdateConfiguration(string? eInvoiceConfig)
    {
        EInvoiceConfig = eInvoiceConfig;
    }

    /// <summary>
    /// Activates this EInvoice setting
    /// </summary>
    public void Activate()
    {
        Status = EInvoiceStatus.Active;
    }

    /// <summary>
    /// Deactivates this EInvoice setting
    /// </summary>
    public void Deactivate()
    {
        Status = EInvoiceStatus.InActive;
    }

    /// <summary>
    /// Gets a value indicating whether this EInvoice setting is active
    /// </summary>
    public bool IsActive => Status == EInvoiceStatus.Active;

    /// <summary>
    /// Gets a value indicating whether this EInvoice setting has configuration
    /// </summary>
    public bool HasConfiguration => !string.IsNullOrWhiteSpace(EInvoiceConfig);
}
