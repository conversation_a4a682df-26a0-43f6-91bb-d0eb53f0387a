using KvFnB.Core.Abstractions;
using KvFnB.Modules.Payment.Domain.Models;

namespace KvFnB.Modules.Payment.Domain.Repositories;

/// <summary>
/// Repository interface for EInvoice setting operations
/// </summary>
public interface IEInvoiceSettingRepository : IRepository<EInvoiceSetting, long>
{
    Task<bool> ExistsByBranchAsync(int branchId, CancellationToken cancellationToken = default);
    Task<List<EInvoiceSetting>> GetAllAsync(CancellationToken cancellationToken = default);
} 