﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace KvFnB.Modules.Payment.Domain.Services
{
    public interface IBranchService
    {
        /// <summary>
        /// Validates if all branch IDs exist in the database and none have limited access
        /// </summary>
        /// <param name="branchIds">List of branch IDs to validate</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if all branches exist and have no access limitations, otherwise false</returns>
        Task<bool> ValidateBranchAsync(List<int> branchIds, CancellationToken cancellationToken = default);
    }
}
