﻿using KvFnB.Modules.Payment.Domain.Models;
using KvFnB.Modules.Payment.Domain.Repositories;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.EntityFrameworkCore;

namespace KvFnB.Modules.Payment.Infrastructure.Persistence
{
    public class EInvoiceSettingRepository(ShardingDbContext context) : BaseRepository<EInvoiceSetting, long>(context), IEInvoiceSettingRepository
    {
        public async Task<bool> ExistsByBranchAsync(int branchId, CancellationToken cancellationToken = default)
        {
            return await _context.Set<EInvoiceSetting>()
                .AnyAsync(x => x.BranchId == branchId, cancellationToken);
        }

        public async Task<List<EInvoiceSetting>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            var query = _context.Set<EInvoiceSetting>().AsQueryable();
            return await query.ToListAsync(cancellationToken);
        }
    }
}
