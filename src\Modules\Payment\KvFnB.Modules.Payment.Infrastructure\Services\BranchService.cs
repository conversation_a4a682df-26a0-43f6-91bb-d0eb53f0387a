﻿using KvFnB.Core.Abstractions;
using KvFnB.Modules.Payment.Domain.Services;

namespace KvFnB.Modules.Payment.Infrastructure.Services
{
    /// <summary>
    /// Implementation of IBranchService for branch-related operations
    /// </summary>
    public class BranchService : IBranchService
    {
        private readonly IQueryService _queryService;
        private readonly ILogger _logger;

        /// <summary>
        /// Constructor for BranchService
        /// </summary>
        public BranchService(IQueryService queryService, ILogger logger)
        {
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Validates if all branch IDs exist in the database and none have limited access
        /// </summary>
        /// <param name="branchIds">List of branch IDs to validate</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if all branches exist and have no access limitations, otherwise false</returns>
        public async Task<bool> ValidateBranchAsync(List<int> branchIds, CancellationToken cancellationToken = default)
        {
            try
            {
                if (branchIds == null || branchIds.Count == 0)
                {
                    return true;
                }

                var parameters = new
                {
                    BranchIds = branchIds
                };

                // Check if all branches exist in the database
                const string existsSql = @"
                    SELECT COUNT(1)
                    FROM Branch
                    WHERE Id IN @BranchIds";

                var existingCount = await _queryService.QueryPlainTextAsync<int>(existsSql, parameters);

                // If the count of found branches doesn't match the input count, some branches don't exist
                return existingCount.FirstOrDefault() == branchIds.Count;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error validating branches: {ex.Message}", ex);
                return false;
            }
        }
    }
}
