using KvFnB.Modules.Payment.Application.UseCases.CheckDigitalSignatureStatus;
using KvFnB.Modules.Payment.Application.UseCases.GetEInvoiceQuota;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.CreateEInvoiceSettingUseCase;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingUseCase;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.UpdateEInvoiceSettingStatusUseCase;
using KvFnB.Modules.Payment.Application.UseCases.EInvoiceSettingUseCases.GetAllEInvoiceSettingsUseCase;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using KvFnB.Core.Authentication;
using KvFnB.Modules.Payment.Application.UseCases.Contracts;

namespace KvFnB.Modules.Payment.Restful;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class EInvoiceController : BaseApi
{
    private readonly CheckDigitalSignatureUseCase _checkSignatureUseCase;
    private readonly GetEInvoiceQuotaUseCase _getQuotaUseCase;
    private readonly CreateEInvoiceSettingUseCase _createSettingUseCase;
    private readonly UpdateEInvoiceSettingUseCase _updateSettingUseCase;
    private readonly UpdateEInvoiceSettingStatusUseCase _updateStatusUseCase;
    private readonly GetAllEInvoiceSettingsUseCase _getAllSettingsUseCase;
    private readonly IAuthUser _authUser;

    public EInvoiceController(
        IHttpContextAccessor httpContextAccessor,
        CheckDigitalSignatureUseCase checkSignatureUseCase,
        GetEInvoiceQuotaUseCase getQuotaUseCase,
        CreateEInvoiceSettingUseCase createSettingUseCase,
        UpdateEInvoiceSettingUseCase updateSettingUseCase,
        UpdateEInvoiceSettingStatusUseCase updateStatusUseCase,
        GetAllEInvoiceSettingsUseCase getAllSettingsUseCase,
        IAuthUser authUser) : base(httpContextAccessor)
    {
        _checkSignatureUseCase = checkSignatureUseCase ?? throw new ArgumentNullException(nameof(checkSignatureUseCase));
        _getQuotaUseCase = getQuotaUseCase ?? throw new ArgumentNullException(nameof(getQuotaUseCase));
        _createSettingUseCase = createSettingUseCase ?? throw new ArgumentNullException(nameof(createSettingUseCase));
        _updateSettingUseCase = updateSettingUseCase ?? throw new ArgumentNullException(nameof(updateSettingUseCase));
        _updateStatusUseCase = updateStatusUseCase ?? throw new ArgumentNullException(nameof(updateStatusUseCase));
        _getAllSettingsUseCase = getAllSettingsUseCase ?? throw new ArgumentNullException(nameof(getAllSettingsUseCase));
        _authUser = authUser ?? throw new ArgumentNullException(nameof(authUser));
    }

    [HttpGet("digital-signature-status")]
    [SwaggerOperation(Summary = "Check digital signature status", Description = "Checks the digital signature status for a merchant")]
    [SwaggerResponse(StatusCodes.Status200OK, "Returns the digital signature status", typeof(CheckDigitalSignatureResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request or validation failed")]
    public async Task<IActionResult> CheckDigitalSignatureStatus()
    {
        var request = new CheckDigitalSignatureRequest();
        var result = await _checkSignatureUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    [HttpGet("quota")]
    [SwaggerOperation(Summary = "Get e-invoice quota", Description = "Gets the e-invoice quota for a merchant")]
    [SwaggerResponse(StatusCodes.Status200OK, "Returns the e-invoice quota information", typeof(GetEInvoiceQuotaResponse))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request or validation failed")]
    public async Task<IActionResult> GetEInvoiceQuota([FromQuery] string taxCode)
    {
        var request = new GetEInvoiceQuotaRequest
        {
            TaxCode = taxCode
        };

        var result = await _getQuotaUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    // EInvoice Settings Endpoints

    [HttpGet("settings")]
    [SwaggerOperation(Summary = "Get all EInvoice settings", Description = "Gets all EInvoice settings for all branches of the current tenant")]
    [SwaggerResponse(StatusCodes.Status200OK, "Returns all EInvoice settings", typeof(GetAllEInvoiceSettingsResponse))]
    [SwaggerResponse(StatusCodes.Status403Forbidden, "Access denied - Admin access required")]
    public async Task<IActionResult> GetAllEInvoiceSettings()
    {
        if (!_authUser.IsAdmin)
        {
            return Forbid("Admin access required.");
        }

        var request = new GetAllEInvoiceSettingsRequest();
        var result = await _getAllSettingsUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    [HttpPost("settings")]
    [SwaggerOperation(Summary = "Create EInvoice setting", Description = "Creates a new EInvoice setting for a branch")]
    [SwaggerResponse(StatusCodes.Status201Created, "EInvoice setting created successfully", typeof(EInvoiceSettingDto))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request or validation failed")]
    [SwaggerResponse(StatusCodes.Status403Forbidden, "Access denied - Admin access required")]
    [SwaggerResponse(StatusCodes.Status409Conflict, "EInvoice setting already exists for this branch")]
    public async Task<IActionResult> CreateEInvoiceSetting([FromBody] CreateEInvoiceSettingRequest request)
    {
        if (!_authUser.IsAdmin)
        {
            return Forbid("Admin access required.");
        }

        var result = await _createSettingUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    [HttpPut("settings/{id}")]
    [SwaggerOperation(Summary = "Update EInvoice setting", Description = "Updates an existing EInvoice setting")]
    [SwaggerResponse(StatusCodes.Status200OK, "EInvoice setting updated successfully", typeof(EInvoiceSettingDto))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request or validation failed")]
    [SwaggerResponse(StatusCodes.Status403Forbidden, "Access denied - Admin access required")]
    [SwaggerResponse(StatusCodes.Status404NotFound, "EInvoice setting not found")]
    public async Task<IActionResult> UpdateEInvoiceSetting(long id, [FromBody] UpdateEInvoiceSettingRequest request)
    {
        if (!_authUser.IsAdmin)
        {
            return Forbid("Admin access required.");
        }

        // Ensure the ID from route matches the request
        request.Id = id;

        var result = await _updateSettingUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }

    [HttpPatch("settings/{id}/status")]
    [SwaggerOperation(Summary = "Update EInvoice setting status", Description = "Updates the status of an existing EInvoice setting (activate/deactivate)")]
    [SwaggerResponse(StatusCodes.Status200OK, "EInvoice setting status updated successfully", typeof(EInvoiceSettingDto))]
    [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request or validation failed")]
    [SwaggerResponse(StatusCodes.Status403Forbidden, "Access denied - Admin access required")]
    [SwaggerResponse(StatusCodes.Status404NotFound, "EInvoice setting not found")]
    public async Task<IActionResult> UpdateEInvoiceSettingStatus(long id, [FromBody] UpdateEInvoiceSettingStatusRequest request)
    {
        if (!_authUser.IsAdmin)
        {
            return Forbid("Admin access required.");
        }

        // Ensure the ID from route matches the request
        request.Id = id;

        var result = await _updateStatusUseCase.ExecuteAsync(request);
        return result.IsSuccess ? Success(result) : Failure(result);
    }
} 