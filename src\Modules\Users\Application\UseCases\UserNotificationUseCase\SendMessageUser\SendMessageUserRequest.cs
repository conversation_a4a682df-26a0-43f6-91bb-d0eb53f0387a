using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using System.ComponentModel;

namespace KvFnB.Modules.Users.Application.UseCases.UserNotificationUseCase.SendMessageUser
{
    /// <summary>
    /// Represents the request model for sending messages to users
    /// </summary>
    public record SendMessageUserRequest
    {
        /// <summary>
        /// The branch ID
        /// </summary>
        [JsonPropertyName("branch_id")]
        [Description("The ID of the branch")]
        public int BranchId { get; init; }

        /// <summary>
        /// Whether to send the message to all users
        /// </summary>
        [JsonPropertyName("is_send_user_admin")]
        [Description("Whether to send the message to all users")]
        public bool IsSendUserAdmin { get; init; }

        /// <summary>
        /// List of user IDs to send the message to
        /// </summary>
        [Required]
        [JsonPropertyName("user_ids")]
        [Description("List of user IDs to send the message to")]
        public List<long> UserIds { get; init; } = new List<long>();

        /// <summary>
        /// Type of event
        /// </summary>
        [Required]
        [JsonPropertyName("event_type")]
        [Description("Type of event that triggered this message")]
        public string EventType { get; init; } = string.Empty;

        /// <summary>
        /// Message content
        /// </summary>
        [Required]
        [JsonPropertyName("message")]
        [Description("The message content to be displayed to users")]
        public string Message { get; init; } = string.Empty;

        /// <summary>
        /// Additional payload data
        /// </summary>
        [Required]
        [JsonPropertyName("payload")]
        [Description("Additional data payload in JSON format")]
        public string Payload { get; init; } = string.Empty;

        /// <summary>
        /// Optional document ID related to this message
        /// </summary>
        [JsonPropertyName("document_id")]
        [Description("Optional ID of the document related to this message")]
        public long? DocumentId { get; init; }

        /// <summary>
        /// Optional document code related to this message
        /// </summary>
        [JsonPropertyName("document_code")]
        [Description("Optional code of the document related to this message")]
        public string DocumentCode { get; init; } = string.Empty;
    }
} 