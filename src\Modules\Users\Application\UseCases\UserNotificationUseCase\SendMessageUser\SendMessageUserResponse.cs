using System.Text.Json.Serialization;
using System.ComponentModel;

namespace KvFnB.Modules.Users.Application.UseCases.UserNotificationUseCase.SendMessageUser
{
    /// <summary>
    /// Represents the response model for sending messages to users
    /// </summary>
    public record SendMessageUserResponse
    {
        /// <summary>
        /// Indicates whether the message was sent successfully
        /// </summary>
        [JsonPropertyName("success")]
        [Description("Indicates whether the message was sent successfully")]
        public bool Success { get; init; }
        
        /// <summary>
        /// The number of users that the message was sent to
        /// </summary>
        [JsonPropertyName("recipients_count")]
        [Description("The number of users that the message was sent to")]
        public int RecipientsCount { get; init; }
    }
} 