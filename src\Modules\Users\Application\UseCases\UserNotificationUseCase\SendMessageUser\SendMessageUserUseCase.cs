using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Shared.DapperQuery;
using KvFnB.Shared.RabbitMq;
using KvFnB.Shared.RabbitMq.Models;
using Microsoft.Extensions.Logging;

namespace KvFnB.Modules.Users.Application.UseCases.UserNotificationUseCase.SendMessageUser
{
    /// <summary>
    /// Implements the SendMessage use case for sending notifications to users via RabbitMQ
    /// </summary>
    public class SendMessageUserUseCase : UseCaseBase<SendMessageUserRequest, SendMessageUserResponse>
    {
        private readonly ILogger<SendMessageUserUseCase> _logger;
        private readonly IRabbitMessageQueueService _rabbitService;
        private readonly ITenantProvider _tenantProvider;
        private readonly IValidator<SendMessageUserRequest> _validator;
        private readonly IQueryService _queryService;


        public SendMessageUserUseCase(
            IValidator<SendMessageUserRequest> validator,
            ILogger<SendMessageUserUseCase> logger,
            IRabbitMessageQueueService rabbitService,
            ITenantProvider tenantProvider,
            IQueryService queryService)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _rabbitService = rabbitService ?? throw new ArgumentNullException(nameof(rabbitService));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
        }

        public override async Task<Result<SendMessageUserResponse>> ExecuteAsync(
            SendMessageUserRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var validationResult = _validator.Validate(request);
                if (!validationResult.IsValid)
                {
                    return Result<SendMessageUserResponse>.Failure(validationResult.Errors);
                }

                // Get users from the query service
                var tenantId = _tenantProvider.GetTenantId() ?? 0;
                
                var query = new QueryBuilder()
                    .Select("u.Id, u.UserName, u.GivenName")
                    .From("[User] as u WITH(NOLOCK)")
                    .Where("u.RetailerId", "=", tenantId)
                    .WhereGroup(subQuery => subQuery
                        .WhereIsNull("u.IsDeleted")
                        .Where("u.IsDeleted", "<>", 1, "OR")
                    );

                if (request.IsSendUserAdmin)
                {
                    query.Where("u.IsAdmin", "=", true);
                } 
                else
                {
                    query.Where("u.Id", "IN", request.UserIds);
                }

                var users = await _queryService.QueryAsync<UserDto>(query);
                
                if (!users.Any())
                {
                    _logger.LogWarning("No users found with the provided IDs");
                    return Result<SendMessageUserResponse>.Failure("No users found with the provided UserIds");
                }

                // Create notification messages for each user
                var currentDateTime = DateTime.UtcNow;
                var groupId = _tenantProvider.GetShardId() ?? 0;
                var branchId = request.BranchId;

                var notificationMessages = users.Select(user => new NotificationMessage
                {
                    MessageId = Guid.NewGuid(),
                    BranchId = branchId,
                    UserId = user.Id,
                    EventType = request.EventType,
                    NotifyUser = user.Id,
                    NotifyMessage = request.Message,
                    Content = request.Message,
                    Payload = request.Payload,
                    Status = 1, // NotificationState.New
                    DocumentId = request.DocumentId,
                    DocumentCode = request.DocumentCode,
                    Shard = groupId,
                    GroupId = groupId.ToString(),
                    RetailerId = tenantId,
                    CreatedDate = currentDateTime,
                    ModifiedDate = currentDateTime,
                    IndustryId = 15, // Hardcoded as per original implementation
                    UserName = user.UserName,
                    ActionByUserName = user.UserName
                }).ToList();

                // Send notifications through RabbitMQ
                await _rabbitService.SendMessage(notificationMessages, cancellationToken);

                // Return successful response
                return Result<SendMessageUserResponse>.Success(new SendMessageUserResponse
                {
                    Success = true,
                    RecipientsCount = notificationMessages.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending messages to users: {ErrorMessage}", ex.Message);
                return Result<SendMessageUserResponse>.Failure($"Failed to send messages: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Represents a user data transfer object
    /// </summary>
    public class UserDto
    {
        /// <summary>
        /// The user's ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// The user's username
        /// </summary>
        public string UserName { get; set; } = string.Empty;
        
        /// <summary>
        /// The user's given name
        /// </summary>
        public string GivenName { get; set; } = string.Empty;
    }
} 