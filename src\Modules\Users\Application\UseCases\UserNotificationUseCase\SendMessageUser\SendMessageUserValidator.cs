using KvFnB.Core.Validation;
using KvFnB.Core.Validation.Extensions;
using System.Linq;

namespace KvFnB.Modules.Users.Application.UseCases.UserNotificationUseCase.SendMessageUser
{
    /// <summary>
    /// Validates the SendMessage request
    /// </summary>
    public class SendMessageUserValidator : Validator<SendMessageUserRequest>
    {
        public SendMessageUserValidator()
        {                
            // Validate UserIds
            RuleFor(x => (x.UserIds != null && x.UserIds.Count > 0) || x.IsSendUserAdmin)
                .NotNull("UserIds cannot be null or IsSendUserAdmin must be true")
                .Must(u => u, "Request must contain at least one user ID or is_send_user_admin must be true");
                
            // Validate Message content
            RuleFor(x => x.Message)
                .NotNull("Message content is required")
                .NotEmpty("Message content cannot be empty");
                
            // Validate Payload
            RuleFor(x => x.Payload)
                .NotNull("Payload is required")
                .NotEmpty("Payload cannot be empty");
                
            // Validate EventType
            RuleFor(x => x.EventType)
                .NotNull("EventType is required")
                .NotEmpty("EventType cannot be empty");
        }
    }
} 