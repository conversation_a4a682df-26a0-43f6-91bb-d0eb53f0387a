using KvFnB.Core.Validation;
using KvFnB.Modules.Users.Application.UseCases.UserNotificationUseCase.SendMessageUser;
using Microsoft.Extensions.DependencyInjection;

namespace KvFnB.Modules.Users.Infrastructure.DependencyInjection
{
    /// <summary>
    /// Dependency injection registration for Users module
    /// </summary>
    public static class ModuleRegistrar
    {
        /// <summary>
        /// Registers Users module services with the DI container
        /// </summary>
        public static IServiceCollection AddUsersModule(this IServiceCollection services)
        {
            // Register use cases
            services.AddScoped<SendMessageUserUseCase>();
            
            // Register validators
            services.AddScoped<IValidator<SendMessageUserRequest>, SendMessageUserValidator>();            
            
            return services;
        }
    }
} 