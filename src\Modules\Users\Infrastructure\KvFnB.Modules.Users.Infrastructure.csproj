<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\KvFnB.Shared.csproj" />
    <ProjectReference Include="..\Application\KvFnB.Modules.Users.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>KvFnB.Modules.Users.Infrastructure</RootNamespace>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project> 