using KvFnB.CoreAPI.Extensions;
using KvFnB.Modules.Menu.Infrastructure.DependencyInjection;
using KvFnB.Modules.Customer.Infrastructure.DependencyInjection;
using KvFnB.Modules.Supplier.Infrastructure.DependencyInjection;
using KvFnB.Shared.DependencyInjection;
using KvFnB.Shared.Logging;
using KvFnB.Shared.Middlewares;
using Serilog;
using KvFnB.Shared.Amazon;
using KvFnB.Modules.Payment.Infrastructure.DependencyInjection;
using KvFnB.Modules.DataManagement.Infrastructure.DependencyInjection;

var builder = WebApplication.CreateBuilder(args);
Log.Logger = SerilogHelpers.CreateSerilogLogger("KvFnB_Core_API", builder.Configuration); // Create Serilog logger
builder.Services.AddCoreApiControllers(); // Register controllers
builder.Services.AddEndpointsApiExplorer(); // Register API explorer
builder.Services.AddOpenApiSwagger(); // Register Swagger
builder.Services.AddSerilogLogging(Log.Logger); // Register Serilog logging
builder.Services.AddServiceStackJwtkAuth(builder.Configuration); // Register Keycloak authentication
builder.Services.AddRedisCacheProvider(builder.Configuration); // Register Redis cache provider
builder.Services.AddRedisMqConnection(builder.Configuration); // Register Redis mq provider
builder.Services.AddAuditTrailService(builder.Configuration); // Register Audit trail service
builder.Services.AddAuthUser(); // Register Auth user
builder.Services.AddHttpTenantProvider(); // Register Http tenant provider
builder.Services.AddAutoMapperWithProvider(); // Register AutoMapper
builder.Services.AddShardingDbContext(builder.Configuration); // Register Sharding database
builder.Services.AddTenantConfiguration(); // Register Tenant configuration
builder.Services.AddDapperQueryService(builder.Configuration); // Register Dapper query service
builder.Services.AddTenantInfo(); // Register Tenant info
builder.Services.AddMultiCurrencyProvider(); // Register Multi-currency provider
builder.Services.AddCodeGenerationService(builder.Configuration); // Register Code generation service
builder.Services.AddPermissionServices();
builder.Services.AddCultureProvider(); // Register Culture provider
builder.Services.AddLocalizationProvider(); // Register Localization provider
builder.Services.AddMenuModule(builder.Configuration); // Register Menu module
builder.Services.AddS3Service(builder.Configuration);
builder.Services.AddCustomerModule(builder.Configuration); // Register Customer module
builder.Services.AddPaymentModule(builder.Configuration); // Register Payment module
builder.Services.AddAddressServices(); // Register Address services
builder.Services.AddSupplierModule(builder.Configuration); // Register Supplier module
builder.Services.RegisterDataManagementModule(builder.Configuration); // Register DataManagementModule
builder.Services.AddDomainEventDispatcher(); // Register DomainEventDispatcher

// Add CORS configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.WebHost.ConfigureKestrel(opts =>
{
    opts.AllowSynchronousIO = true;
    opts.RequestHeaderEncodingSelector = _ => System.Text.Encoding.Latin1;
});

var app = builder.Build();

// Exception handling (first)
app.UseMiddleware<ExceptionHandlingMiddleware>();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "KvFnB Core API"));
}
// Add routing
app.UseRouting();
// CORS after routing
app.UseCors("AllowAll");
// Security middleware
app.UseAuthentication();
app.UseAuthorization();
// Endpoints (last)
app.MapControllers();
app.Run();


