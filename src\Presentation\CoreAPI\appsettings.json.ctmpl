{
  "AllowedHosts": "{{ key (print "config/" (env "APP_ENV") "/api_core/allowed_hosts") }}",
  "ProxyUrl": "{{ key (print "config/" (env "APP_ENV") "/general_config/proxy_url") }}",
  "ConnectionStrings": { {{ $names := parseJSON (key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings_names")) }}
  {{ $lookup := parseJSON (key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings_lookup")) }}
  {{ range $index, $name := $names }}  {{ if gt $index 0 }},
  {{ end }}  {{ $sname := index $lookup $name }}"{{ $name }}": "Server={{ key (print "config/" (env "APP_ENV") "/general_config/connection_strings/" $sname "/server") }};Database={{ key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/database") }};{{ if keyExists (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/persist_security_info") }}Persist Security Info={{ key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/persist_security_info") }};{{ end }}{{ if keyExists (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/user") }}User={{ key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/user") }};{{ end }}{{ if keyExists (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/user_id") }}User Id={{ key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/user_id") }};{{ end }}Password={{ with secret (print "kvv2/applications/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/password") }}{{ .Data.data.password }}{{ end }};MultipleActiveResultSets={{ key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/multiple_active_result_sets") }};Max Pool Size={{ key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/max_pool_size") }};{{ if keyExists (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/trust_server_certificate") }}TrustServerCertificate={{ key (print "config/" (env "APP_ENV") "/" "api_core" "/connection_strings/" $sname "/trust_server_certificate") }};{{ end }}"{{ end }}
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/minimum_level/default") }}",
      "Override": {
        "Microsoft": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/minimum_level/override/microsoft") }}",
        "System": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/minimum_level/override/system") }}"
      }
    },
    "WriteTo": [
      {
        "Name": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/name") }}",
        "Args": {
          "path": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/path") }}",
          "rollingInterval": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/rolling_interval") }}",
          "fileSizeLimitBytes": {{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/file_size_limit_bytes") }},
          "rollOnFileSizeLimit": true,
          "formatter": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/args/formatter") }}"
        }
      },
      {
        "Name": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/write_to/name") }}"
      }
    ],
    "Properties": {
      "ApplicationName": "{{ key (print "config/" (env "APP_ENV") "/api_core/serilog/properties/application_name") }}"
    }
  },
  "RedisCache": {
    "Servers": "{{ key (print "config/" (env "APP_ENV") "/general_config/redis_cache/servers") }}",
    "SentinelMasterName": "{{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/sentinel_master_name") }}",
    "DbNumber": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/db_number") }},
    "IsSentinel": true,
    "IsStrictPool": true,
    "MaxPoolSize": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/max_pool_size") }},
    "MaxPoolTimeout": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/max_pool_timeout") }},
    "WaitBeforeForcingMasterFailover": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_cache/wait_before_forcing_master_failover") }}
  },
  "RedisMq": {
    "Servers": "{{ key (print "config/" (env "APP_ENV") "/general_config/redis_mq/servers") }}",
    "SentinelMasterName": "{{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/sentinel_master_name") }}",
    "DbNumber": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/db_number") }},
    "IsSentinel": true,
    "IsStrictPool": true,
    "MaxPoolSize": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/max_pool_size") }},
    "MaxPoolTimeout": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/max_pool_timeout") }},
    "WaitBeforeForcingMasterFailover": {{ key (print "config/" (env "APP_ENV") "/api_core/redis_mq/wait_before_forcing_master_failover") }}
  },
  "ServiceStackJwtSettings": {
    "AuthKeyBase64": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/auth_key_base64") }}{{ .Data.data.auth_key_base64 }}{{ end }}",
    "AesIvBase64": "{{ key (print "config/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/aes_iv_base64") }}",
    "PrivateKeyXml": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/private_key_xml") }}{{ .Data.data.private_key_xml }}{{ end }}",
    "PublicKeyXml": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/public_key_xml") }}{{ .Data.data.public_key_xml }}{{ end }}",
    "ExpireTokensInDays": {{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/expire_tokens_in_days") }}{{ .Data.data.expire_tokens_in_days }}{{ end }},
    "ExpireTokensRefreshInDays": {{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/expire_tokens_refresh_in_days") }}{{ .Data.data.expire_tokens_refresh_in_days }}{{ end }},
    "Issuer": "{{ key (print "config/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/issuer") }}",
    "EncryptPayload": false,
    "RequireSecureConnection": false,
    "HashAlgorithm": "{{ key (print "config/" (env "APP_ENV") "/api_core/service_stack_jwt_settings/hash_algorithm") }}",
    "RequireHashAlgorithm": false
  },
  "OtpConfig": {
    "OtpSecret": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/otp_config/otp_secret") }}{{ .Data.data.otp_secret }}{{ end }}",
    "OtpIssuer": "{{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/otp_issuer") }}",
    "MaximumSmsRequestFromRetailerPerDay": {{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/maximum_sms_request_from_retailer_per_day") }},
    "SmsCountKeyPrefix": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/otp_config/sms_count_key_prefix") }}{{ .Data.data.sms_count_key_prefix }}{{ end }}",
    "OtpIssuser": "{{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/otp_issuser") }}",
    "OtpMessageTemplate": "{{ key (print "config/" (env "APP_ENV") "/api_core/otp_config/otp_message_template") }}"
  },
  "Kfin": {
    "InternalEndPoint": "{{ key (print "config/" (env "APP_ENV") "/api_core/kfin/internal_end_point") }}",
    "InternalPrivateKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/kfin/internal_private_key") }}{{ .Data.data.internal_private_key }}{{ end }}",
    "ProxyUrl": "{{ key (print "config/" (env "APP_ENV") "/api_core/kfin/proxy_url") }}"
  },
  "Kma": {
    "CmaOpenDomainEndpoint": "{{ key (print "config/" (env "APP_ENV") "/api_core/kma/cma_open_domain_endpoint") }}",
    "ApiKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/kma/api_key") }}{{ .Data.data.api_key }}{{ end }}",
    "Timeout": {{ key (print "config/" (env "APP_ENV") "/api_core/kma/timeout") }},
    "RetryCount": {{ key (print "config/" (env "APP_ENV") "/api_core/kma/retry_count") }}
  },
  "AmazonS3": {
    "BucketName": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/bucket_name") }}",
    "Region": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/region") }}",
    "AccessKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/access_key") }}{{ .Data.data.access_key }}{{ end }}",
    "SecretKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/secret_key") }}{{ .Data.data.secret_key }}{{ end }}",
    "UseInstanceProfile": false,
    "UseCompatibleMode": true,
    "CompatibleEndpoint": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/compatible_endpoint") }}",
    "CompatibleAccessKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/compatible_access_key") }}{{ .Data.data.compatible_access_key }}{{ end }}",
    "CompatibleSecretKey": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/compatible_secret_key") }}{{ .Data.data.compatible_secret_key }}{{ end }}",
    "ForcePathStyle": true,
    "ProxyUrl": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/proxy_url") }}",
    "BaseKeyPrefix": "{{ with secret (print "kvv2/applications/" (env "APP_ENV") "/api_core/amazon_s3/base_key_prefix") }}{{ .Data.data.base_key_prefix }}{{ end }}",
    "UploadPartSize": {{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/upload_part_size") }},
    "DefaultACL": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/default_acl") }}",
    "StorageClass": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/storage_class") }}",
    "EncryptionMethod": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/encryption_method") }}",
    "CacheControlHeader": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/cache_control_header") }}",
    "CloudfrontUrl": "{{ key (print "config/" (env "APP_ENV") "/api_core/amazon_s3/cloudfront_url") }}"
  },
  "TfaRedisMessagequeue": "{{ key (print "config/" (env "APP_ENV") "/api_core/tfa_redis_messagequeue") }}",
  "Kafka": {
    "BootstrapServers": "{{ key (print "config/" (env "APP_ENV") "/general_config/kafka/bootstrap_servers") }}",
    "DefaultTopic": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka/default_topic") }}",
    "ClientId": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka/client_id") }}",
    "MessageMaxBytes": {{ key (print "config/" (env "APP_ENV") "/api_core/kafka/message_max_bytes") }},
    "RetryAttempts": {{ key (print "config/" (env "APP_ENV") "/api_core/kafka/retry_attempts") }}
  },
  "KafkaSmSProducer": {
    "BootstrapServers": "{{ key (print "config/" (env "APP_ENV") "/general_config/kafka_sms_producer/bootstrap_servers") }}",
    "ClientId": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka_sms_producer/client_id") }}",
    "Topic": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka_sms_producer/topic") }}"
  },
  "KafkaCommandDispatcherProducer": {
    "BootstrapServers": "{{ key (print "config/" (env "APP_ENV") "/general_config/kafka_command_dispatcher_producer/bootstrap_servers") }}",
    "Topic": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka_command_dispatcher_producer/topic") }}",
    "ClientId": "{{ key (print "config/" (env "APP_ENV") "/api_core/kafka_command_dispatcher_producer/client_id") }}"
  }
}