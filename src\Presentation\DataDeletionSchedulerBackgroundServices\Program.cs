using KvFnB.DataDeletionSchedulerBackgroundService.Extensions;
using KvFnB.Modules.DataManagement.Infrastructure.DependencyInjection;
using Quartz;
using KvFnB.Presentation.BackgroundServices.Extensions;
using Serilog;
using KvFnB.Shared.DependencyInjection;
using KvFnB.Shared.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace KvFnB.DataDeletionSchedulerBackgroundService
{
    public class Program
    {
        public const string AppName = "KvFnB_Data_Deletion_Scheduler_BackgroundService";
        public static async Task Main(string[] args)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: false)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            try
            {
                JsonConvert.DefaultSettings = () => new JsonSerializerSettings
                {
                    Formatting = Formatting.Indented,
                    ContractResolver = new CamelCasePropertyNamesContractResolver(),
                    DateParseHandling = DateParseHandling.DateTimeOffset,
                    DateFormatHandling = DateFormatHandling.IsoDateFormat
                };

                Log.Logger = SerilogHelpers.CreateSerilogLogger(AppName, configuration); // Create Serilog logger

                await CreateHostBuilder(configuration, args).Build().RunAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Data Deletion Scheduler Background Service terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(IConfiguration configuration, string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration(x => x.AddConfiguration(configuration))
                .UseSerilog()
                .ConfigureServices((hostContext, services) =>
                {
                    // Register Data Management module
                    services.RegisterBackgroundDataManagementModule(hostContext.Configuration);
                    services.AddDapperQueryService();
                    services.AddDbShardingContext(hostContext.Configuration);

                    // Register Serilog logging
                    var AppName = "KvFnB_Data_Deletion_Scheduler_BackgroundService";
                    Log.Logger = SerilogHelpers.CreateSerilogLogger(AppName, hostContext.Configuration);
                    services.AddSerilogLogging(Log.Logger);

                    Log.Information("Starting host...");

                    // Register Quartz services
                    services.AddScheduleJob(hostContext.Configuration);

                    // Add the Quartz.NET hosted service
                    services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);
                    // Register data deletion services
                    services.AddCompletedDataProcessingServices(hostContext.Configuration);
                    services.AddRedisDistributedLockProvider(hostContext.Configuration);
                });
    }
}