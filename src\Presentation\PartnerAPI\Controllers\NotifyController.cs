using AutoMapper;
using KvFnB.Modules.Users.Application.UseCases.UserNotificationUseCase.SendMessageUser;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace KvFnB.PartnerAPI.Controllers

{
    [ApiController]
    [Route("notify")]
    public class NotifyController : BaseApi
    {
        private readonly SendMessageUserUseCase _sendMessageUserUseCase;
        private readonly IMapper _mapper;
        public NotifyController(
            IHttpContextAccessor httpContextAccessor, 
            SendMessageUserUseCase sendMessageUserUseCase, 
            IMapper mapper) : base(httpContextAccessor)
        {
            _sendMessageUserUseCase = sendMessageUserUseCase ?? throw new ArgumentNullException(nameof(sendMessageUserUseCase));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        [HttpPost("send-message-user")]
        [SwaggerOperation(Summary = "Send a message to a user", Description = "Send a message to a user")]
        [SwaggerResponse(StatusCodes.Status200OK, "Message sent successfully", typeof(SendMessageUserResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid request")]
        public async Task<IActionResult> SendMessageUser([FromBody] Models.SendMessageUserRequest request)
        {
            var domainRequest = _mapper.Map<SendMessageUserRequest>(request);
            var result = await _sendMessageUserUseCase.ExecuteAsync(domainRequest);
            return result.IsSuccess ? Success(result) : Failure(result);
        }
    }

}