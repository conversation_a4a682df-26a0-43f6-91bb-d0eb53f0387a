using KvFnB.Modules.Payment.Application.UseCases.PayslipPaymentUseCases.GetPayslipPaymentByIds;
using KvFnB.Shared.Filters;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace KvFnB.PartnerAPI.Controllers
{
    [ApiController]
    [Route("payslip-payments")]
    [Authorize]
    public class PayslipPaymentController : ControllerBase
    {
        private readonly GetPayslipPaymentByIdsUseCase _getPayslipPaymentByIdsUseCase;

        public PayslipPaymentController(GetPayslipPaymentByIdsUseCase getPayslipPaymentByIdsUseCase)
        {
            _getPayslipPaymentByIdsUseCase = getPayslipPaymentByIdsUseCase ?? throw new ArgumentNullException(nameof(getPayslipPaymentByIdsUseCase));
        }

        /// <summary>
        /// Retrieves multiple payslip payment records by their IDs
        /// </summary>
        /// <param name="request">Request containing list of payslip payment IDs to retrieve</param>
        /// <returns>Collection of payslip payment records</returns>
        [HttpPost("by-ids")]
        [SwaggerOperation(
            Summary = "Get payslip payments by IDs",
            Description = "Retrieves multiple payslip payment records by providing a list of IDs. Returns empty collection if no matching records found.",
            OperationId = "GetPayslipPaymentByIds",
            Tags = new[] { "PayslipPayments" })]
        [SwaggerResponse(StatusCodes.Status200OK, "Returns the payslip payment records", typeof(GetPayslipPaymentByIdsResponse))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "If the request is invalid or validation fails")]
        [SwaggerResponse(StatusCodes.Status401Unauthorized, "If the user is not authenticated")]
        [SwaggerResponse(StatusCodes.Status403Forbidden, "If the user lacks required permissions")]
        [SwaggerResponse(StatusCodes.Status500InternalServerError, "If an internal server error occurs")]
        public async Task<IActionResult> GetPayslipPaymentByIds([FromBody] GetPayslipPaymentByIdsRequest request)
        {
            var result = await _getPayslipPaymentByIdsUseCase.ExecuteAsync(request);

            if (!result.IsSuccess)
            {
                return BadRequest(result.ErrorMessage);
            }

            return Ok(result.Value);
        }
    }
}
