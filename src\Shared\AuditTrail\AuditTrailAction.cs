using System.ComponentModel;

namespace KvFnB.Shared.AuditTrail
{
    public enum AuditTrailAction
    {
        [Description("Thêm mới")]
        Create = 1,
        [Description("Cập nhật")]
        Update = 2,
        [Description("Xóa")]
        Delete = 3,
        [Description("Hủy")]
        Reject = 4,
        [Description("Import")]
        ImportFile = 5,
        [Description("Export")]
        ExportFile = 6,
        [Description("Đăng nhập")]
        Login = 7,
        [Description("Xem")]
        Read = 8,
        [Description("Đồng bộ hàng hóa")]
        ProductIntergate = 9,
        [Description("Đồng bộ đặt hàng")]
        OrderIntergate = 10,
        [Description("Đồng bộ hóa đơn")]
        InvoiceIntergate = 11,
        [Description("Đăng xuất")]
        Logout = 13,
        [Description("Đổi ca")]
        SwapClocking = 200,
        [Description("Đồng bộ chấm công")]
        AutoTimekeeping = 201
    }
}