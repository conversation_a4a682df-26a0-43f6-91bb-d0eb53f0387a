﻿using KvFnB.Core.Abstractions;

namespace KvFnB.Shared.Configuration
{
    public class KafkaProducerConfiguration : IKafkaProducerConfiguration
    {
        public string BootstrapServers { get; set; } = string.Empty;

        public string ClientId { get; set; } = string.Empty;

        public int MessageMaxBytes { get; set; } = 104857600; // optional: default 100MB

        public int MaximumRetryProduce { get; set; } = 3; // optional: default 3q

        public string Topic { get; set; } = string.Empty;
    }
}
