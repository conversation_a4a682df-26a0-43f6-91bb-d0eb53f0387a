﻿using KvFnB.Core.Abstractions;
using KvFnB.Shared.Configuration;
using KvFnB.Shared.MessageQueueProvider.Kafka;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;


namespace KvFnB.Shared.DependencyInjection
{
    public static class KafkaProducerExtensions
    {
        public static IServiceCollection AddKafkaProducer(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IKafkaProducerConfiguration>(provider =>
            {
                var kafkaConfiguration = new KafkaProducerConfiguration();
                configuration.GetSection("KafkaProducer").Bind(kafkaConfiguration);
                return kafkaConfiguration;
            });

            services.AddSingleton<IManagedProducer, ManagedProducer>();

            return services;
        }
    }
}
