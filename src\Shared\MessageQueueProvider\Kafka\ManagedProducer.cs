﻿using Confluent.Kafka;
using KvFnB.Core.Abstractions;
using Polly;

namespace KvFnB.Shared.MessageQueueProvider.Kafka
{
    public class ManagedProducer : IManagedProducer, IDisposable
    {
        private readonly IKafkaProducerConfiguration _configuration;
        private readonly ILogger _logger;
        private IProducer<string, string>? _producer;
        private bool _disposedValue;
        public ManagedProducer(
            IKafkaProducerConfiguration configuration,
            ILogger logger
        )
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task ProducerAsync(string topic, Message<string, string> message, CancellationToken cancellationToken = default)
        {
            if (_producer == null)
            {
                CreateProducer();
            }

            var retryPolicy = Policy.Handle<Exception>()
                .WaitAndRetryAsync(
                _configuration.MaximumRetryProduce, 
                retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                    (ex, time) =>
                    {
                        _logger.Error($"Could not publish retry message after {time.TotalSeconds:N1}s ({ex.Message})");
                    });

            await retryPolicy.ExecuteAsync(async () =>
            {
                await _producer!.ProduceAsync(topic, message, cancellationToken);
            });
        }

        private void CreateProducer()
        {
            var config = new ProducerConfig
            {
                BootstrapServers = _configuration.BootstrapServers,
                ClientId = _configuration.ClientId,
                MessageMaxBytes = _configuration.MessageMaxBytes
            };
            _producer = new ProducerBuilder<string, string>(config)
                .SetLogHandler((_, message) =>
                {
                    _logger.Information($"Kafka producer log: level: {message.Level} message: {message.Message}");
                })
                .Build();
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposedValue)
            {
                if (disposing)
                {
                    _producer?.Dispose();
                    _producer = null;
                }
                _disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}
