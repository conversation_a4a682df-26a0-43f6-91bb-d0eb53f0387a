using KvFnB.Modules.Payment.Domain.Models;
using KvFnB.Modules.Payment.Domain.Enums;
using KvFnB.Shared.Persistence.ShardingDb.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KvFnB.Shared.Persistence.ShardingDb.EntityTypeConfigurations
{
    /// <summary>
    /// Entity Framework configuration for EInvoiceSetting
    /// </summary>
    public class EInvoiceSettingEntityTypeConfiguration : BaseEntityTypeConfiguration<EInvoiceSetting>
    {
        public override void Configure(EntityTypeBuilder<EInvoiceSetting> builder)
        {
            base.Configure(builder);
            builder.ToTable("EInvoiceSetting");

            // Primary Key
            builder.Property(e => e.Id)
                .UseIdentityColumn()
                .ValueGeneratedOnAdd()
                .HasColumnName("Id")
                .HasColumnType(SqlServerColumnTypes.BIGINT);

            // Branch ID - Required
            builder.Property(e => e.BranchId)
                .IsRequired()
                .HasColumnName("BranchId")
                .HasColumnType(SqlServerColumnTypes.INT);

            // Status - Required, with value converter to map custom enum to int
            builder.Property(e => e.Status)
                .IsRequired()
                .HasColumnName("Status")
                .HasColumnType(SqlServerColumnTypes.BYTE)
                .HasConversion(
                    status => status.Id,  // Convert enum to int for database
                    statusId => EInvoiceStatus.FromId(statusId)); // Convert int back to enum

            // EInvoice Configuration JSON - Optional
            builder.Property(e => e.EInvoiceConfig)
                .IsRequired(false)
                .HasColumnName("EInvoiceConfig")
                .HasColumnType(SqlServerColumnTypes.NVARCHARMAX);

            // Partner Type - Required, with value converter to map custom enum to int
            builder.Property(e => e.PartnerType)
                .IsRequired()
                .HasColumnName("PartnerType")
                .HasColumnType(SqlServerColumnTypes.INT)
                .HasConversion(
                    partnerType => partnerType.Id, // Convert enum to int for database
                    partnerTypeId => EInvoicePartnerType.FromId(partnerTypeId)); // Convert int back to enum
        }
    }
} 