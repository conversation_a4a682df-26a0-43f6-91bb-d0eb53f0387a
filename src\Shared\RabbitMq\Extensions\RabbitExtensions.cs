using KiotVietFnB.RabbitMq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;

namespace KvFnB.Shared.RabbitMq.Extensions
{
    public static class RabbitExtensions
    {
        private const string _rabbitMq = "RabbitMessageQueue";
        public static void AddRabbitMqExtension(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IRabbitMessageQueueConfiguration>(sp =>
            {
                var config = new RabbitMessageQueueConfiguration();
                configuration.GetSection(_rabbitMq).Bind(config);
                return config;
            });

            services.AddSingleton<IRabbitMqConfig>(sp =>
            {
                var notificationConfiguration = sp.GetRequiredService<IRabbitMessageQueueConfiguration>();
                return notificationConfiguration.RabbitMq;
            });
            services.AddScoped<IRabbitMessageQueueService, RabbitMessageQueueService>();
            services.TryAddSingleton<DefaultObjectPoolProvider>();
            services.AddSingleton<IRabbitMqClient, RabbitMqClient>();
            
            services.AddSingleton<IRabbitMqProducer>(sp =>
            {
                var client = sp.GetRequiredService<IRabbitMqClient>();
                var channelPoolPolicy = new RabbitMqChannelPoolPolicy(client.GetConnection());
                var provider = sp.GetRequiredService<DefaultObjectPoolProvider>();
                var rabbitConfig = sp.GetRequiredService<IRabbitMessageQueueConfiguration>();
                provider.MaximumRetained = rabbitConfig.MaximumRetainedCapacity;
                var logger = sp.GetRequiredService<ILogger<RabbitMqProducer>>();
                return new RabbitMqProducer(provider.Create(channelPoolPolicy), logger);
            });
        }
    }
}
