namespace KvFnB.Shared.RabbitMq.Models
{
    public class ExecutionContext
    {
        public string Id { get; set; } = string.Empty;
        public SessionUser User { get; set; } = new SessionUser();
        public string IpSource { get; set; } = string.Empty;
        public string ClientInfo { get; set; } = string.Empty;
        public string Host { get; set; } = string.Empty;
        public int BranchId { get; set; }
        public IDictionary<string, ISet<int>> Permissions { get; set; } = new Dictionary<string, ISet<int>>();
        public int[] AuthorizedBranchIds { get; set; } = Array.Empty<int>();
        public int RetailerId { get; set; }
        public string RetailerCode { get; set; } = string.Empty;
        public int IndustryId { get; set; }
        public int GroupId { get; set; }
        public KvGroup Group { get; set; } = new KvGroup();
        public int ConfigDefaultTimeSetting { get; set; }
    }

    public class KvGroup
    {
        public int Id { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public string PromotionConnectionString { get; set; } = string.Empty;
        public bool IsCurrent { get; set; }
        public string Server { get; set; } = string.Empty;
        public string Prefix { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string TimeSheetConnectionString { get; set; } = string.Empty;
        public string FnbEventStoreConnectionString { get; set; } = string.Empty;
        public string FnbPartnerMappingConnectionString { get; set; } = string.Empty;
    }
}
