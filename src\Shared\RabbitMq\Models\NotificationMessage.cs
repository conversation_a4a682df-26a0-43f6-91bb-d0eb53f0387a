namespace KvFnB.Shared.RabbitMq.Models
{
    public class NotificationMessage
    {
        public long UserId { get; set; }
        public int BranchId { get; set; }
        public int RetailerId { get; set; }
        public int FunctionId { get; set; }
        public int Action { get; set; }
        public string Content { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string IpSource { get; set; } = string.Empty;
        public string ClientInfo { get; set; } = string.Empty;
        public long? DocumentId { get; set; }
        public string NewDocumentId { get; set; } = string.Empty;
        public string DocumentCode { get; set; } = string.Empty;
        public long ActionByUserId { get; set; }
        public string ActionByUserName { get; set; } = string.Empty;
        public string BranchName { get; set; } = string.Empty;
        public int ToBranchId { get; set; }
        public string ToBranchName { get; set; } = string.Empty;
        public string GroupId { get; set; } = string.Empty;
        public string NotifyMessage { get; set; } = string.Empty;
        public string EventType { get; set; } = string.Empty;
        public decimal? Value { get; set; }
        public string ResponseError { get; set; } = string.Empty;
        public int Status { get; set; }
        public bool? IsSent { get; set; }
        public bool? IsSystem { get; set; }
        public bool? IsPrintedOrder { get; set; }
        public long NotifyUser { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int IndustryId { get; set; }
        public string DocumentItem { get; set; } = string.Empty;
        public string DocumentImage { get; set; } = string.Empty;
        public string TableName { get; set; } = string.Empty;
        public int? TableId { get; set; }
        public string TableGroupName { get; set; } = string.Empty;
        public string Payload { get; set; } = string.Empty;
        public string ToDevice { get; set; } = string.Empty;
        public int Shard { get; set; }
        public Guid MessageId { get; set; }
    }
}
