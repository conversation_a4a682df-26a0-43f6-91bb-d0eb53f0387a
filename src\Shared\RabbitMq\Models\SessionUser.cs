namespace KvFnB.Shared.RabbitMq.Models
{
    public class SessionUser
{
        public long Id { get; set; }
        public int RetailerId { get; set; }
        public byte Type { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string GivenName { get; set; } = string.Empty;
        public bool IsAdmin { get; set; }
        public bool IsActive { get; set; }
        public bool IsLimitTime { get; set; }
        public bool IsLimitedByTrans { get; set; }
        public bool IsShowSumRow { get; set; }
    }
}
