using KiotVietFnB.RabbitMq;
using KvFnB.Shared.RabbitMq.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace KvFnB.Shared.RabbitMq
{
    public class RabbitMessageQueueService : IRabbitMessageQueueService
    {
        private readonly ILogger<RabbitMessageQueueService> _logger;
        private readonly IRabbitMessageQueueConfiguration _notificationConfiguration;
        private readonly IRabbitMqProducer _rabbitMqProducer;

        public RabbitMessageQueueService(
            ILogger<RabbitMessageQueueService> logger,
            IRabbitMessageQueueConfiguration notificationConfiguration,
            IRabbitMqProducer rabbitMqProducer
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _notificationConfiguration = notificationConfiguration ?? throw new ArgumentNullException(nameof(notificationConfiguration));
            _rabbitMqProducer = rabbitMqProducer ?? throw new ArgumentNullException(nameof(rabbitMqProducer));
        }

        public async Task SendMessage(List<NotificationMessage> messages, CancellationToken cancellationToken = default)
        {
            foreach (var notiMsg in messages)
            {
                try
                {
                    var msg = new NotificationMessageQueue
                    {
                        Data = notiMsg,
                        Context = new Models.ExecutionContext()
                        {
                            User = new SessionUser()
                            {
                                Id = notiMsg.UserId,
                                UserName = notiMsg.UserName,
                                GivenName = notiMsg.UserName
                            },
                            GroupId = notiMsg.Shard,
                            BranchId = notiMsg.BranchId,
                            RetailerId = notiMsg.RetailerId
                        }
                    };

                    var msgString = JsonConvert.SerializeObject(msg,
                                        new JsonSerializerSettings()
                                        {
                                            Formatting = Formatting.None
                                        });
                    await _rabbitMqProducer.ProduceAsync(msgString, routingKey: _notificationConfiguration.RabbitMq.QueueName, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending message to RabbitMQ: {Message}", ex.Message);
                }
            }
        }
    }
}

