using KvFnB.Core.Abstractions;
using KvFnB.Core.Application.Dispatcher;
using KvFnB.Core.Domain;
using Moq;

namespace KvFnB.Shared.Tests
{
    /// <summary>
    /// Unit tests for DomainEventDispatcher
    /// </summary>
    public class DomainEventDispatcherTests
    {
        private readonly Mock<IServiceProvider> _mockServiceProvider;
        private readonly Mock<ILogger> _mockLogger;
        private readonly DomainEventDispatcher _dispatcher;

        public DomainEventDispatcherTests()
        {
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLogger = new Mock<ILogger>();
            _dispatcher = new DomainEventDispatcher(_mockServiceProvider.Object, _mockLogger.Object);
        }

        [Fact]
        public void Constructor_ShouldThrowArgumentNullException_WhenServiceProviderIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new DomainEventDispatcher(null!, _mockLogger.Object));
        }

        [Fact]
        public void Constructor_ShouldThrowArgumentNullException_WhenLoggerIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new DomainEventDispatcher(_mockServiceProvider.Object, null!));
        }

        [Fact]
        public async Task DispatchAsync_ShouldLogWarning_WhenDomainEventIsNull()
        {
            // Act
            await _dispatcher.DispatchAsync(null!);

            // Assert
            _mockLogger.Verify(
                x => x.Warning("Attempted to dispatch null domain event"),
                Times.Once);
        }

        [Fact]
        public async Task DispatchAsync_ShouldLogInformation_WhenDispatchingEvent()
        {
            // Arrange
            var testEvent = new TestDomainEvent();
            var handlerType = typeof(IDomainEventHandler<TestDomainEvent>);
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(handlerType);

            _mockServiceProvider.Setup(sp => sp.GetService(handlerType))
                .Returns((object?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(enumerableType))
                .Returns(new List<IDomainEventHandler<TestDomainEvent>>());

            // Act
            await _dispatcher.DispatchAsync(testEvent);

            // Assert
            _mockLogger.Verify(
                x => x.Information(It.Is<string>(s => s.Contains("Dispatching domain event of type TestDomainEvent"))),
                Times.Once);
        }

        [Fact]
        public async Task DispatchAsync_ShouldLogDebug_WhenNoHandlersFound()
        {
            // Arrange
            var testEvent = new TestDomainEvent();
            var handlerType = typeof(IDomainEventHandler<TestDomainEvent>);
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(handlerType);

            _mockServiceProvider.Setup(sp => sp.GetService(handlerType))
                .Returns((object?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(enumerableType))
                .Returns(new List<IDomainEventHandler<TestDomainEvent>>());

            // Act
            await _dispatcher.DispatchAsync(testEvent);

            // Assert
            _mockLogger.Verify(
                x => x.Debug(It.Is<string>(s => s.Contains("No handlers found for domain event type TestDomainEvent"))),
                Times.Once);
        }

        [Fact]
        public async Task DispatchAsync_ShouldCallHandlers_WhenHandlersExist()
        {
            // Arrange
            var testEvent = new TestDomainEvent();
            var mockHandler1 = new Mock<TestDomainEventHandler>();
            var mockHandler2 = new Mock<TestDomainEventHandler>();

            var handlers = new List<IDomainEventHandler<TestDomainEvent>> { mockHandler1.Object, mockHandler2.Object };
            var handlerType = typeof(IDomainEventHandler<TestDomainEvent>);
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(handlerType);

            _mockServiceProvider.Setup(sp => sp.GetService(handlerType))
                .Returns((object?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(enumerableType))
                .Returns(handlers);

            // Act
            await _dispatcher.DispatchAsync(testEvent);

            // Assert
            mockHandler1.Verify(h => h.Handle(testEvent, It.IsAny<CancellationToken>()), Times.Once);
            mockHandler2.Verify(h => h.Handle(testEvent, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task DispatchAsync_ShouldLogError_WhenHandlerThrowsException()
        {
            // Arrange
            var testEvent = new TestDomainEvent();
            var mockHandler = new Mock<TestDomainEventHandler>();
            var exception = new InvalidOperationException("Handler failed");

            mockHandler.Setup(h => h.Handle(testEvent, It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            var handlers = new List<IDomainEventHandler<TestDomainEvent>> { mockHandler.Object };
            var handlerType = typeof(IDomainEventHandler<TestDomainEvent>);
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(handlerType);

            _mockServiceProvider.Setup(sp => sp.GetService(handlerType))
                .Returns((object?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(enumerableType))
                .Returns(handlers);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _dispatcher.DispatchAsync(testEvent));

            _mockLogger.Verify(
                x => x.Error(It.Is<string>(s => s.Contains("Handler") && s.Contains("failed to handle domain event")), It.IsAny<Exception>()),
                Times.Once);
        }

        [Fact]
        public async Task DispatchEventsAsync_ShouldLogDebug_WhenNoEventsToDispatch()
        {
            // Arrange
            var mockAggregateRoot = new Mock<IAggregateRoot>();
            mockAggregateRoot.Setup(ar => ar.DomainEvents)
                .Returns(new List<DomainEvent>().AsReadOnly());

            // Act
            await _dispatcher.DispatchEventsAsync(mockAggregateRoot.Object);

            // Assert
            _mockLogger.Verify(
                x => x.Debug("No domain events to dispatch for aggregate root"),
                Times.Once);
        }

        [Fact]
        public async Task DispatchEventsAsync_ShouldHandleNullAggregateRoot()
        {
            // Act
            await _dispatcher.DispatchEventsAsync(null!);

            // Assert
            _mockLogger.Verify(
                x => x.Debug("No domain events to dispatch for aggregate root"),
                Times.Once);
        }

        [Fact]
        public async Task DispatchEventsAsync_ShouldDispatchAllEvents_AndClearEvents()
        {
            // Arrange
            var event1 = new TestDomainEvent();
            var event2 = new TestDomainEvent();
            var events = new List<DomainEvent> { event1, event2 };

            var mockAggregateRoot = new Mock<IAggregateRoot>();
            mockAggregateRoot.Setup(ar => ar.DomainEvents)
                .Returns(events.AsReadOnly());

            var mockHandler = new Mock<TestDomainEventHandler>();
            var handlers = new List<IDomainEventHandler<TestDomainEvent>> { mockHandler.Object };
            var handlerType = typeof(IDomainEventHandler<TestDomainEvent>);
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(handlerType);

            _mockServiceProvider.Setup(sp => sp.GetService(handlerType))
                .Returns((object?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(enumerableType))
                .Returns(handlers);

            // Act
            await _dispatcher.DispatchEventsAsync(mockAggregateRoot.Object);

            // Assert
            mockHandler.Verify(h => h.Handle(event1, It.IsAny<CancellationToken>()), Times.Once);
            mockHandler.Verify(h => h.Handle(event2, It.IsAny<CancellationToken>()), Times.Once);
            mockAggregateRoot.Verify(ar => ar.ClearDomainEvents(), Times.Once);

            _mockLogger.Verify(
                x => x.Information("Successfully dispatched all domain events and cleared aggregate root events"),
                Times.Once);
        }

        [Fact]
        public async Task DispatchEventsAsync_ShouldLogEventCount()
        {
            // Arrange
            var events = new List<DomainEvent> { new TestDomainEvent(), new TestDomainEvent() };
            var mockAggregateRoot = new Mock<IAggregateRoot>();
            mockAggregateRoot.Setup(ar => ar.DomainEvents)
                .Returns(events.AsReadOnly());

            var handlerType = typeof(IDomainEventHandler<TestDomainEvent>);
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(handlerType);

            _mockServiceProvider.Setup(sp => sp.GetService(handlerType))
                .Returns((object?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(enumerableType))
                .Returns(new List<IDomainEventHandler<TestDomainEvent>>());

            // Act
            await _dispatcher.DispatchEventsAsync(mockAggregateRoot.Object);

            // Assert
            _mockLogger.Verify(
                x => x.Information(It.Is<string>(s => s.Contains("Dispatching 2 domain events from aggregate root"))),
                Times.Once);
        }

        [Fact]
        public async Task DispatchEventsAsync_ShouldThrowException_WhenDispatchFails()
        {
            // Arrange
            var testEvent = new TestDomainEvent();
            var events = new List<DomainEvent> { testEvent };

            var mockAggregateRoot = new Mock<IAggregateRoot>();
            mockAggregateRoot.Setup(ar => ar.DomainEvents)
                .Returns(events.AsReadOnly());

            var mockHandler = new Mock<TestDomainEventHandler>();
            mockHandler.Setup(h => h.Handle(testEvent, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Handler failed"));

            var handlers = new List<IDomainEventHandler<TestDomainEvent>> { mockHandler.Object };
            var handlerType = typeof(IDomainEventHandler<TestDomainEvent>);
            var enumerableType = typeof(IEnumerable<>).MakeGenericType(handlerType);

            _mockServiceProvider.Setup(sp => sp.GetService(handlerType))
                .Returns((object?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(enumerableType))
                .Returns(handlers);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _dispatcher.DispatchEventsAsync(mockAggregateRoot.Object));
        }

        /// <summary>
        /// Test domain event for testing purposes
        /// </summary>
        public class TestDomainEvent : DomainEvent
        {
            public string TestProperty { get; set; } = "Test Value";
        }

        /// <summary>
        /// Test domain event handler for testing purposes
        /// </summary>
        public class TestDomainEventHandler : IDomainEventHandler<TestDomainEvent>
        {
            public virtual Task Handle(TestDomainEvent domainEvent, CancellationToken cancellationToken = default)
            {
                return Task.CompletedTask;
            }
        }
    }
} 