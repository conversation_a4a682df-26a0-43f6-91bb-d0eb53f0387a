using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Core.Constants;
using KvFnB.Core.Domain.Services;
using KvFnB.Core.Validation;
using KvFnB.Modules.Customer.Application.UseCases.CustomerUseCase.CreateCustomer;
using KvFnB.Modules.Customer.Domain.Repositories;
using KvFnB.Shared.AuditTrailMessagePublisher;
using KvFnB.Shared.MultiTenancy;
using Moq;
using KvFnB.Modules.Customer.Application.Abstractions;
using Confluent.Kafka;

namespace KvFnB.Modules.Customer.Tests.Application.UseCases.CustomerUseCase.CreateCustomer;

public class CreateCustomerUseCaseTests
{
    private readonly Mock<IValidator<CreateCustomerRequest>> _validatorMock;
    private readonly Mock<ICustomerRepository> _repositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<AutoMapper.IMapper> _mapperMock;
    private readonly Mock<ITenantProvider> _tenantProviderMock;
    private readonly Mock<IAuthUser> _authUserMock;
    private readonly Mock<ILogger> _loggerMock;
    private readonly Mock<ICodeGenerationFromTbService> _codeGenerationServiceMock;
    private readonly Mock<IAuditTrailService> _auditTrailServiceMock;
    private readonly TenantConfiguration _tenantConfiguration;
    private readonly Mock<ICustomerGroupRepository> _customerGroupRepositoryMock;
    private readonly Mock<IAddressDomainService> _addressDomainServiceMock;
    private readonly Mock<IKafkaCommandDispatcherProducer> _commandDispatcherMock;
    private readonly Mock<IKafkaCommandDispatcherProducerConfiguration> _kafkaCommandDispatcherProducerConfigurationMock;
    private readonly CreateCustomerUseCase _useCase;

    public CreateCustomerUseCaseTests()
    {
        _validatorMock = new Mock<IValidator<CreateCustomerRequest>>();
        _repositoryMock = new Mock<ICustomerRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _mapperMock = new Mock<AutoMapper.IMapper>();
        _tenantProviderMock = new Mock<ITenantProvider>();
        _authUserMock = new Mock<IAuthUser>();
        _loggerMock = new Mock<ILogger>();
        _codeGenerationServiceMock = new Mock<ICodeGenerationFromTbService>();
        _auditTrailServiceMock = new Mock<IAuditTrailService>();
        _tenantConfiguration = new TenantConfiguration();
        _customerGroupRepositoryMock = new Mock<ICustomerGroupRepository>();
        _addressDomainServiceMock = new Mock<IAddressDomainService>();
        _commandDispatcherMock = new Mock<IKafkaCommandDispatcherProducer>();
        _kafkaCommandDispatcherProducerConfigurationMock = new Mock<IKafkaCommandDispatcherProducerConfiguration>();
        _useCase = new CreateCustomerUseCase(
            _validatorMock.Object,
            _repositoryMock.Object,
            _unitOfWorkMock.Object,
            _mapperMock.Object,
            _tenantProviderMock.Object,
            _authUserMock.Object,
            _loggerMock.Object,
            _codeGenerationServiceMock.Object,
            _auditTrailServiceMock.Object,
            _tenantConfiguration,
            _customerGroupRepositoryMock.Object,
            _addressDomainServiceMock.Object,
            _commandDispatcherMock.Object,
            _kafkaCommandDispatcherProducerConfigurationMock.Object);
    }

    [Fact]
    public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest();
        var validationResult = new ValidationResult(false, new List<string> { "Error message" });
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(validationResult.Errors, result.ValidationErrors);
        
        // Verify that further logic is not executed
        _repositoryMock.Verify(r => r.IsCodeUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        _repositoryMock.Verify(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        _auditTrailServiceMock.Verify(a => a.AddLog(It.IsAny<AuditTrailLog>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenCodeNotUnique_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest { Code = "DUPLICATE" };
        var validationResult = new ValidationResult(false, new List<string> { "Customer with code 'DUPLICATE' already exists" });
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
        _repositoryMock.Setup(r => r.IsCodeUniqueAsync(request.Code, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains($"Customer with code '{request.Code}' already exists", result.ValidationErrors!.First());
        
        // Verify that further logic is not executed
        _repositoryMock.Verify(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        _auditTrailServiceMock.Verify(a => a.AddLog(It.IsAny<AuditTrailLog>()), Times.Never);
    }


    [Fact]
    public async Task ExecuteAsync_WhenCodeIsEmpty_ShouldGenerateCode()
    {
        // Arrange
        var request = new CreateCustomerRequest 
        { 
            Code = string.Empty, 
            Name = "Test Customer",
            Email = "<EMAIL>"
        };
        var customer = new Domain.Models.Customer { Id = 1, Name = "Test Customer" };
        var customerAddAsync = new Domain.Models.Customer { Id = 1, Name = "Test Customer", Code = "KH000001" };
        var expectedResponse = new CreateCustomerResponse { Id = 1, Code = "KH000001", Name = "Test Customer" };
        var validationResult = ValidationResult.Success();
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
        _mapperMock.Setup(m => m.Map<Domain.Models.Customer>(request)).Returns(customer);
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
        _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
        _authUserMock.Setup(a => a.Id).Returns(123L);
        _authUserMock.Setup(a => a.UserName).Returns("testuser");
        _codeGenerationServiceMock.Setup(c => c.GenerateNextCodeAsync(
                It.IsAny<Type>(), 
                It.IsAny<string>(), 
                It.IsAny<int>()))
            .ReturnsAsync("KH000001");

        _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(customerAddAsync);
            
        _mapperMock.Setup(m => m.Map<CreateCustomerResponse>(It.IsAny<Domain.Models.Customer>())).Returns(expectedResponse);
        _auditTrailServiceMock.Setup(a => a.AddLog(It.IsAny<AuditTrailLog>())).ReturnsAsync(true);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(expectedResponse, result.Value);
        
        // Verify code generation was called
        _codeGenerationServiceMock.Verify(c => c.GenerateNextCodeAsync(
                It.IsAny<Type>(), 
                It.IsAny<string>(), 
                It.IsAny<int>()), Times.Once);
        
        // Verify that audit log was created
        _auditTrailServiceMock.Verify(a => a.AddLog(It.Is<AuditTrailLog>(log => 
            log.RetailerId == 1 && 
            log.BranchId == 1 && 
            log.UserId == 123L &&
            log.UserName == "testuser"
        )), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WhenSuccessful_ShouldReturnSuccessAndCreateAuditLog()
    {
        // Arrange
        var request = new CreateCustomerRequest 
        { 
            Code = "CUST123", 
            Name = "Test Customer",
            Email = "<EMAIL>"
        };
        var customer = new Domain.Models.Customer { Id = 1, Code = "CUST123", Name = "Test Customer" };
        var expectedResponse = new CreateCustomerResponse { Id = 1, Code = "CUST123", Name = "Test Customer" };
        var validationResult = ValidationResult.Success();
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
        _repositoryMock.Setup(r => r.IsCodeUniqueAsync(request.Code, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mapperMock.Setup(m => m.Map<Domain.Models.Customer>(request)).Returns(customer);
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
        _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
        _authUserMock.Setup(a => a.Id).Returns(123L);
        _authUserMock.Setup(a => a.UserName).Returns("testuser");
        _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(customer);
        _mapperMock.Setup(m => m.Map<CreateCustomerResponse>(customer)).Returns(expectedResponse);
        _auditTrailServiceMock.Setup(a => a.AddLog(It.IsAny<AuditTrailLog>())).ReturnsAsync(true);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(expectedResponse, result.Value);
        
        // Verify that repository and unit of work methods are called
        _repositoryMock.Verify(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        
        // Verify that the customer was properly prepared before saving
        _repositoryMock.Verify(r => r.AddAsync(
            It.Is<Domain.Models.Customer>(c => 
                c.Id == 1 &&
                c.Code == "CUST123"),
            It.IsAny<CancellationToken>()),
            Times.Once);
            
        // Verify that audit log was created
        _auditTrailServiceMock.Verify(a => a.AddLog(It.Is<AuditTrailLog>(log => 
            log.FunctionId == (int)KvFnB.Core.Enums.FunctionType.Customer && 
            log.Action == (int)KvFnB.Core.Enums.AuditAction.Create &&
            log.UserName == "testuser" &&
            log.RefId == 1
        )), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest { Code = "CUST123", Name = "Test Customer" };
        var customer = new Domain.Models.Customer();
        var validationResult = ValidationResult.Success();
        var exception = new Exception("Test exception");
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
        _repositoryMock.Setup(r => r.IsCodeUniqueAsync(request.Code, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mapperMock.Setup(m => m.Map<Domain.Models.Customer>(request)).Returns(customer);
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
        _authUserMock.Setup(a => a.Id).Returns(123L);
        _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(ErrorMessages.InternalServerError, result.ErrorMessage);
        
        // Verify that logger was called with the exception
        _loggerMock.Verify(l => l.Error(It.IsAny<string>(), exception), Times.Once);
        
        // Verify audit log was not created
        _auditTrailServiceMock.Verify(a => a.AddLog(It.IsAny<AuditTrailLog>()), Times.Never);
    }
    
    [Fact]
    public async Task ExecuteAsync_WhenAuditLogFails_ShouldStillReturnSuccess()
    {
        // Arrange
        var request = new CreateCustomerRequest 
        { 
            Code = "CUST123", 
            Name = "Test Customer" 
        };
        var customer = new Domain.Models.Customer { Id = 1, Code = "CUST123", Name = "Test Customer" };
        var expectedResponse = new CreateCustomerResponse { Id = 1, Code = "CUST123", Name = "Test Customer" };
        var validationResult = ValidationResult.Success();
        var auditException = new Exception("Audit log exception");
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
        _repositoryMock.Setup(r => r.IsCodeUniqueAsync(request.Code, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mapperMock.Setup(m => m.Map<Domain.Models.Customer>(request)).Returns(customer);
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
        _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
        _authUserMock.Setup(a => a.Id).Returns(123L);
        _authUserMock.Setup(a => a.UserName).Returns("testuser");
        _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(customer);
        _mapperMock.Setup(m => m.Map<CreateCustomerResponse>(customer)).Returns(expectedResponse);
        _auditTrailServiceMock.Setup(a => a.AddLog(It.IsAny<AuditTrailLog>())).ThrowsAsync(auditException);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(expectedResponse, result.Value);
        
        // Verify that the exception from audit logging was caught and logged
        _loggerMock.Verify(l => l.Error(It.IsAny<string>(), auditException), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WhenContactNumberNotUnique_ShouldReturnFailure()
    {
        // Arrange
        var request = new CreateCustomerRequest 
        { 
            Code = "CUST123", 
            Name = "Test Customer",
            ContactNumber = "**********"
        };
        var validationResult = ValidationResult.Success();
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
        _repositoryMock.Setup(r => r.IsCodeUniqueAsync(request.Code, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _repositoryMock.Setup(r => r.HasCustomerByContactNumberAsync(request.ContactNumber!, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _useCase.ExecuteAsync(request);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains($"Customer with contact number already exists", result.ErrorMessage!);
        
        // Verify that further logic is not executed
        _repositoryMock.Verify(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Never);
        _auditTrailServiceMock.Verify(a => a.AddLog(It.IsAny<AuditTrailLog>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteAsync_WhenDuplicateContactNumberAllowed_ShouldSucceed()
    {
        // Arrange
        var request = new CreateCustomerRequest 
        { 
            Code = "CUST123", 
            Name = "Test Customer",
            ContactNumber = "**********"
        };
        var customer = new Domain.Models.Customer { Id = 1, Code = "CUST123", Name = "Test Customer", ContactNumber = "**********" };
        var expectedResponse = new CreateCustomerResponse { Id = 1, Code = "CUST123", Name = "Test Customer", ContactNumber = "**********" };
        var validationResult = ValidationResult.Success();
        
        // Create a new test instance with AllowDuplicateCustomerPhone set to true
        var tenantConfigWithAllowDuplicate = new TenantConfiguration { AllowDuplicateCustomerPhone = true };
        var useCaseWithAllowDuplicate = new CreateCustomerUseCase(
            _validatorMock.Object,
            _repositoryMock.Object,
            _unitOfWorkMock.Object,
            _mapperMock.Object,
            _tenantProviderMock.Object,
            _authUserMock.Object,
            _loggerMock.Object,
            _codeGenerationServiceMock.Object,
            _auditTrailServiceMock.Object,
            tenantConfigWithAllowDuplicate,
            _customerGroupRepositoryMock.Object,
            _addressDomainServiceMock.Object,
            _commandDispatcherMock.Object,
            _kafkaCommandDispatcherProducerConfigurationMock.Object);
        
        _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
        _repositoryMock.Setup(r => r.IsCodeUniqueAsync(request.Code, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _repositoryMock.Setup(r => r.HasCustomerByContactNumberAsync(request.ContactNumber!, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true); // Contact number exists but it should be allowed
        _mapperMock.Setup(m => m.Map<Domain.Models.Customer>(request)).Returns(customer);
        _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(1);
        _tenantProviderMock.Setup(t => t.GetBranchId()).Returns(1);
        _authUserMock.Setup(a => a.Id).Returns(123L);
        _authUserMock.Setup(a => a.UserName).Returns("testuser");
        _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(customer);
        _mapperMock.Setup(m => m.Map<CreateCustomerResponse>(customer)).Returns(expectedResponse);
        _auditTrailServiceMock.Setup(a => a.AddLog(It.IsAny<AuditTrailLog>())).ReturnsAsync(true);
        _tenantProviderMock.Setup(t => t.GetRetailerCode()).Returns("RETAIL001");
        _tenantProviderMock.Setup(t => t.GetShardId()).Returns(1);
        _kafkaCommandDispatcherProducerConfigurationMock.Setup(k => k.Topic).Returns("digital-marketing-topic");
        _commandDispatcherMock.Setup(c => c.ProducerAsync(
            It.IsAny<string>(), 
            It.IsAny<Message<string, string>>(), 
            It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await useCaseWithAllowDuplicate.ExecuteAsync(request);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(expectedResponse, result.Value);
        
        // Verify that repository and unit of work methods are called
        _repositoryMock.Verify(r => r.AddAsync(It.IsAny<Domain.Models.Customer>(), It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(u => u.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
} 