﻿using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Confluent.Kafka;
using KvFnB.Core.Abstractions;
using KvFnB.Core.Authentication;
using KvFnB.Modules.DataManagement.Application.Abstractions;
using KvFnB.Modules.DataManagement.Application.Dtos;
using KvFnB.Modules.DataManagement.Application.Tests.TestHelpers;
using KvFnB.Modules.DataManagement.Application.UseCases.ScanScheduledRequestsUseCase;
using KvFnB.Modules.DataManagement.Domain.Entities;
using KvFnB.Modules.DataManagement.Domain.Enums;
using KvFnB.Modules.DataManagement.Domain.Models;
using KvFnB.Modules.DataManagement.Domain.Repositories;
using KvFnB.Modules.DataManagement.Domain.Services;
using KvFnB.Shared.Persistence.ShardingDb;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;

namespace KvFnB.Modules.DataManagement.Tests.Application.UseCases.ScanScheduledRequestsUseCaseTests
{
    /// <summary>
    /// Test class for ScanScheduledRequestsUseCase following BDD-style naming and comprehensive coverage
    /// </summary>
    public class ScanScheduledRequestsUseCaseTests
    {
        #region Test Setup and Dependencies
        
        private readonly Mock<IDataManagementQueryService> _queryServiceMock;
        private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock;
        private readonly IConfiguration _configuration;
        private readonly Mock<IServiceScope> _serviceScopeMock;
        private readonly Mock<IServiceProvider> _serviceProviderMock;
        private readonly ScanScheduledRequestsUseCase _useCase;
        private readonly Mock<IDeleteDataRequestRepository> _deleteDataRequestRepositoryMock;
        private readonly Mock<IProcessingDataJobRepository> _processingDataJobRepositoryMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly Mock<IInitUserInfo> _authUserMock;
        private readonly Mock<ShardingDbContext> _dbContextMock;
        private readonly Mock<IManagedProducer> _managedProducerMock;
        private readonly Mock<IKafkaProducerConfiguration> _kafkaProducerConfigMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly CancellationToken _cancellationToken = CancellationToken.None;

        public ScanScheduledRequestsUseCaseTests()
        {
            // Initialize all mocks
            _queryServiceMock = new Mock<IDataManagementQueryService>();
            _serviceScopeFactoryMock = new Mock<IServiceScopeFactory>();
            _serviceScopeMock = new Mock<IServiceScope>();
            _serviceProviderMock = new Mock<IServiceProvider>();
            _deleteDataRequestRepositoryMock = new Mock<IDeleteDataRequestRepository>();
            _processingDataJobRepositoryMock = new Mock<IProcessingDataJobRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _authUserMock = new Mock<IInitUserInfo>();
            _managedProducerMock = new Mock<IManagedProducer>();
            _kafkaProducerConfigMock = new Mock<IKafkaProducerConfiguration>();
            _loggerMock = new Mock<ILogger>();
            
            // Use the helper to create a properly mocked DbContext
            _dbContextMock = DbContextMockHelper.CreateDbContextMock();

            // Create a ScheduleUserInfo for testing
            var scheduleUserInfo = DbContextMockHelper.CreateScheduleUserInfo();
            _authUserMock.Setup(a => a.Id).Returns(scheduleUserInfo.Id);
            _authUserMock.Setup(a => a.TenantId).Returns(scheduleUserInfo.TenantId);
            _authUserMock.Setup(a => a.UserName).Returns(scheduleUserInfo.UserName);
            _authUserMock.Setup(a => a.SessionId).Returns(scheduleUserInfo.SessionId);
            _authUserMock.Setup(a => a.IsAdmin).Returns(scheduleUserInfo.IsAdmin);

            // Set up the service scope factory to return our service scope
            _serviceScopeFactoryMock.Setup(x => x.CreateScope()).Returns(_serviceScopeMock.Object);
            _serviceScopeMock.Setup(x => x.ServiceProvider).Returns(_serviceProviderMock.Object);

            // Set up the service provider to return our mocked services
            _serviceProviderMock.Setup(x => x.GetService(typeof(ITenantProvider))).Returns(_tenantProviderMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IInitUserInfo))).Returns(_authUserMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(ShardingDbContext))).Returns(_dbContextMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IDeleteDataRequestRepository))).Returns(_deleteDataRequestRepositoryMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IProcessingDataJobRepository))).Returns(_processingDataJobRepositoryMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(IUnitOfWork))).Returns(_unitOfWorkMock.Object);
            _serviceProviderMock.Setup(x => x.GetService(typeof(ILogger))).Returns(_loggerMock.Object);

            // Create a real configuration instance with an in-memory provider
            var configValues = new Dictionary<string, string?>
            {
                { "DataDeletion:MainTransactionTables:0", "Invoice" },
                { "DataDeletion:MainTransactionTables:1", "Payment" },
                { "DataDeletion:MainTransactionTables:2", "PurchaseOrder" },
                { "DataDeletion:AdjustmentTables:0", "AdjustmentCustomer" },
                { "DataDeletion:AdjustmentTables:1", "AdjustmentSupplier" },
                { "DataDeletion:AdjustmentTables:2", "AdjustmentPartnerDelivery" },
                { "DataDeletion:AdjustmentTables:3", "OtherAdjustment" }
            };

            _configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();
                
            // Setup Kafka producer config
            _kafkaProducerConfigMock.Setup(k => k.Topic).Returns("data-processing-topic");

            _useCase = new ScanScheduledRequestsUseCase(
                _queryServiceMock.Object,
                _serviceScopeFactoryMock.Object,
                _configuration,
                _managedProducerMock.Object,
                _kafkaProducerConfigMock.Object);
        }
        
        #endregion

        #region Happy Path Tests

        [Fact]
        public async Task Given_NoDueRequests_When_ExecuteAsync_Then_ShouldReturnEmptySuccessResponse()
        {
            // Arrange
            var request = CreateTestRequest();
            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest>());

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Value);
            Assert.Equal(0, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
            
            // Verify query service was called with correct parameters
            _queryServiceMock.Verify(
                q => q.QueryPlainTextAsync<DeleteDataRequest>(
                    It.Is<string>(sql => sql.Contains("SELECT RetailerId as TenantId, CreatedDate as CreatedAt, * FROM DeleteDataRequest") &&
                                        sql.Contains("WHERE Status = @Status AND") &&
                                        sql.Contains("Type = @Type AND") &&
                                        sql.Contains("ExecuteDate < @CurrentTime")),
                    It.Is<object>(p => HasCorrectQueryParameters(p, request.CurrentTime)),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task Given_SingleDueRequestWithScheduleConfig_When_ExecuteAsync_Then_ShouldProcessAndUpdateNextExecutionDate()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithScheduleConfig();
            var processingJob = CreateSampleProcessingJob(deleteRequest);

            SetupSuccessfulScenario(deleteRequest, processingJob);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);

            // Verify the request processing workflow
            VerifySuccessfulProcessingWorkflow(deleteRequest, request);
        }

        [Fact]
        public async Task Given_RequestWithoutScheduleConfig_When_ExecuteAsync_Then_ShouldMarkAsCompleted()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithoutScheduleConfig();
            var processingJob = CreateSampleProcessingJob(deleteRequest);

            SetupSuccessfulScenario(deleteRequest, processingJob);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);

            // Verify that the request was marked as completed
            _deleteDataRequestRepositoryMock.Verify(r => r.UpdateAsync(
                It.Is<DeleteDataRequest>(dr =>
                    dr.Id == deleteRequest.Id &&
                    dr.Status == (int)DeleteDataRequestStatus.Completed),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Given_RequestWithMultipleBranches_When_ExecuteAsync_Then_ShouldCreateDeleteDataDetailsForAllBranchesAndTables()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithMultipleBranches();
            var processingJob = CreateSampleProcessingJob(deleteRequest);

            SetupSuccessfulScenario(deleteRequest, processingJob);

            DeleteDataRequest capturedRequest = null;
            _deleteDataRequestRepositoryMock.Setup(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()))
                .Callback<DeleteDataRequest, CancellationToken>((req, _) => capturedRequest = req);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
            
            // Verify delete data details were created correctly
            Assert.NotNull(capturedRequest);
            VerifyDeleteDataDetailsForMultipleBranches(capturedRequest);
            
            // Verify Kafka messages were sent for all details
            _managedProducerMock.Verify(m => m.ProducerAsync(
                It.IsAny<string>(),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()), Times.AtLeast(10));
        }

        #endregion

        #region Daily Schedule Tests

        [Fact]
        public async Task Given_DailyScheduleRequest_When_ExecuteAsync_Then_ShouldCalculateCorrectNextExecutionDate()
        {
            // Arrange
            var currentTime = new DateTime(2023, 1, 1, 10, 0, 0, DateTimeKind.Utc);
            var request = new ScanScheduledRequestsRequest(currentTime, 2);
            var deleteRequest = CreateSampleDeleteDataRequestWithDailySchedule(currentTime);
            var processingJob = CreateSampleProcessingJob(deleteRequest);

            SetupSuccessfulScenario(deleteRequest, processingJob);

            DeleteDataRequest capturedRequest = null;
            _deleteDataRequestRepositoryMock.Setup(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()))
                .Callback<DeleteDataRequest, CancellationToken>((req, _) => capturedRequest = req);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);

            // Verify next execution date calculation
            Assert.NotNull(capturedRequest);
            var expectedNextExecution = new DateTime(2023, 1, 2, 8, 0, 0, DateTimeKind.Utc);
            Assert.Equal(expectedNextExecution, capturedRequest.ExecuteDate);

            // Verify filter condition was updated
            var filterCondition = JsonSerializer.Deserialize<FilterCondition>(capturedRequest.FilterCondition);
            Assert.Equal(expectedNextExecution, filterCondition.ToDate);
        }

        #endregion

        #region Adjustment Tables Tests

        [Fact]
        public async Task Given_RequestWithAdjustmentTables_When_ExecuteAsync_Then_ShouldHandleSpecialAdjustmentTypesCorrectly()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithMultipleBranches(); // 3 branches: "101,102,103"
            var processingJob = CreateSampleProcessingJob(deleteRequest);

            SetupSuccessfulScenario(deleteRequest, processingJob);

            DeleteDataRequest capturedRequest = null;
            _deleteDataRequestRepositoryMock.Setup(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()))
                .Callback<DeleteDataRequest, CancellationToken>((req, _) => capturedRequest = req);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);
            
            // Verify captured request details
            Assert.NotNull(capturedRequest);
            
            // Special adjustment types (AdjustmentCustomer, AdjustmentSupplier, AdjustmentPartnerDelivery) 
            // should have only one entry regardless of branch count
            var specialAdjustmentDetails = capturedRequest.DeleteDataDetails
                .Where(d => new[] { "AdjustmentCustomer", "AdjustmentSupplier", "AdjustmentPartnerDelivery" }.Contains(d.Type))
                .ToList();
                
            // Regular adjustment types (OtherAdjustment) should have one entry per branch
            var regularAdjustmentDetails = capturedRequest.DeleteDataDetails
                .Where(d => d.Type == "OtherAdjustment")
                .ToList();
                
            Assert.Equal(3, specialAdjustmentDetails.Count); // One for each special type
            Assert.Equal(3, regularAdjustmentDetails.Count); // One for each branch
        }

        #endregion

        #region Error Scenarios Tests

        [Fact]
        public async Task Given_TenantProviderThrowsException_When_ExecuteAsync_Then_ShouldPropagateException()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithScheduleConfig();
            var processingJob = CreateSampleProcessingJob(deleteRequest);

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            _processingDataJobRepositoryMock.Setup(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(processingJob);

            // Set up the tenant provider to throw exception during GetShardId
            _tenantProviderMock.Setup(t => t.GetShardId())
                .Throws(new Exception("Tenant not found"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(async () =>
                await _useCase.ExecuteAsync(request, _cancellationToken));

            // Verify that initialization was attempted before the exception
            _tenantProviderMock.Verify(t => t.InitTenantInfo(
                request.GroupId,
                deleteRequest.TenantId), Times.Once);

            // Verify that the job was created before the exception
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Given_RequestWithEmptyBranchIds_When_ExecuteAsync_Then_ShouldSkipProcessingButStillCountAsProcessed()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithScheduleConfig();
            deleteRequest.BranchIds = string.Empty; // Empty branch IDs

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount); // Still counted as processed
            Assert.Empty(result.Value.Errors);

            // Verify that no repository operations were performed
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Never);
            
            _deleteDataRequestRepositoryMock.Verify(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()), Times.Never);
                
            _managedProducerMock.Verify(m => m.ProducerAsync(
                It.IsAny<string>(),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Given_RequestWithNullBranchIds_When_ExecuteAsync_Then_ShouldSkipProcessing()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithScheduleConfig();
            deleteRequest.BranchIds = null; // Null branch IDs

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(1, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);

            // Verify that no repository operations were performed
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Given_KafkaProducerThrowsException_When_ExecuteAsync_Then_ShouldPropagateException()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest = CreateSampleDeleteDataRequestWithScheduleConfig();
            var processingJob = CreateSampleProcessingJob(deleteRequest);

            SetupSuccessfulScenario(deleteRequest, processingJob);

            // Setup Kafka producer to throw exception
            _managedProducerMock.Setup(m => m.ProducerAsync(
                It.IsAny<string>(),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Kafka connection failed"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(async () =>
                await _useCase.ExecuteAsync(request, _cancellationToken));

            // Verify that processing was attempted before Kafka failure
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Once);
                
            _deleteDataRequestRepositoryMock.Verify(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Multiple Requests Tests

        [Fact]
        public async Task Given_MultipleDueRequests_When_ExecuteAsync_Then_ShouldProcessAllRequests()
        {
            // Arrange
            var request = CreateTestRequest();
            var deleteRequest1 = CreateSampleDeleteDataRequestWithScheduleConfig();
            deleteRequest1.Id = 1;
            deleteRequest1.TenantId = 100;
            
            var deleteRequest2 = CreateSampleDeleteDataRequestWithScheduleConfig();
            deleteRequest2.Id = 2;
            deleteRequest2.TenantId = 200;

            var processingJob1 = CreateSampleProcessingJob(deleteRequest1);
            var processingJob2 = CreateSampleProcessingJob(deleteRequest2);

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest1, deleteRequest2 });

            _processingDataJobRepositoryMock.SetupSequence(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(processingJob1)
                .ReturnsAsync(processingJob2);

            _tenantProviderMock.Setup(t => t.GetShardId()).Returns(request.GroupId);

            // Act
            var result = await _useCase.ExecuteAsync(request, _cancellationToken);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(2, result.Value.ProcessedCount);
            Assert.Empty(result.Value.Errors);

            // Verify both requests were processed
            _authUserMock.Verify(a => a.InitUserInfo(
                deleteRequest1.CreatedBy,
                deleteRequest1.TenantId), Times.Once);
                
            _authUserMock.Verify(a => a.InitUserInfo(
                deleteRequest2.CreatedBy,
                deleteRequest2.TenantId), Times.Once);

            _tenantProviderMock.Verify(t => t.InitTenantInfo(
                request.GroupId,
                deleteRequest1.TenantId), Times.Once);
                
            _tenantProviderMock.Verify(t => t.InitTenantInfo(
                request.GroupId,
                deleteRequest2.TenantId), Times.Once);

            // Verify processing jobs were created for both
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Exactly(2));
        }

        #endregion

        #region Helper Methods

        private ScanScheduledRequestsRequest CreateTestRequest()
        {
            return new ScanScheduledRequestsRequest(DateTime.Now, 2);
        }

        private DeleteDataRequest CreateSampleDeleteDataRequestWithScheduleConfig()
        {
            var scheduleConfig = new ScheduleConfig
            {
                ScheduleType = ScheduleTypeEnum.Daily,
                ExecutionTime = "08:00:00"
            };

            var filterCondition = new FilterCondition
            {
                FromDate = DateTime.Now.AddDays(-30),
                ToDate = DateTime.Now,
                IgnoreEInvoice = true
            };

            var deleteRequest = DeleteDataRequest.Create(
                email: "<EMAIL>",
                type: DeleteDataType.FinancialOnly,
                scheduleType: DeleteDataScheduleType.Scheduled,
                branchIds: "101",
                status: DeleteDataRequestStatus.Approved,
                executeDate: DateTime.Now,
                scheduleConfigJson: JsonSerializer.Serialize(scheduleConfig),
                filterConditionJson: JsonSerializer.Serialize(filterCondition));

            deleteRequest.ScheduleConfig = JsonSerializer.Serialize(scheduleConfig);
            deleteRequest.Id = 1;
            deleteRequest.CreatedBy = 100;
            deleteRequest.TenantId = 200;

            return deleteRequest;
        }

        private DeleteDataRequest CreateSampleDeleteDataRequestWithoutScheduleConfig()
        {
            var filterCondition = new FilterCondition
            {
                FromDate = DateTime.Now.AddDays(-30),
                ToDate = DateTime.Now,
                IgnoreEInvoice = true
            };

            var deleteRequest = DeleteDataRequest.Create(
                email: "<EMAIL>",
                type: DeleteDataType.FinancialOnly,
                scheduleType: DeleteDataScheduleType.Scheduled,
                branchIds: "101",
                status: DeleteDataRequestStatus.Approved,
                executeDate: DateTime.Now,
                scheduleConfigJson: null,
                filterConditionJson: JsonSerializer.Serialize(filterCondition));

            deleteRequest.ScheduleConfig = null; // No schedule config
            deleteRequest.Id = 1;
            deleteRequest.CreatedBy = 100;
            deleteRequest.TenantId = 200;

            return deleteRequest;
        }

        private DeleteDataRequest CreateSampleDeleteDataRequestWithMultipleBranches()
        {
            var deleteRequest = CreateSampleDeleteDataRequestWithScheduleConfig();
            deleteRequest.BranchIds = "101,102,103"; // Multiple branches
            return deleteRequest;
        }

        private DeleteDataRequest CreateSampleDeleteDataRequestWithDailySchedule(DateTime executeDate)
        {
            var scheduleConfig = new ScheduleConfig
            {
                ScheduleType = ScheduleTypeEnum.Daily,
                ExecutionTime = "08:00:00"
            };

            var filterCondition = new FilterCondition
            {
                FromDate = executeDate.AddDays(-30),
                ToDate = executeDate,
                IgnoreEInvoice = true
            };

            var deleteRequest = DeleteDataRequest.Create(
                email: "<EMAIL>",
                type: DeleteDataType.FinancialOnly,
                scheduleType: DeleteDataScheduleType.Scheduled,
                branchIds: "101",
                status: DeleteDataRequestStatus.Approved,
                executeDate: executeDate,
                scheduleConfigJson: JsonSerializer.Serialize(scheduleConfig),
                filterConditionJson: JsonSerializer.Serialize(filterCondition));

            deleteRequest.ScheduleConfig = JsonSerializer.Serialize(scheduleConfig);
            deleteRequest.ExecuteDate = executeDate;
            deleteRequest.Id = 1;
            deleteRequest.CreatedBy = 100;
            deleteRequest.TenantId = 200;

            return deleteRequest;
        }

        private ProcessingDataJob CreateSampleProcessingJob(DeleteDataRequest deleteRequest)
        {
            return new ProcessingDataJob
            {
                Id = 1,
                RequestId = deleteRequest.Id,
                Status = (byte)ProcessingDataJobStatus.Pending,
                CreatedAt = DateTime.Now,
                TenantId = deleteRequest.TenantId
            };
        }

        private void SetupSuccessfulScenario(DeleteDataRequest deleteRequest, ProcessingDataJob processingJob)
        {
            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<DeleteDataRequest> { deleteRequest });

            _processingDataJobRepositoryMock.Setup(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(processingJob);

            _tenantProviderMock.Setup(t => t.GetShardId()).Returns(2);
        }

        private void VerifySuccessfulProcessingWorkflow(DeleteDataRequest deleteRequest, ScanScheduledRequestsRequest request)
        {
            // Verify query service was called
            _queryServiceMock.Verify(q => q.QueryPlainTextAsync<DeleteDataRequest>(
                It.IsAny<string>(),
                It.IsAny<object>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // Verify auth setup
            _authUserMock.Verify(a => a.InitUserInfo(
                deleteRequest.CreatedBy,
                deleteRequest.TenantId), Times.Once);

            // Verify tenant provider setup
            _tenantProviderMock.Verify(t => t.InitTenantInfo(
                request.GroupId,
                deleteRequest.TenantId), Times.Once);

            // Verify processing job creation
            _processingDataJobRepositoryMock.Verify(r => r.AddAsync(
                It.IsAny<ProcessingDataJob>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // Verify DeleteDataRequest update
            _deleteDataRequestRepositoryMock.Verify(r => r.UpdateAsync(
                It.IsAny<DeleteDataRequest>(),
                It.IsAny<CancellationToken>()), Times.Once);

            // Verify unit of work commits (twice: job creation and request update)
            _unitOfWorkMock.Verify(u => u.CommitAsync(
                It.IsAny<CancellationToken>()), Times.Exactly(2));

            // Verify Kafka producer calls
            _managedProducerMock.Verify(m => m.ProducerAsync(
                It.IsAny<string>(),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        private void VerifyDeleteDataDetailsForMultipleBranches(DeleteDataRequest capturedRequest)
        {
            // 3 branches × 3 main tables = 9 main table details
            var mainTableDetails = capturedRequest.DeleteDataDetails
                .Where(d => new[] { "Invoice", "Payment", "PurchaseOrder" }.Contains(d.Type))
                .ToList();
            Assert.Equal(9, mainTableDetails.Count);

            // Verify each branch has details for all main tables
            foreach (var branchId in new[] { "101", "102", "103" })
            {
                var branchDetails = mainTableDetails.Where(d => d.BranchId == long.Parse(branchId)).ToList();
                Assert.Equal(3, branchDetails.Count); // Invoice, Payment, PurchaseOrder
            }

            // Verify adjustment table details
            var adjustmentDetails = capturedRequest.DeleteDataDetails
                .Where(d => new[] { "AdjustmentCustomer", "AdjustmentSupplier", "AdjustmentPartnerDelivery", "OtherAdjustment" }.Contains(d.Type))
                .ToList();

            // Special adjustment types should have only one entry each
            var specialAdjustmentCount = adjustmentDetails
                .Where(d => new[] { "AdjustmentCustomer", "AdjustmentSupplier", "AdjustmentPartnerDelivery" }.Contains(d.Type))
                .Count();
            Assert.Equal(3, specialAdjustmentCount);

            // Regular adjustment types should have one entry per branch
            var regularAdjustmentCount = adjustmentDetails
                .Where(d => d.Type == "OtherAdjustment")
                .Count();
            Assert.Equal(3, regularAdjustmentCount);
        }

        private bool HasCorrectQueryParameters(object parameters, DateTime currentTime)
        {
            var props = parameters.GetType().GetProperties();
            var currentTimeProp = props.FirstOrDefault(p => p.Name == "CurrentTime");
            var statusProp = props.FirstOrDefault(p => p.Name == "Status");
            var typeProp = props.FirstOrDefault(p => p.Name == "Type");

            return currentTimeProp?.GetValue(parameters)?.Equals(currentTime.Date.AddDays(1)) == true &&
                   statusProp?.GetValue(parameters)?.Equals(DeleteDataRequestStatus.Approved) == true &&
                   typeProp?.GetValue(parameters)?.Equals(DeleteDataScheduleType.Scheduled) == true;
        }

        #endregion
    }
}