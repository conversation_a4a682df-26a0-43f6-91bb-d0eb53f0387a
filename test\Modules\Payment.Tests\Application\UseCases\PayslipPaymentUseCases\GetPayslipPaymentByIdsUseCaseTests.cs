using KvFnB.Core.Abstractions;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Payment.Application.UseCases.PayslipPaymentUseCases.GetPayslipPaymentByIds;
using Moq;
using Xunit;

namespace KvFnB.Modules.Payment.Tests.Application.UseCases.PayslipPaymentUseCases
{
    public class GetPayslipPaymentByIdsUseCaseTests
    {
        private readonly Mock<ILogger> _loggerMock;
        private readonly Mock<IQueryService> _queryServiceMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly GetPayslipPaymentByIdsUseCase _useCase;

        public GetPayslipPaymentByIdsUseCaseTests()
        {
            _loggerMock = new Mock<ILogger>();
            _queryServiceMock = new Mock<IQueryService>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            
            _useCase = new GetPayslipPaymentByIdsUseCase(
                _loggerMock.Object,
                _queryServiceMock.Object,
                _tenantProviderMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, 2, 3]
            };

            var retailerId = 1;
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(retailerId);

            var dynamicResults = new[]
            {
                new
                {
                    Id = 1L,
                    Code = "PAY001",
                    Amount = 1000.50m,
                    AmountOriginal = 1000.50m,
                    IsAllocation = true,
                    Method = "BANK",
                    BranchId = 1,
                    PayslipId = 100L,
                    Description = "Payment for salary",
                    EmployeeId = 10L,
                    TransDate = DateTime.Now,
                    UserId = (long?)5L,
                    Status = (byte)1,
                    AccountId = (int?)1,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1L,
                    ModifiedBy = (long?)2L,
                    ModifiedDate = (DateTime?)DateTime.Now
                },
                new
                {
                    Id = 2L,
                    Code = "PAY002",
                    Amount = 2000.75m,
                    AmountOriginal = 2000.75m,
                    IsAllocation = false,
                    Method = "CASH",
                    BranchId = 2,
                    PayslipId = 101L,
                    Description = "Bonus payment",
                    EmployeeId = 11L,
                    TransDate = DateTime.Now,
                    UserId = (long?)6L,
                    Status = (byte)1,
                    AccountId = (int?)2,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1L,
                    ModifiedBy = (long?)null,
                    ModifiedDate = (DateTime?)null
                }
            };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(dynamicResults);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Value);
            Assert.Equal(2, result.Value.Data.Count);
            Assert.Equal(2, result.Value.Total);
            
            // Verify first record
            var firstPayment = result.Value.Data.First();
            Assert.Equal(1L, firstPayment.Id);
            Assert.Equal("PAY001", firstPayment.Code);
            Assert.Equal(1000.50m, firstPayment.Amount);
            Assert.True(firstPayment.IsAllocation);
            Assert.Equal("BANK", firstPayment.Method);
            
            // Verify logging
            _loggerMock.Verify(l => l.Information(It.IsAny<string>()), Times.Exactly(2));
        }

        [Fact]
        public async Task ExecuteAsync_WhenNoRecordsFound_ShouldReturnEmptyResult()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [999, 998]
            };

            var retailerId = 1;
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(retailerId);

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new dynamic[0]);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Value);
            Assert.Empty(result.Value.Data);
            Assert.Equal(0, result.Value.Total);
        }

        [Fact]
        public async Task ExecuteAsync_WhenTenantIdIsNull_ShouldUseZeroAsRetailerId()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1]
            };

            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns((int?)null);

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new dynamic[0]);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            _loggerMock.Verify(l => l.Information(It.Is<string>(s => s.Contains("RetailerId: 0"))), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenQueryServiceThrowsException_ShouldReturnFailure()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, 2]
            };

            var retailerId = 1;
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(retailerId);

            var exception = new Exception("Database connection failed");
            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Failed to retrieve PayslipPayment records", result.Errors);
            
            // Verify error logging
            _loggerMock.Verify(l => l.Error(It.IsAny<string>(), exception), Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenQueryExecuted_ShouldUseTenantFilteringInQuery()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, 2, 3]
            };

            var retailerId = 5;
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(retailerId);

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new dynamic[0]);

            // Act
            await _useCase.ExecuteAsync(request);

            // Assert
            _queryServiceMock.Verify(q => q.QueryPlainTextAsync<dynamic>(
                It.Is<string>(sql => sql.Contains("WHERE p.RetailerId = @retailerId")),
                It.IsAny<object>()),
                Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenQueryExecuted_ShouldUseCorrectParameters()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, 2, 3]
            };

            var retailerId = 10;
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(retailerId);

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new dynamic[0]);

            // Act
            await _useCase.ExecuteAsync(request);

            // Assert
            _queryServiceMock.Verify(q => q.QueryPlainTextAsync<dynamic>(
                It.IsAny<string>(),
                It.Is<object>(param => param != null)),
                Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldMapAllFieldsCorrectly()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1]
            };

            var retailerId = 1;
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(retailerId);

            var testData = new[]
            {
                new
                {
                    Id = 123L,
                    Code = "TEST001",
                    Amount = 1500.25m,
                    AmountOriginal = 1600.00m,
                    IsAllocation = true,
                    Method = "TRANSFER",
                    BranchId = 5,
                    PayslipId = 456L,
                    Description = "Test payment description",
                    EmployeeId = 789L,
                    TransDate = new DateTime(2024, 1, 15),
                    UserId = (long?)999L,
                    Status = (byte)2,
                    AccountId = (int?)10,
                    CreatedDate = new DateTime(2024, 1, 1),
                    CreatedBy = 100L,
                    ModifiedBy = (long?)200L,
                    ModifiedDate = (DateTime?)new DateTime(2024, 1, 10)
                }
            };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(testData);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            var payment = result.Value.Data.First();
            
            Assert.Equal(123L, payment.Id);
            Assert.Equal("TEST001", payment.Code);
            Assert.Equal(1500.25m, payment.Amount);
            Assert.Equal(1600.00m, payment.AmountOriginal);
            Assert.True(payment.IsAllocation);
            Assert.Equal("TRANSFER", payment.Method);
            Assert.Equal(5, payment.BranchId);
            Assert.Equal(456L, payment.PayslipId);
            Assert.Equal("Test payment description", payment.Description);
            Assert.Equal(789L, payment.EmployeeId);
            Assert.Equal(new DateTime(2024, 1, 15), payment.TransDate);
            Assert.Equal(999L, payment.UserId);
            Assert.Equal(2, payment.Status);
            Assert.Equal(10, payment.AccountId);
            Assert.Equal(new DateTime(2024, 1, 1), payment.CreatedDate);
            Assert.Equal(100L, payment.CreatedBy);
            Assert.Equal(200L, payment.ModifiedBy);
            Assert.Equal(new DateTime(2024, 1, 10), payment.ModifiedDate);
        }

        [Fact]
        public async Task ExecuteAsync_WhenMethodIsNull_ShouldUseEmptyString()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1]
            };

            var retailerId = 1;
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(retailerId);

            var testData = new[]
            {
                new
                {
                    Id = 1L,
                    Code = "PAY001",
                    Amount = 1000m,
                    AmountOriginal = 1000m,
                    IsAllocation = false,
                    Method = (string?)null,
                    BranchId = 1,
                    PayslipId = 100L,
                    Description = "Test",
                    EmployeeId = 10L,
                    TransDate = DateTime.Now,
                    UserId = (long?)5L,
                    Status = (byte)1,
                    AccountId = (int?)1,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1L,
                    ModifiedBy = (long?)null,
                    ModifiedDate = (DateTime?)null
                }
            };

            _queryServiceMock.Setup(q => q.QueryPlainTextAsync<dynamic>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(testData);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(string.Empty, result.Value.Data.First().Method);
        }
    }
} 