using KvFnB.Modules.Payment.Application.UseCases.PayslipPaymentUseCases.GetPayslipPaymentByIds;
using Xunit;

namespace KvFnB.Modules.Payment.Tests.Application.UseCases.PayslipPaymentUseCases
{
    public class GetPayslipPaymentByIdsValidatorTests
    {
        private readonly GetPayslipPaymentByIdsValidator _validator;

        public GetPayslipPaymentByIdsValidatorTests()
        {
            _validator = new GetPayslipPaymentByIdsValidator();
        }

        [Fact]
        public void Validate_WhenValidRequest_ShouldReturnValid()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, 2, 3, 4, 5]
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsIsNull_ShouldReturnInvalid()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = null!
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.Contains("cannot be null"));
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsIsEmpty_ShouldReturnInvalid()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = []
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.Contains("At least one PayslipPayment ID is required"));
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsContainsZero_ShouldReturnInvalid()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, 0, 3]
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.Contains("must be greater than zero"));
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsContainsNegativeNumber_ShouldReturnInvalid()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, -5, 3]
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.Contains("must be greater than zero"));
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsExceedsMaximum_ShouldReturnInvalid()
        {
            // Arrange
            var ids = Enumerable.Range(1, 101).Select(i => (long)i).ToList();
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = ids
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.Contains("Maximum 100 PayslipPayment IDs allowed"));
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsIs100Items_ShouldReturnValid()
        {
            // Arrange
            var ids = Enumerable.Range(1, 100).Select(i => (long)i).ToList();
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = ids
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsHasSingleValidId_ShouldReturnValid()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [999999]
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void Validate_WhenPayslipPaymentIdsHasDuplicates_ShouldReturnValid()
        {
            // Arrange - Business logic may need to handle duplicates, but validator should allow them
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, 2, 2, 3, 1]
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void Validate_WhenMultipleValidationErrorsExist_ShouldReturnAllErrors()
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = []
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.NotEmpty(result.Errors);
            // Should have error for empty list
            Assert.Contains(result.Errors, e => e.Contains("At least one PayslipPayment ID is required"));
        }

        [Theory]
        [InlineData(1)]
        [InlineData(50)]
        [InlineData(99)]
        [InlineData(100)]
        public void Validate_WhenPayslipPaymentIdsCountIsValid_ShouldReturnValid(int count)
        {
            // Arrange
            var ids = Enumerable.Range(1, count).Select(i => (long)i).ToList();
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = ids
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Theory]
        [InlineData(-1)]
        [InlineData(0)]
        public void Validate_WhenPayslipPaymentIdIsInvalid_ShouldReturnInvalid(long invalidId)
        {
            // Arrange
            var request = new GetPayslipPaymentByIdsRequest
            {
                PayslipPaymentIds = [1, invalidId, 3]
            };

            // Act
            var result = _validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.Contains("must be greater than zero"));
        }
    }
} 