using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using KvFnB.Core.Contracts;
using KvFnB.Core.Validation;
using KvFnB.Modules.Users.Application.Contracts;
using KvFnB.Modules.Users.Application.UseCases.UserNotificationUseCase.SendMessage;
using KvFnB.Shared.MultiTenancy;
using KvFnB.Shared.RabbitMq;
using KvFnB.Shared.RabbitMq.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace KvFnB.Modules.Users.Tests.Application.UseCases.UserNotificationUseCase.SendMessage
{
    public class SendMessageUserUseCaseTests
    {
        private readonly Mock<IValidator<SendMessageUserRequest>> _validatorMock;
        private readonly Mock<ILogger<SendMessageUserUseCase>> _loggerMock;
        private readonly Mock<IRabbitMessageQueueService> _rabbitServiceMock;
        private readonly Mock<ITenantProvider> _tenantProviderMock;
        private readonly Mock<IUserQueryService> _userQueryServiceMock;
        private readonly SendMessageUserUseCase _useCase;

        public SendMessageUserUseCaseTests()
        {
            _validatorMock = new Mock<IValidator<SendMessageUserRequest>>();
            _loggerMock = new Mock<ILogger<SendMessageUserUseCase>>();
            _rabbitServiceMock = new Mock<IRabbitMessageQueueService>();
            _tenantProviderMock = new Mock<ITenantProvider>();
            _userQueryServiceMock = new Mock<IUserQueryService>();
            
            _useCase = new SendMessageUserUseCase(
                _validatorMock.Object,
                _loggerMock.Object,
                _rabbitServiceMock.Object,
                _tenantProviderMock.Object,
                _userQueryServiceMock.Object);
        }

        [Fact]
        public async Task ExecuteAsync_WhenValidationFails_ShouldReturnFailure()
        {
            // Arrange
            var request = new SendMessageUserRequest
            {
                Message = new MessageDto
                {
                    UserIds = new List<long> { 1, 2, 3 },
                    Message = "Test message",
                    Payload = "Test payload"
                }
            };
            
            var validationResult = new ValidationResult(false, new List<string> { "Validation error" });
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(validationResult.Errors, result.Errors);
            
            // Verify service interactions
            _userQueryServiceMock.Verify(
                q => q.GetListByIdsAsync(It.IsAny<List<long>>(), It.IsAny<CancellationToken>()),
                Times.Never);
            
            _rabbitServiceMock.Verify(
                r => r.SendMessage(It.IsAny<List<NotificationMessage>>(), It.IsAny<CancellationToken>()),
                Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenNoUsersFound_ShouldReturnSuccessWithZeroCount()
        {
            // Arrange
            var request = new SendMessageUserRequest
            {
                Message = new MessageDto
                {
                    UserIds = new List<long> { 1, 2, 3 },
                    Message = "Test message",
                    Payload = "Test payload",
                    EventType = "TestEvent"
                }
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _userQueryServiceMock.Setup(q => q.GetListByIdsAsync(request.Message.UserIds, It.IsAny<CancellationToken>()))
                .ReturnsAsync(Enumerable.Empty<UserDto>());

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.False(result.Value.Success);
            Assert.Equal(0, result.Value.RecipientsCount);
            
            // Verify service interactions
            _rabbitServiceMock.Verify(
                r => r.SendMessage(It.IsAny<List<NotificationMessage>>(), It.IsAny<CancellationToken>()),
                Times.Never);
        }

        [Fact]
        public async Task ExecuteAsync_WhenSuccessful_ShouldSendMessagesAndReturnSuccess()
        {
            // Arrange
            var request = new SendMessageUserRequest
            {
                Message = new MessageDto
                {
                    BranchId = 5,
                    UserIds = new List<long> { 1, 2, 3 },
                    Message = "Test message",
                    Payload = "Test payload",
                    EventType = "TestEvent",
                    DocumentId = 123,
                    DocumentCode = "TEST-123"
                }
            };
            
            var users = new List<UserDto>
            {
                new UserDto { Id = 1, UserName = "user1", GivenName = "User One" },
                new UserDto { Id = 2, UserName = "user2", GivenName = "User Two" },
                new UserDto { Id = 3, UserName = "user3", GivenName = "User Three" }
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _userQueryServiceMock.Setup(q => q.GetListByIdsAsync(request.Message.UserIds, It.IsAny<CancellationToken>()))
                .ReturnsAsync(users);
                
            _tenantProviderMock.Setup(t => t.GetTenantId()).Returns(100);
            _tenantProviderMock.Setup(t => t.GetGroupId()).Returns(10);
            
            _rabbitServiceMock.Setup(r => r.SendMessage(It.IsAny<List<NotificationMessage>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.True(result.Value.Success);
            Assert.Equal(users.Count, result.Value.RecipientsCount);
            
            // Verify RabbitMQ service was called with correct parameters
            _rabbitServiceMock.Verify(
                r => r.SendMessage(
                    It.Is<List<NotificationMessage>>(list => 
                        list.Count == users.Count &&
                        list.All(m => 
                            m.Content == request.Message.Message &&
                            m.Payload == request.Message.Payload &&
                            m.EventType == request.Message.EventType &&
                            m.BranchId == request.Message.BranchId &&
                            m.DocumentId == request.Message.DocumentId &&
                            m.DocumentCode == request.Message.DocumentCode &&
                            m.RetailerId == 100 && 
                            m.Shard == 10)
                    ),
                    It.IsAny<CancellationToken>()
                ),
                Times.Once);
        }

        [Fact]
        public async Task ExecuteAsync_WhenExceptionOccurs_ShouldReturnFailure()
        {
            // Arrange
            var request = new SendMessageUserRequest
            {
                Message = new MessageDto
                {
                    UserIds = new List<long> { 1, 2, 3 },
                    Message = "Test message",
                    Payload = "Test payload",
                    EventType = "TestEvent"
                }
            };
            
            var validationResult = ValidationResult.Success();
            _validatorMock.Setup(v => v.Validate(request)).Returns(validationResult);
            _userQueryServiceMock.Setup(q => q.GetListByIdsAsync(request.Message.UserIds, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _useCase.ExecuteAsync(request);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Test exception", result.Errors.First());
            
            // Verify service interactions
            _rabbitServiceMock.Verify(
                r => r.SendMessage(It.IsAny<List<NotificationMessage>>(), It.IsAny<CancellationToken>()),
                Times.Never);
        }
    }
} 